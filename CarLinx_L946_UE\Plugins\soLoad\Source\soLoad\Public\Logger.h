#pragma once
#if PLATFORM_ANDROID
#include <android/log.h>
#define CARLINX_TAG "CarLinx_UE"

namespace LogPriority
{
    static constexpr android_LogPriority LogLevelMap[] = {
        ANDROID_LOG_INFO,     // Default
        ANDROID_LOG_FATAL,    // Fatal
        ANDROID_LOG_ERROR,    // Error
        ANDROID_LOG_WARN,     // Warning
        ANDROID_LOG_INFO,     // Display
        ANDROID_LOG_DEBUG,    // Log
        ANDROID_LOG_VERBOSE   // Verbose
    };

    static constexpr android_LogPriority GetAndroidPriority(ELogVerbosity::Type Level)
    {
        switch(Level)
        {
            case ELogVerbosity::Fatal:    return LogLevelMap[1];
            case ELogVerbosity::Error:    return LogLevelMap[2];
            case ELogVerbosity::Warning:  return LogLevelMap[3];
            case ELogVerbosity::Display:  return LogLevelMap[4];
            case ELogVerbosity::Log:      return LogLevelMap[5];
            case ELogVerbosity::Verbose:  return LogLevelMap[6];
            default:                      return LogLevelMap[0];
        }
    }
}

#define CARLINX_LOG(Category, Level, Format, ...) \
    __android_log_print(LogPriority::GetAndroidPriority(ELogVerbosity::Level), CARLINX_TAG, \
    "%s", TCHAR_TO_UTF8(*FString::Printf(TEXT("[%s][%d]" Format), ANSI_TO_TCHAR(__FUNCTION__), __LINE__, ##__VA_ARGS__)))
#else

#define CARLINX_LOG(Category, Level, Format, ...) \
    UE_LOG(Category, Level, TEXT("[%s][%d] " Format), TEXT(__FUNCTION__), __LINE__, ##__VA_ARGS__)
#endif

//DECLARE_LOG_CATEGORY_EXTERN(LogCarLinx, Log, All);

DEFINE_LOG_CATEGORY_STATIC(LogCarLinx, Log, All);
