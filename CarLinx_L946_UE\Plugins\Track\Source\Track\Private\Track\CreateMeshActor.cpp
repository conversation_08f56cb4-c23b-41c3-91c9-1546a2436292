// Fill out your copyright notice in the Description page of Project Settings.


#include "Track/CreateMeshActor.h"
#include "Kismet/KismetMathLibrary.h"

// Sets default values
ACreateMeshActor::ACreateMeshActor()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
}

// Called when the game starts or when spawned
void ACreateMeshActor::BeginPlay()
{
	Super::BeginPlay();
}

void ACreateMeshActor::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);
}

// Called every frame
void ACreateMeshActor::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}


//promesh
TArray<FVector> ACreateMeshActor::SetTrackPoint(const FVector& Point0,const FVector& Point1)
{
	TArray<FVector> Points;

	/////////计算第一对点

	//垂直X轴
	if (Point0.X - Point1.X == 0 && Point0.Y - Point1.Y <= 0)
	{
		Points.Add(FVector(Point0.X - RoundSize, Point0.Y, Point0.Z));

		Points.Add(FVector(Point0.X + RoundSize, Point0.Y, Point0.Z));

		Points.Add(FVector(Point1.X - RoundSize, Point1.Y, Point1.Z));

		Points.Add(FVector(Point1.X + RoundSize, Point1.Y, Point1.Z));

	}
	else if (Point0.X - Point1.X == 0 && Point0.Y - Point1.Y > 0)
	{
		Points.Add(FVector(Point0.X + RoundSize, Point0.Y, Point0.Z));

		Points.Add(FVector(Point0.X - RoundSize, Point0.Y, Point0.Z));

        Points.Add(FVector(Point1.X + RoundSize, Point1.Y, Point1.Z));

		Points.Add(FVector(Point1.X - RoundSize, Point1.Y, Point1.Z));	
	}
	else if (Point0.Y - Point1.Y == 0 && Point0.X - Point1.X <= 0)
	{
		Points.Add(FVector(Point0.X, Point0.Y + RoundSize, Point0.Z));

		Points.Add(FVector(Point0.X, Point0.Y - RoundSize, Point0.Z));

		Points.Add(FVector(Point1.X, Point1.Y + RoundSize, Point1.Z));

		Points.Add(FVector(Point1.X, Point1.Y - RoundSize, Point1.Z));
	}
	else if (Point0.Y - Point1.Y == 0 && Point0.X - Point1.X > 0)
	{
		Points.Add(FVector(Point0.X, Point0.Y - RoundSize, Point0.Z));

		Points.Add(FVector(Point0.X, Point0.Y + RoundSize, Point0.Z));

		Points.Add(FVector(Point1.X, Point1.Y - RoundSize, Point1.Z));

		Points.Add(FVector(Point1.X, Point1.Y + RoundSize, Point1.Z));
	}
	else
	{
		//不垂直X轴

		//当前两点的KB
		float K = (Point0.Y - Point1.Y) / (Point0.X - Point1.X);

		float B = Point0.Y - K * Point0.X;

		float BS = Point1.Y - K * Point1.X;

		//垂直的KB
		float B1 = Point0.Y + (1 / K * Point0.X);
        float BS1 = Point1.Y + (1 / K * Point1.X);

        float LeftB,LeftBS,RightB,RightBS;


		if (Point0.X < Point1.X)
		{
			//左侧的KB
			LeftB = RoundSize * FMath::Sqrt(K * K + 1) + B;
			LeftBS = RoundSize * FMath::Sqrt(K * K + 1) + BS;

			//右侧的KB
			RightB = B - RoundSize * FMath::Sqrt(K * K + 1);
			RightBS = BS - RoundSize * FMath::Sqrt(K * K + 1);
		}
		else
		{
			//右侧的KB
			RightB = RoundSize * FMath::Sqrt(K * K + 1) + B;
            RightBS = RoundSize * FMath::Sqrt(K * K + 1) + BS;


			//左侧的KB
			LeftB = B - RoundSize * FMath::Sqrt(K * K + 1);
			LeftBS = BS - RoundSize * FMath::Sqrt(K * K + 1);
		}
		Points.Add(Getintersect(-1 / K, B1, K, LeftB, Point0.Z));

		Points.Add(Getintersect(-1 / K, B1, K, RightB, Point0.Z));

		Points.Add(Getintersect(-1 / K, BS1, K, LeftBS, Point1.Z));

		Points.Add(Getintersect(-1 / K, BS1, K, RightBS, Point1.Z));
	}

	return Points;
}

FVector ACreateMeshActor::Getintersect(float K1, float B1, float K2, float B2, float Z)
{
	float X = (B1 - B2) / (K2 - K1);

	float Y = K1 * X + B1;

	return FVector(X, Y, Z);
}
