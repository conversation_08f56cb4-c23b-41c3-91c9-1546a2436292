#include "Publisher.h"
#include "CoreMinimal.h"
#include "DatasourceImpl.h"
#include "Subscriber.h"

namespace hege {
	Publisher* Publisher::sPubInst = nullptr;

	Publisher* Publisher::GetInstance() {
		if (nullptr == sPubInst) {
			sPubInst = new Publisher();
			sPubInst->mThreadRunning = 1;
		}
		return sPubInst;
	}

	Publisher::Publisher()
	: mThreadRunning(0) {
		mSubMap.clear();
	}

	Publisher::~Publisher() {
		DelAllSubs();
	}

	bool Publisher::RegisterSub(FString& devName, ASubscriber* subObj) {
		UE_LOG(LogTemp, Log, TEXT("RegisterSub devName: %s"), *devName);
		bool rst = false;
		FString name = TCHAR_TO_UTF8(*devName);
		mSubMap[name] = subObj;
		rst = true;
		mThreadRunning = 1;
		return rst;
	}

	bool Publisher::DelSub(FString& devName) {
		bool rst = false;
		auto it = mSubMap.find(devName);
		if (it != mSubMap.end()) {
			mSubMap.erase(it);
			rst = true;
		}
		return rst;
	}

	bool Publisher::DelAllSubs() {
		mThreadRunning = 0;
		mSubMap.clear();
		mStoreMapInt.clear();
		mStoreMapFloat.clear();
		mStoreMapTarryFloat.clear();
		mStoreMapTarryVector.clear();
		mStoreMapTMapVector.clear();
		mStoreMapStr.clear();
		return true;
	}

	void Publisher::PubMsg(std::string topic, int value) {
		/*if (0 == mThreadRunning) {
			return;
		}
		auto findValueIt = mStoreMapInt.find(topic);
		bool update = true;
		if (findValueIt == mStoreMapInt.end()) {
			update = true;
		} else if (findValueIt->second != value) {
			update = true;
		}
		mStoreMapInt[topic] = value;
		if (update) {
			for (auto it = mSubMap.begin(); it != mSubMap.end(); it++) {
				FString Ftopic = UTF8_TO_TCHAR(topic.c_str());
				UE_LOG(LogTemp, Log, TEXT("PubMsg devName: %s, Value Int: %d"), *Ftopic, value);
				for (auto item : it->second->mTopicVec) {
					if (!topic.compare(TCHAR_TO_UTF8(*item))) {
						it->second->IntValueUpdateEvent(UTF8_TO_TCHAR(topic.c_str()), value);
						break;
					}
				}
			}
		}*/

		/************************此处为测试代码*************************************************/

		if (mThreadRunning == 0) {
			return;
		}

		// 查找并判断是否需要更新
		auto findValueIt = mStoreMapInt.find(topic);
		//bool update = (findValueIt != mStoreMapInt.end()&&findValueIt->second == value);
		bool update = true;
		if (update) {
			// 更新mStoreMapInt中的值
			mStoreMapInt[topic] = value;

			// 将UTF8主题转为TCHAR，避免重复转换
			FString Ftopic = UTF8_TO_TCHAR(topic.c_str());

			// 遍历所有订阅者
			for (auto it = mSubMap.begin(); it != mSubMap.end(); ++it) {
				// 输出更新的消息，只转换一次
				UE_LOG(LogTemp, Log, TEXT("PubMsg devName: %s, Value Int: %d"), *Ftopic, value);

				// 遍历订阅者的主题列表，查看是否包含当前topic
				for (auto& item : it->second->mTopicVec) {
					// 直接比较UTF8字符串
					if (topic == TCHAR_TO_UTF8(*item)) {
						// 如果找到匹配的主题，调用更新事件
						it->second->IntValueUpdateEvent(*item, value);
						break;
					}
				}
			}
		}

	}

	void Publisher::PubMsg(std::string topic, float value) {
		if (0 == mThreadRunning) {
			return;
		}
		auto findValueIt = mStoreMapFloat.find(topic);
		bool update = true;
		if (findValueIt == mStoreMapFloat.end()) {
			update = true;
		}
		else if (abs(findValueIt->second - value) >= 0.00001) {
			update = true;
		}
		mStoreMapFloat[topic] = value;
		if (update) {
			for (auto it = mSubMap.begin(); it != mSubMap.end(); it++) {
				FString Ftopic = UTF8_TO_TCHAR(topic.c_str());
				UE_LOG(LogTemp, Log, TEXT("PubMsg devName: %s, Value Float: %.2f"), *Ftopic, value);
				mStoreMapFloat.emplace(topic, value);
				for (auto item : it->second->mTopicVec) {
					if (!topic.compare(TCHAR_TO_UTF8(*item))) {
						it->second->FloatValueUpdateEvent(UTF8_TO_TCHAR(topic.c_str()), value);
						break;
					}
				}
			}
		}
	}

	void Publisher::PubMsg(std::string topic, std::string value) {
		if (0 == mThreadRunning) {
			return;
		}
		auto findValueIt = mStoreMapStr.find(topic);
		bool update = true;
		if (findValueIt == mStoreMapStr.end()) {
			update = true;
		}
		else if (!findValueIt->second.compare(value)) {
			update = true;
		}
		mStoreMapStr[topic] = value;
		if (update) {
			for (auto it = mSubMap.begin(); it != mSubMap.end(); it++) {
				FString Ftopic = UTF8_TO_TCHAR(topic.c_str());
				FString Fvalue = UTF8_TO_TCHAR(value.c_str());
				UE_LOG(LogTemp, Log, TEXT("PubMsg devName: %s, Value String: %s"), *Ftopic, *Fvalue);
				mStoreMapStr.emplace(topic, value);
				for (auto item : it->second->mTopicVec) {
					if (!topic.compare(TCHAR_TO_UTF8(*item))) {
						it->second->FStringValueUpdate(UTF8_TO_TCHAR(topic.c_str()), UTF8_TO_TCHAR(value.c_str()));
						break;
					}
				}
			}
		}
	}

	void Publisher::PubMsg(std::string topic, TArray<float> value)
	{
		if (0 == mThreadRunning) {
			return;
		}
		auto findValueIt = mStoreMapTarryFloat.find(topic);
		bool update = true;
		if (findValueIt == mStoreMapTarryFloat.end()) {
			update = true;
		}
		else if (findValueIt->second != value) {
			update = true;
		}
		mStoreMapTarryFloat[topic] = value;
		if (update) {
			for (auto it = mSubMap.begin(); it != mSubMap.end(); it++) {
				FString Ftopic = UTF8_TO_TCHAR(topic.c_str());
				mStoreMapTarryFloat.emplace(topic, value);
				for (auto item : it->second->mTopicVec) {
					if (!topic.compare(TCHAR_TO_UTF8(*item))) {
						it->second->TarryFloatValueUpdateEvent(UTF8_TO_TCHAR(topic.c_str()), value);
						break;
					}
				}
			}
		}
	}

	void Publisher::PubMsg(std::string topic, TArray<FVector> value)
	{
		if (0 == mThreadRunning) {
			return;
		}
		auto findValueIt = mStoreMapTarryVector.find(topic);
		bool update = true;
		if (findValueIt == mStoreMapTarryVector.end()) {
			update = true;
		}
		else if (findValueIt->second != value) {
			update = true;
		}
		mStoreMapTarryVector[topic] = value;
		if (update) {
			for (auto it = mSubMap.begin(); it != mSubMap.end(); it++) {
				FString Ftopic = UTF8_TO_TCHAR(topic.c_str());
				mStoreMapTarryVector.emplace(topic, value);
				for (auto item : it->second->mTopicVec) {
					if (!topic.compare(TCHAR_TO_UTF8(*item))) {
						it->second->TarryVectorValueUpdateEvent(UTF8_TO_TCHAR(topic.c_str()), value);
						break;
					}
				}
			}
		}
	}

	void Publisher::PubMsg(std::string topic, TMap<int, FVector> value, TArray<float> RotationValue, TArray<int> Type)
	{
		if (0 == mThreadRunning) {
			return;
		}
		// auto findValueIt = mStoreMapTMapVector.find(topic);
		//当update 为 false时候，相同信号值不能触发，将上边注释去掉；为 True 时，不判断相同信号值。
		// bool update = false;
		// if (findValueIt == mStoreMapTMapVector.end()) {
		// 	update = true;
		// }
		// else if (findValueIt->second != value) {
		// 	update = true;
		// }
		bool update = true;
		mStoreMapTMapVector[topic] = value;
		mStoreRotationValue[topic] = RotationValue;
		mStoreTypeValue[topic] = Type;
		if (update) {
			for (auto it = mSubMap.begin(); it != mSubMap.end(); it++) {
				FString Ftopic = UTF8_TO_TCHAR(topic.c_str());
				mStoreMapTMapVector.emplace(topic, value);
				mStoreRotationValue.emplace(topic, RotationValue);
				mStoreTypeValue.emplace(topic, Type);
				for (auto item : it->second->mTopicVec) {
					if (!topic.compare(TCHAR_TO_UTF8(*item))) {
						it->second->TMapVectorValueUpdateEvent(UTF8_TO_TCHAR(topic.c_str()), value, RotationValue, Type);
						break;
					}
				}
			}
		}
	}
}
