﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ParkingDataProcessor2UE.h"

#include "HistoryParkingSlotManager.generated.h"

/**
 * 
 */

enum class ECarSlot : uint8;
class AHistoryParkingLotBase;
struct FParkingSpace;

UENUM(BlueprintType)
enum class EParkingOrientation : uint8
{
	Horizontal,
	Vertical
};

UCLASS()
class CARLINX_L946_UE_API AHistoryParkingSlotManager : public AActor
{
	GENERATED_BODY()
	
public:

	void BeginPlay() override;

	//清理历史车位
	UFUNCTION(BlueprintCallable, Category = "Parking System")
	void ResetAllHistoryParkingSlot();

	//相机委托开始事件
	UFUNCTION(BlueprintCallable, Category = "Parking System")
	void CameraBeginEvent();

protected:

	//获取HPA Touch位置
	UFUNCTION(BlueprintImplementableEvent, Category = "Parking System")
	void GetHpaTouchLocation(FVector& Location, FVector& ForwardVector);

	//水平车位类
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = "ParkingClassType")
	TSubclassOf<AHistoryParkingLotBase> HorizontalParkingSlotClass;

	//竖向车位类
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = "ParkingClassType")
	TSubclassOf<AHistoryParkingLotBase> VerticalParkingSlotClass;

private:

	//车位动态管理
	void DynamicControlParkingSlot(int32 ID, int32 MeshType, int32 Number, int32 Status, const FVector& Location, float Rotation, ECarSlot CarSlot, float Width);

	// 添加历史车位（蓝图可调用）
	void AddHistorySpot(int32 InID, AHistoryParkingLotBase* Spot, EParkingOrientation Orientation);

	// 移除历史车位（蓝图可调用）
	void RemoveHistorySpot(int32 InID, EParkingOrientation Orientation);

private:
	
	// 横向车位容器
	UPROPERTY()
	TMap<int32, AHistoryParkingLotBase*> HorizontalSpots;

	// 竖向车位容器
	UPROPERTY()
	TMap<int32, AHistoryParkingLotBase*> VerticalSpots;

	//所有历史车位数据
	TArray<FParkingSlotData>AllParkingSlotData;

	//World指针
	UWorld* CurrentWorld = nullptr;
};
