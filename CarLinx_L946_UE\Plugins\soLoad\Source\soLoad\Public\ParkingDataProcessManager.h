#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "ParkingDataManager.h"
#include "DataTypes.h"
#include "ParkingDataProcessManager.generated.h"

// 声明委托
DECLARE_MULTICAST_DELEGATE(FOnSelfCarDataProcessedDelegate);
DECLARE_MULTICAST_DELEGATE_OneParam(FOnSlotDataProcessedDelegate, int32);
DECLARE_MULTICAST_DELEGATE_OneParam(FOnObstacleDataProcessedDelegate, int32);
DECLARE_MULTICAST_DELEGATE_OneParam(FOnTrafficSignDataProcessedDelegate, int32);
DECLARE_MULTICAST_DELEGATE_OneParam(FOnRoadRightPointDataProcessedDelegate, int32);
DECLARE_MULTICAST_DELEGATE_OneParam(FOnRoadLeftPointDataProcessedDelegate, int32);
DECLARE_MULTICAST_DELEGATE(FOnPerspectiveModeProcessedDelegate);
DECLARE_MULTICAST_DELEGATE(FOnTrackPlanningProcessedDelegate);
DECLARE_MULTICAST_DELEGATE(FOnLeftPointsProcessedDelegate);
DECLARE_MULTICAST_DELEGATE(FOnRoadPointDataUpdated);

UCLASS()
class SOLOAD_API UParkingDataProcessManager : public UObject
{
    GENERATED_BODY()

public:
    static UParkingDataProcessManager* GetInstance();

    // 处理接收到的数据
    void ProcessReceivedData(const std::vector<uint8_t>& Data);

    // 获取处理后的数据
    TMap<FString, FString> GetSelfCarData() const;
    TMap<FString, FString> GetSlotData(int32 SlotNumber) const;
    TMap<FString, FString> GetObstacleData(int32 ObstacleNumber) const;
    TMap<FString, FString> GetTrafficSignData(int32 SignNumber) const;
    TMap<FString, FString> GetRoadRightPointData(int32 PointNumber) const;
    TMap<FString, FString> GetRoadLeftPointData(int32 PointNumber) const;
    TMap<FString, FString> GetPerspectiveModeData() const;
    TArray<FSTCTRACK> GetTrackPlanningPoints() const;
    TArray<FRoadPoint> GetHPARoadPoints() const;
    void ResetHPARoadPoints();
    int GetLineType() const;


    // 数据处理完成的委托
    FOnSelfCarDataProcessedDelegate OnSelfCarDataProcessed;
    FOnSlotDataProcessedDelegate OnSlotDataProcessed;
    FOnObstacleDataProcessedDelegate OnObstacleDataProcessed;
    FOnTrafficSignDataProcessedDelegate OnTrafficSignDataProcessed;
    FOnRoadRightPointDataProcessedDelegate OnRoadRightPointDataProcessed;
    FOnRoadLeftPointDataProcessedDelegate OnRoadLeftPointDataProcessed;
    FOnPerspectiveModeProcessedDelegate OnPerspectiveModeProcessed;
    FOnTrackPlanningProcessedDelegate OnTrackPlanningProcessed;
    FOnLeftPointsProcessedDelegate OnLeftPointsProcessed;
    FOnRoadPointDataUpdated OnRoadPointDataUpdated;

private:
    static UParkingDataProcessManager* Instance;

    // 存储处理后的数据
    mutable FCriticalSection DataLock;
    TMap<FString, FString> SelfCarData;
    TMap<FString, FString> SlotData[32];
    TMap<FString, FString> ObstacleData[60];
    TMap<FString, FString> TrafficSignData[10];
    TMap<FString, FString> RoadRightPointData[100];
    TMap<FString, FString> RoadLeftPointData[100];
    TMap<FString, FString> PerspectiveModeData;
    TArray<FSTCTRACK> TrackPlanningPoints;
    int LineType;
    TArray<FRoadPoint> HPARoadPoints;

    // 处理各类数据的内部函数
    void ProcessSelfCarData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& Data);
    void ProcessSlotData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot& Data, int32 Index);
    void ProcessObstacleData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle& Data, int32 Index);
    void ProcessTrafficSignData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgTrafficsign& Data, int32 Index);
    void ProcessRoadPointData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint& Data, int32 Index, bool bIsRight);
    void ProcessPerspectiveMode(uint8_t Mode);
    void ProcessTrackPlanningData(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& left, 
        const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& right, 
        const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& selfData);
    void DeserializeHpaRoadPoint(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& Left, 
        const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& Right);
}; 


// 蓝图可调用的类
UCLASS(BlueprintType)
class SOLOAD_API UParkingDataProcessManager2UE : public UObject
{
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintCallable, Category = "ParkingDataProcessor")
    static bool GetProcessedRangeInfo(FVector& OutCenter, float& OutDistance);
    
private:
    
};