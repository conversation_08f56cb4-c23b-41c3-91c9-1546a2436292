// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Engine/DataTable.h"
#include "Containers/Map.h"
#include "ToolFunctionLibrary.generated.h"

/**
 *
 */



UENUM(BlueprintType)
enum class ECarSlot : uint8
{
	LeftSlot,
	RightSlot,
	ForwardSlot,
	BehindSlot,
	Unkonw

};

USTRUCT(BlueprintType)
struct FParkingSpace :public FTableRowBase
{
	GENERATED_USTRUCT_BODY()

public:

	FParkingSpace() {};

	// FParkingSpace(TMap<FString, int32>& Map,int32 SlotNumber ) {};
	 // 结构体包含的成员
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		FVector CenterPosition;//中心点
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 ID;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 status;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		float Angle;//角度基于X轴为正方向计算的
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 Number;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 Lenth;//CA向量是否为长边(暂时舍弃)
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 Type;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 Index;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 hasWheelBlock;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 wheelBlockFront;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 wheelBlockRear;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 hasGroundLock;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		int32 groundLockStatus;
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		float Length; // 停车位长度
	UPROPERTY(BlueprintReadWrite, Category = "MyStruct")
		float Width; // 停车位宽度

};

USTRUCT(BlueprintType)
struct FDatajson :public FTableRowBase
{
	GENERATED_USTRUCT_BODY()

public:

	FDatajson() {};
	UPROPERTY(BlueprintReadWrite, Category = "DataJson")
		FString ID;
	UPROPERTY(BlueprintReadWrite, Category = "DataJson")
		FString Time;
	UPROPERTY(BlueprintReadWrite, Category = "DataJson")
		FString Name;
	UPROPERTY(BlueprintReadWrite, Category = "DataJson")
		FString XunhangTime;
};


UCLASS()
class CARLINX_L946_UE_API UToolFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()


public:

	UFUNCTION(BlueprintCallable, Category = "ToolLib")
		static FParkingSpace ReadParkingData(const TMap<FString, FString>& Map, const int32 SlotNumber);

	UFUNCTION(BlueprintCallable, Category = "ToolLib")
		static FString getMapValue(const TMap<FString, FString>& Map, const FString& key);

	UFUNCTION(BlueprintCallable, Category = "ToolLib")
		static FVector CalculateCenterPoint(const FVector A, const FVector B, const FVector C, const FVector D);


	UFUNCTION(BlueprintCallable, Category = "ToolLib")
		static float CalculateAngleWithXAxis360(const FVector A, const FVector B, const FVector C, const FVector D, const  FVector CenterPoint);


	UFUNCTION(BlueprintCallable, Category = "ToolLib")
		static int32 CAtrueOrFalse(const FVector A, const FVector B, const FVector C, const FVector D);

	UFUNCTION(BlueprintCallable, Category = "ToolLib")
		static void CalculateParkingSpaceDimensions(const FVector A, const FVector B, const FVector C, const FVector D, float& OutLength, float& OutWidth);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "ToolLib")
		static float AngleConversion(const float& Value);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "ToolLib")
		static int32 ParkingLotTypeManager(const int32& Value);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "ToolLib")
		static bool LeftOrRight(const FVector& MyLocation, const FVector& Forward, const FVector& TargetPoint);
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "ToolLib")
		static ECarSlot SlotType(const FVector& MyLocation, const FVector& Forward, const FVector& TargetPoint);


	UFUNCTION(BlueprintCallable, Category = "JSON|Utilities")
		static bool SaveVectorArrayToJsonWithTimestamp(const TArray<FVector>& VectorArray, const FString& SaveDirectory, bool bIsAbsolutePath = false);

	UFUNCTION(BlueprintCallable, Category = "FlieTexture")
		static	void CreateDrivingSnapshotData();
	UFUNCTION(BlueprintCallable, Category = "FlieTexture")
		static	bool SaveTextureToPNG(UTexture2D* Texture, const FString& FileName);

	UFUNCTION(BlueprintCallable, Category = "FlieTexture")
		static bool SaveRenderTargetToPNG(UTextureRenderTarget2D* RenderTarget, const FString& FullFilePath);

	/**
 * 根据输入值 n 返回一个 32 位的无符号整数，其中第 n 位为 1
 * @param n 要设置为 1 的位索引（范围 0-31）
 * @return 返回一个 32 位的整数，只有第 n 位为 1
 */
	UFUNCTION(BlueprintCallable, Category = "Bit Manipulation")
		static int32 SetBitAtPosition(int32 n);

	UFUNCTION(BlueprintCallable, Category = "Bit Manipulation")
		static FString SetBitAtPositionAsString(int32 n);

	UFUNCTION(BlueprintCallable, Category = "Bit Manipulation")
		static void GetBitByID(int32 ID, int32& Result);
	//json反序列化
	UFUNCTION(BlueprintCallable, Category = "Json To MapList")
		static	TArray<int32> ConvertJsonListsToIntArray(const FString& JsonFilePath);



	UFUNCTION(BlueprintCallable, Category = "Bit Manipulation")
		static	int32 DetermineListAndIndex(int32 InputValue);


	UFUNCTION(BlueprintCallable, Category = "Bit Manipulation")
		static	int32 DetermineListIndex(int32 InputValue);

	UFUNCTION(BlueprintCallable, Category = "Bit Manipulation")
		static	TArray<int32> Int32ToBinaryArray(int32 Value);

	/**
* 从整数的二进制中找出为1的Bit位，修改对应JSON文件的列表
* @param JsonFilePath JSON文件的路径
* @param ListName 要操作的列表名称
* @param InputValue 输入的整数值
* @return 是否成功完成操作
*/
	UFUNCTION(BlueprintCallable, Category = "Utility|JSON")
		static bool ModifyJsonBasedOnBitIndices(const FString& JsonFilePath, const FString& ListName, int32 InputValue);



	/**
 * 根据输入的整数和列表名称计算 MapID，并返回所有符合条件的 MapID
 * @param InputValue 输入的 int32 整数
 * @param ListName 列表名称 (1 = 0-31, 2 = 32-63, etc.)
 * @param OutMapIDs 输出所有符合条件的 MapID 数组
 * @return 是否成功计算出 MapID
 */
	UFUNCTION(BlueprintCallable, Category = "Utility|Bit")
		static bool CalculateMapIDsFromBitIndices(int32 InputValue, int32 ListName, TArray<int32>& OutMapIDs);

	/**
	 * 根据给定的 MapID 删除对应的文件夹
	 * @param MapID 计算出的 MapID
	 * @param ProjectPath 项目路径
	 * @return 是否成功删除文件夹
	 */
	UFUNCTION(BlueprintCallable, Category = "Utility|File")
		static bool DeleteFolderByMapID(int32 MapID, const FString& ProjectPath);




	/** 读取 JSON 文件并解析为 128 个元素的数组 */
	UFUNCTION(BlueprintCallable, Category = "Utility|File")
		static bool ReadJsonFile(const FString& FilePath, TArray<int32>& OutData);



	/** 返回第一个 0 的索引（从索引 0 开始） */
	UFUNCTION(BlueprintCallable, Category = "JSON Parsing")
		static int32 GetFirstZeroIndexFromStart();

	/** 返回第一个 0 的索引（从索引 50 开始） */
	UFUNCTION(BlueprintCallable, Category = "JSON Parsing")
		static int32 GetFirstZeroIndexFromIndex50();

	//去掉末尾数字的
	UFUNCTION(BlueprintCallable, Category = "StringUtils")
		static FString RemoveTrailingNumbers(const FString& InputString);

	//读取文件.png路径
	UFUNCTION(BlueprintCallable, Category = "FileUtils")
		static FString FindFirstPNGFile(const FString& FolderPath);
	//添加新的字段 巡航时间
	UFUNCTION(BlueprintCallable, Category = "FileUtils")
	static bool SaveTimesToJsonFile(const FString& FathFlie, const FString& Times);
   //读取巡航时间
	UFUNCTION(BlueprintCallable, Category = "FileUtils")
	static bool LoadTimesFromJsonFile(const FString& FathFlie, FString& OutTimes);


	UFUNCTION(BlueprintCallable, Category = "FileUtils")
		static void CalculateCenterAndDiameter(const TArray<FVector>& Points, FVector& OutCenter, float& OutDiameter);

		// 静态函数：将秒数转换为分钟数
		UFUNCTION(BlueprintCallable, Category = "Time Conversion")
		static FString ConvertSecondsToMinutes(int32 Seconds);

	/**
 * 计算能够完整拍摄道路的最佳相机位置和正交宽度
 * @param Points - 道路点数组（通过蓝图传入）
 * @param ZOffset - 相机在高度方向的偏移量（防止近裁剪面剪切）
 * @param OutOrthoWidth - （输出）计算得到的正交宽度值
 * @return 最佳相机位置（FVector）
 */
	UFUNCTION(BlueprintCallable, Category = "Camera Calculation",
		meta = (Keywords = "Calculate Camera Position"))
		static FVector CalculateCapturePosition(
			const TArray<FVector>& Points,
			float ZOffset,
			float& OutOrthoWidth);


	/***
	*Camera信息保存
	* 
	{
    "Length": 120.5,
    "Location": {
        "X": 10.0,
        "Y": 20.0,
        "Z": 30.0
    }
}
	*/

	UFUNCTION(BlueprintCallable, Category = "Camera Calculation")
		static bool SaveCameraData(const FString& Path, float Length, const FVector& Loc);

	UFUNCTION(BlueprintCallable, Category = "Camera Calculation")
	static bool LoadCameraData(const FString& Path, float& OutLength, FVector& OutLoc);
	/***
	*
	*
	*地图管理页的全选删除
	* 
	*
	***/
	
	UFUNCTION(BlueprintCallable, Category = "BitSet")
		static void SetBitsDeleteMaps(const TArray<int32>& InputNumbers, TArray<int32>& OutputArray);
	UFUNCTION(BlueprintCallable, Category = "SpringArmLength")
		static float CalculateSpringArmLength(float MaxDistance, float VerticalFOV, float AspectRatio);
	UFUNCTION(BlueprintCallable, Category = "SpringArmLength")
		static float CalculateTargetArmLength( FVector MapMin,FVector MapMax,FVector CenterPos,FRotator SpringArmRotation,float FOV,float ScreenAspectRatio,float ArmLengthOffest=1);
	UFUNCTION(BlueprintCallable, Category = "SpringArmLength")
		static float CalculateTargetArmLengthByDis(float MaxDistance,FVector CenterPos,FTransform SpringArmLocalTransform,FTransform ParentWorldTransform,float FOV,float ScreenRatio,float ArmLengthOffest=1);
};
