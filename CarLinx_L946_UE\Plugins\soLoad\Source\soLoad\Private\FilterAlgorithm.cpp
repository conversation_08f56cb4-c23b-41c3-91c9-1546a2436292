#include "FilterAlgorithm.h"
#include <cmath>

using namespace gossoa::ap::services;

// 静态成员初始化
std::map<uint16_t, FilterAlgorithm::FilterData> FilterAlgorithm::filterDataMap;
std::vector<float> FilterAlgorithm::gaussianKernel;

// 滤波参数初始化
int FilterAlgorithm::movingAverageWindowSize = 10;
float FilterAlgorithm::lowPassAlpha = 0.2f;
float FilterAlgorithm::gaussianSigma = 1.0f;
int FilterAlgorithm::gaussianKernelSize = 5;

// 参数配置接口实现
void FilterAlgorithm::SetMovingAverageWindowSize(int windowSize) {
    if (windowSize > 1) {
        movingAverageWindowSize = windowSize;
    }
}

void FilterAlgorithm::SetLowPassAlpha(float alpha) {
    if (alpha >= 0.0f && alpha <= 1.0f) {
        lowPassAlpha = alpha;
    }
}

void FilterAlgorithm::SetGaussianParameters(float sigma, int kernelSize) {
    if (sigma > 0.0f && kernelSize > 0 && (kernelSize % 2 == 1)) {
        gaussianSigma = sigma;
        gaussianKernelSize = kernelSize;
    }
}

// 统一滤波接口实现
VehValtPrkgHmiGenr::PrkgObstacle FilterAlgorithm::Filter(
    const VehValtPrkgHmiGenr::PrkgObstacle& input,
    FilterType type
) {
    switch (type) {
        case FilterType::MovingAverage:
            {
                if (input.objTyp__ == 1) {
                    // 车
                    return MovingAverageFilter(input, movingAverageWindowSize * 1);
                } else if (input.objTyp__ == 10) {
                    // 柱子
                    return MovingAverageFilter(input, movingAverageWindowSize * 10);
                } else if (input.objTyp__ == 15) {
                    // 锥桶
                    return MovingAverageFilter(input, movingAverageWindowSize * 10);
                } else {
                    return MovingAverageFilter(input, movingAverageWindowSize * 1);
                }
            }
        case FilterType::LowPass:
            return LowPassFilter(input, lowPassAlpha);
        case FilterType::Gaussian:
            return GaussianFilter(input, gaussianSigma, gaussianKernelSize);
        default:
            return input;
    }
}

// 更新滤波数据
void FilterAlgorithm::UpdateFilterData(const VehValtPrkgHmiGenr::PrkgObstacle& input, uint16_t objId) {
    auto& filterData = filterDataMap[objId];
    filterData.lastUpdateTime = std::chrono::system_clock::now();
    filterData.lastValue = input;
}

// 清除所有缓存数据
void FilterAlgorithm::ClearAllData() {
    filterDataMap.clear();
    gaussianKernel.clear();
}

// 清理过期数据
void FilterAlgorithm::ClearStaleData(float timeThresholdSeconds) {
    auto now = std::chrono::system_clock::now();
    auto it = filterDataMap.begin();
    while (it != filterDataMap.end()) {
        auto timeDiff = std::chrono::duration_cast<std::chrono::seconds>(now - it->second.lastUpdateTime).count();
        if (timeDiff > timeThresholdSeconds) {
            it = filterDataMap.erase(it);
        } else {
            ++it;
        }
    }
}

// 移动平均滤波实现
VehValtPrkgHmiGenr::PrkgObstacle FilterAlgorithm::MovingAverageFilter(
    const VehValtPrkgHmiGenr::PrkgObstacle& input,
    int windowSize) {
    
    uint16_t objId = input.objID__;
    auto& filterData = filterDataMap[objId];
    
    // 如果缓存为空，用当前数据填满缓冲区并初始化和
    if (filterData.buffer.empty()) {
        filterData.sumX = input.objPosn__.posnX__ * windowSize;
        filterData.sumY = input.objPosn__.posnY__ * windowSize;
        filterData.sumZ = input.objPosn__.posnZ__ * windowSize;
        
        for (int i = 0; i < windowSize; ++i) {
            filterData.buffer.push_back(input);
        }
    } else {
        // 更新缓存和滑动窗口的和
        if (filterData.buffer.size() >= static_cast<size_t>(windowSize)) {
            // 减去最旧的数据
            const auto& oldest = filterData.buffer.front();
            filterData.sumX -= oldest.objPosn__.posnX__;
            filterData.sumY -= oldest.objPosn__.posnY__;
            filterData.sumZ -= oldest.objPosn__.posnZ__;
            filterData.buffer.pop_front();
        }
        
        // 加上新数据
        filterData.buffer.push_back(input);
        filterData.sumX += input.objPosn__.posnX__;
        filterData.sumY += input.objPosn__.posnY__;
        filterData.sumZ += input.objPosn__.posnZ__;
    }

    VehValtPrkgHmiGenr::PrkgObstacle result = input;
    
    // 使用维护的和计算平均值
    size_t count = filterData.buffer.size();
    result.objPosn__.posnX__ = static_cast<int16_t>(filterData.sumX / count);
    result.objPosn__.posnY__ = static_cast<int16_t>(filterData.sumY / count);
    result.objPosn__.posnZ__ = static_cast<int8_t>(filterData.sumZ / count);

    UpdateFilterData(result, objId);
    return result;
}

// 低通滤波实现
VehValtPrkgHmiGenr::PrkgObstacle FilterAlgorithm::LowPassFilter(
    const VehValtPrkgHmiGenr::PrkgObstacle& input,
    float alpha
) {
    uint16_t objId = input.objID__;
    auto& filterData = filterDataMap[objId];
    
    VehValtPrkgHmiGenr::PrkgObstacle result = input;

    // 第一次处理该ID的数据
    if (filterDataMap.find(objId) == filterDataMap.end()) {
        UpdateFilterData(result, objId);
        return result;
    }

    // 应用低通滤波
    const auto& last = filterData.lastValue;
    result.objPosn__.posnX__ = static_cast<int16_t>(alpha * input.objPosn__.posnX__ + (1 - alpha) * last.objPosn__.posnX__);
    result.objPosn__.posnY__ = static_cast<int16_t>(alpha * input.objPosn__.posnY__ + (1 - alpha) * last.objPosn__.posnY__);
    result.objPosn__.posnZ__ = static_cast<int8_t>(alpha * input.objPosn__.posnZ__ + (1 - alpha) * last.objPosn__.posnZ__);
    
    result.objHeading__.roll__ = static_cast<uint16_t>(alpha * input.objHeading__.roll__ + (1 - alpha) * last.objHeading__.roll__);
    result.objHeading__.pitch__ = static_cast<uint16_t>(alpha * input.objHeading__.pitch__ + (1 - alpha) * last.objHeading__.pitch__);
    result.objHeading__.yaw__ = static_cast<uint16_t>(alpha * input.objHeading__.yaw__ + (1 - alpha) * last.objHeading__.yaw__);
    
    result.objSpd__ = static_cast<int16_t>(alpha * input.objSpd__ + (1 - alpha) * last.objSpd__);

    UpdateFilterData(result, objId);
    return result;
}

// 创建高斯核函数
std::vector<float> FilterAlgorithm::CreateGaussianKernel(float sigma, int size) {
    std::vector<float> kernel(size);
    float sum = 0.0f;
    int center = size / 2;

    for (int i = 0; i < size; ++i) {
        float x = static_cast<float>(i - center);
        kernel[i] = std::exp(-(x * x) / (2 * sigma * sigma));
        sum += kernel[i];
    }

    // 归一化
    for (float& value : kernel) {
        value /= sum;
    }

    return kernel;
}

// 高斯滤波实现
VehValtPrkgHmiGenr::PrkgObstacle FilterAlgorithm::GaussianFilter(
    const VehValtPrkgHmiGenr::PrkgObstacle& input,
    float sigma,
    int kernelSize
) {
    uint16_t objId = input.objID__;
    auto& filterData = filterDataMap[objId];
    
    // 更新高斯核
    gaussianKernel = CreateGaussianKernel(sigma, kernelSize);
    
    // 更新缓存
    filterData.buffer.push_back(input);
    if (filterData.buffer.size() > static_cast<size_t>(kernelSize)) {
        filterData.buffer.pop_front();
    }

    VehValtPrkgHmiGenr::PrkgObstacle result = input;
    
    if (filterData.buffer.size() < static_cast<size_t>(kernelSize)) {
        UpdateFilterData(result, objId);
        return result;
    }

    // 应用高斯滤波
    float sumX = 0, sumY = 0, sumZ = 0;
    float sumRoll = 0, sumPitch = 0, sumYaw = 0;
    float sumSpd = 0;

    for (size_t i = 0; i < filterData.buffer.size(); ++i) {
        float weight = gaussianKernel[i];
        const auto& obs = filterData.buffer[i];
        
        sumX += obs.objPosn__.posnX__ * weight;
        sumY += obs.objPosn__.posnY__ * weight;
        sumZ += obs.objPosn__.posnZ__ * weight;
        sumRoll += obs.objHeading__.roll__ * weight;
        sumPitch += obs.objHeading__.pitch__ * weight;
        sumYaw += obs.objHeading__.yaw__ * weight;
        sumSpd += obs.objSpd__ * weight;
    }

    result.objPosn__.posnX__ = static_cast<int16_t>(sumX);
    result.objPosn__.posnY__ = static_cast<int16_t>(sumY);
    result.objPosn__.posnZ__ = static_cast<int8_t>(sumZ);
    result.objHeading__.roll__ = static_cast<uint16_t>(sumRoll);
    result.objHeading__.pitch__ = static_cast<uint16_t>(sumPitch);
    result.objHeading__.yaw__ = static_cast<uint16_t>(sumYaw);
    result.objSpd__ = static_cast<int16_t>(sumSpd);

    UpdateFilterData(result, objId);
    return result;
}