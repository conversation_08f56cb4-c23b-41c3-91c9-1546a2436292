#include "AndroidDataFeeder.h"
#include "DatasourceImpl.h"
#if PLATFORM_ANDROID
#include "Android/AndroidApplication.h"
#include "Launch/Public/Android/AndroidJNI.h"
#endif

static UDatasourceImpl* sDatasource = nullptr;
#if PLATFORM_ANDROID
static JavaVM* sJavaVm;
static jobject sCallbackObj;
#endif

void registerDatasource(UDatasourceImpl* datasource) {
#if PLATFORM_ANDROID
    ALOGD("!!! registerLinkerActor s_linkerActor set begin !!!");
#endif
    if (nullptr == sDatasource) {
        sDatasource = datasource;
#if PLATFORM_ANDROID
        ALOGD("!!! registerLinkerActor s_linkerActor set Success !!!");
#endif
    }
    else {
        sDatasource = datasource;
    }
}

void PrintLog(std::string str) {
#if PLATFORM_ANDROID
    ALOGD("!!! PrintLog: %s", str.c_str());
#endif
}

#if PLATFORM_ANDROID
JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetStringValue(JNIEnv* env, jclass clazz, jstring name, jstring value) {
    // TODO: implement focusEvent()
    if (nullptr == env || nullptr == sDatasource) {
        ALOGD("!!! NotifyStringValue Error!!! env or actor is NULL");
        return;
    }

    const char* nameStr = env->GetStringUTFChars(name, NULL);
    const char* valueStr = env->GetStringUTFChars(value, NULL);
    ALOGD("!!!!!!!!!!!!!!! set %s Value %s", nameStr, valueStr);

    FString nameString(UTF8_TO_TCHAR(nameStr));
    FString valueString(UTF8_TO_TCHAR(valueStr));

    sDatasource->SetStringValue(nameString, valueString);

    env->ReleaseStringUTFChars(name, nameStr);
    env->ReleaseStringUTFChars(value, valueStr);
}

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetIntValue(JNIEnv* env, jclass clazz, jstring name, jint value) {
    // TODO: implement focusEvent()
    if (nullptr == env || nullptr == sDatasource) {
        ALOGD("!!! NotifyIntValue Error!!! env or actor is NULL");
        return;
    }

    const char* nameStr = env->GetStringUTFChars(name, NULL);
    FString nameString(UTF8_TO_TCHAR(nameStr));
    int intValue = value;

    sDatasource->SetIntValue(nameString, intValue);

    env->ReleaseStringUTFChars(name, nameStr);

    ALOGD("!!!!!!!!!!!!!!! set %s Value %d", nameStr, value);
}

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetFloatValue(JNIEnv* env, jclass clazz, jstring name, jfloat value) {
    // TODO: implement focusEvent()
    if (nullptr == env || nullptr == sDatasource) {
        ALOGD("!!! NotifyFloatValue Error!!! env or actor is NULL");
        return;
    }

    const char* nameStr = env->GetStringUTFChars(name, NULL);
    FString nameString(UTF8_TO_TCHAR(nameStr));
    float floatValue = value;

    sDatasource->SetFloatValue(nameString, floatValue);

    env->ReleaseStringUTFChars(name, nameStr);
    ALOGD("!!!!!!!!!!!!!!! set %s Value %.2f", nameStr, value);
}

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetTarryFloatValue(JNIEnv* env, jclass clazz, jstring name, jfloatArray value)
{
    if (nullptr == env || nullptr == sDatasource) {
        ALOGD("!!! NotifyFloatValue Error!!! env or actor is NULL");
        return;
    }

    const char* nameStr = env->GetStringUTFChars(name, NULL);
    FString nameString(UTF8_TO_TCHAR(nameStr));
    
    jsize length = env->GetArrayLength(value);
    TArray<float> floatValues;
    floatValues.SetNumUninitialized(length);
    env->GetFloatArrayRegion(value,0,length,reinterpret_cast<jfloat*>(&floatValues[0]));

    sDatasource->SetTarryFloatValue(nameString,floatValues);
    env->ReleaseStringUTFChars(name, nameStr);
}

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetTarryVectorValue(JNIEnv* env, jclass clazz, jstring name, jfloatArray XValue,jfloatArray YValue,jfloatArray ZValue)
{
    if (nullptr == env || nullptr == sDatasource) {
        ALOGD("!!! NotifyFloatValue Error!!! env or actor is NULL");
        return;
    }
    const char* nameStr = env->GetStringUTFChars(name, NULL);
    FString nameString(UTF8_TO_TCHAR(nameStr));
    
    jsize length = env->GetArrayLength(XValue);
    TArray<float> XFloatValues;
    TArray<float> YFloatValues;
    TArray<float> ZFloatValues;
    XFloatValues.SetNumUninitialized(length);
    YFloatValues.SetNumUninitialized(length);
    ZFloatValues.SetNumUninitialized(length);
    env->GetFloatArrayRegion(XValue,0,length,reinterpret_cast<jfloat*>(&XFloatValues[0]));
    env->GetFloatArrayRegion(YValue,0,length,reinterpret_cast<jfloat*>(&YFloatValues[0]));
    env->GetFloatArrayRegion(ZValue,0,length,reinterpret_cast<jfloat*>(&ZFloatValues[0]));
    TArray<FVector> PointCoordinates;
    PointCoordinates.SetNumUninitialized(length);
    for(int i = 0;i < length;i++)
    {
        PointCoordinates[i].X = XFloatValues[i];
        PointCoordinates[i].Y = YFloatValues[i];
        PointCoordinates[i].Z = ZFloatValues[i];
    }

    sDatasource->SetTarryVectorValue(nameString,PointCoordinates);
    env->ReleaseStringUTFChars(name, nameStr);
}

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetTMapVectorValue(JNIEnv* env, jclass clazz,
    jstring name,jintArray Value,jfloatArray XValue,jfloatArray YValue,jfloatArray ZValue,jfloatArray RotationValue,jintArray Type)
{
    //转换jstring类型为FString类型
    if (nullptr == env || nullptr == sDatasource) {
        ALOGD("!!! NotifyFloatValue Error!!! env or actor is NULL");
        return;
    }
    const char* nameStr = env->GetStringUTFChars(name, NULL);
    FString nameString(UTF8_TO_TCHAR(nameStr));
    //获取数组长度
    jsize length = env->GetArrayLength(Value);
    //存储Java传输的数据
    TArray<int> IntValues;
    TArray<float> XFloatValues;
    TArray<float> YFloatValues;
    TArray<float> ZFloatValues;
    TArray<float> RotationValues;
    TArray<int> TypeValues;
    //创建数组长度
    IntValues.SetNumUninitialized(length);
    XFloatValues.SetNumUninitialized(length);
    YFloatValues.SetNumUninitialized(length);
    ZFloatValues.SetNumUninitialized(length);
    RotationValues.SetNumUninitialized(length);
    TypeValues.SetNumUninitialized(length);
    //获取数组长度
    env->GetIntArrayRegion(Value,0,length,reinterpret_cast<jint*>(&IntValues[0]));
    env->GetFloatArrayRegion(XValue,0,length,reinterpret_cast<jfloat*>(&XFloatValues[0]));
    env->GetFloatArrayRegion(YValue,0,length,reinterpret_cast<jfloat*>(&YFloatValues[0]));
    env->GetFloatArrayRegion(ZValue,0,length,reinterpret_cast<jfloat*>(&ZFloatValues[0]));
    env->GetFloatArrayRegion(RotationValue,0,length,reinterpret_cast<jfloat*>(&RotationValues[0]));
    env->GetIntArrayRegion(Type,0,length,reinterpret_cast<jint*>(&TypeValues[0]));
    //存储到Map
    TMap<int,FVector> PointValues;
    for(int i = 0;i < length;i++)
    {
        PointValues.Emplace(IntValues[i],(XFloatValues[i],YFloatValues[i],ZFloatValues[i]));
    }
    sDatasource->SetTMapVectorValue(nameString,PointValues,RotationValues,TypeValues);
    env->ReleaseStringUTFChars(name, nameStr);
}

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_RegisterCallback(JNIEnv* env, jclass clazz,
    jobject callback) {
    // TODO: implement RegisterCallback()
    if (0 == env) {
        ALOGD("!!!!!!!!!! Env is empty, register failed");
        return;
    }
    env->GetJavaVM(&sJavaVm);
    //JNIEnv* localEnv;
    //javaVm->GetEnv((void**)&localEnv, JNI_VERSION_1_6);
    sCallbackObj = env->NewGlobalRef(callback);
}
#endif



void CallbackPlatformIntValue(std::string name, int value) {
#if PLATFORM_ANDROID
    JNIEnv* localEnv;
    if (!sJavaVm) {
        ALOGD("sJavaVm is empty.");
        return;
    }
    sJavaVm->GetEnv((void**)&localEnv, JNI_VERSION_1_6);
    jclass clazz = localEnv->GetObjectClass(sCallbackObj);
    jmethodID notifyMethodId = localEnv->GetMethodID(clazz, "OnIntValueCallback", "(Ljava/lang/String;I)V");
    if (0 == notifyMethodId) {
        ALOGD("Not found MethodId of \"OnIntValueCallback\"");
        return;
    }
    localEnv->CallVoidMethod(sCallbackObj, notifyMethodId, localEnv->NewStringUTF(name.c_str()), value);
#endif
}

void CallbackPlatformFloatValue(std::string name, float value) {
#if PLATFORM_ANDROID
    JNIEnv* localEnv;
    if (!sJavaVm) {
        ALOGD("sJavaVm is empty.");
        return;
    }
    sJavaVm->GetEnv((void**)&localEnv, JNI_VERSION_1_6);
    jclass clazz = localEnv->GetObjectClass(sCallbackObj);
    jmethodID notifyMethodId = localEnv->GetMethodID(clazz, "OnFloatValueCallback", "(Ljava/lang/String;F)V");
    if (0 == notifyMethodId) {
        ALOGD("Not found MethodId of \"OnFloatValueCallback\"");
        return;
    }
    localEnv->CallVoidMethod(sCallbackObj, notifyMethodId, localEnv->NewStringUTF(name.c_str()), value);
#endif
}

void CallbackPlatformStringValue(std::string name, std::string value) {
#if PLATFORM_ANDROID
    JNIEnv* localEnv;
    if (!sJavaVm) {
        ALOGD("sJavaVm is empty.");
        return;
    }
    sJavaVm->GetEnv((void**)&localEnv, JNI_VERSION_1_6);
    jclass clazz = localEnv->GetObjectClass(sCallbackObj);
    jmethodID notifyMethodId = localEnv->GetMethodID(clazz, "OnStringValueCallback", "(Ljava/lang/String;Ljava/lang/String)V");
    if (0 == notifyMethodId) {
        ALOGD("Not found MethodId of \"OnStringValueCallback\"");
        return;
    }
    localEnv->CallVoidMethod(sCallbackObj, notifyMethodId, localEnv->NewStringUTF(name.c_str()), localEnv->NewStringUTF(value.c_str()));
#endif
}

void CallbackPlatformTarryFloatValue(std::string name, TArray<float> value)
{
#if PLATFORM_ANDROID
    JNIEnv* localEnv;
    if (!sJavaVm) {
        ALOGD("sJavaVm is empty.");
        return;
    }
    sJavaVm->GetEnv((void**)&localEnv, JNI_VERSION_1_6);
    jclass clazz = localEnv->GetObjectClass(sCallbackObj);
    jmethodID notifyMethodId = localEnv->GetMethodID(clazz, "OnTarryFloatValueCallback", "(Ljava/lang/String;[F)V");
    //创建jfloatArray
    jfloatArray jValue = localEnv->NewFloatArray(value.Num());
    //填充jfloatArray
    localEnv->SetFloatArrayRegion(jValue,0,value.Num(),value.GetData());

    if (0 == notifyMethodId) {
        ALOGD("Not found MethodId of \"OnTarryFloatValueCallback\"");
        localEnv->DeleteLocalRef(jValue);
        return;
    }
    localEnv->CallVoidMethod(sCallbackObj, notifyMethodId, localEnv->NewStringUTF(name.c_str()), jValue);

#endif
}

void CallbackPlatformTarryVectorValue(std::string name, TArray<float> XValue, TArray<float> YValue, TArray<float> ZValue)
{
#if PLATFORM_ANDROID
    JNIEnv* localEnv;
    if (!sJavaVm) {
        ALOGD("sJavaVm is empty.");
        return;
    }
    sJavaVm->GetEnv((void**)&localEnv, JNI_VERSION_1_6);
    jclass clazz = localEnv->GetObjectClass(sCallbackObj);
    jmethodID notifyMethodId = localEnv->GetMethodID(clazz, "OnTarryVectorValueCallback", "(Ljava/lang/String;[F;[F;[F)V");
    //创建jfloatArray
    jfloatArray jXValue = localEnv->NewFloatArray(XValue.Num());
    jfloatArray jYValue = localEnv->NewFloatArray(YValue.Num());
    jfloatArray jZValue = localEnv->NewFloatArray(ZValue.Num());
    //填充jfloatArray
    localEnv->SetFloatArrayRegion(jXValue,0,XValue.Num(),XValue.GetData());
    localEnv->SetFloatArrayRegion(jYValue,0,YValue.Num(),YValue.GetData());
    localEnv->SetFloatArrayRegion(jZValue,0,ZValue.Num(),ZValue.GetData());

    if (0 == notifyMethodId) {
        ALOGD("Not found MethodId of \"OnTarryVectorValueCallback\"");
        localEnv->DeleteLocalRef(jXValue);
        localEnv->DeleteLocalRef(jYValue);
        localEnv->DeleteLocalRef(jZValue);
        return;
    }
    localEnv->CallVoidMethod(sCallbackObj, notifyMethodId, localEnv->NewStringUTF(name.c_str()), jXValue,jYValue,jZValue);

#endif
}

void CallbackPlatformTMapVectorValue(std::string name, TArray<int> Value,TArray<float> XValue, TArray<float> YValue, TArray<float> ZValue,TArray<float> RotationValue, TArray<int> Type)
{
#if PLATFORM_ANDROID
    JNIEnv* localEnv;
    if (!sJavaVm) {
        ALOGD("sJavaVm is empty.");
        return;
    }
    sJavaVm->GetEnv((void**)&localEnv, JNI_VERSION_1_6);
    jclass clazz = localEnv->GetObjectClass(sCallbackObj);
    jmethodID notifyMethodId = localEnv->GetMethodID(clazz, "OnTarryVectorValueCallback", "(Ljava/lang/String;[I;[F;[F;[F)V");
    //创建jfloatArray
    jintArray jValue = localEnv->NewIntArray(Value.Num());
    jfloatArray jXValue = localEnv->NewFloatArray(XValue.Num());
    jfloatArray jYValue = localEnv->NewFloatArray(YValue.Num());
    jfloatArray jZValue = localEnv->NewFloatArray(ZValue.Num());
    jfloatArray jRotationValue = localEnv->NewFloatArray(RotationValue.Num());
    jintArray jType = localEnv->NewIntArray(Type.Num());
    //填充jfloatArray
    localEnv->SetIntArrayRegion(jValue,0,Value.Num(),Value.GetData());
    localEnv->SetFloatArrayRegion(jXValue,0,XValue.Num(),XValue.GetData());
    localEnv->SetFloatArrayRegion(jYValue,0,YValue.Num(),YValue.GetData());
    localEnv->SetFloatArrayRegion(jZValue,0,ZValue.Num(),ZValue.GetData());
    localEnv->SetFloatArrayRegion(jRotationValue,0,RotationValue.Num(),RotationValue.GetData());
    localEnv->SetIntArrayRegion(jType,0,Type.Num(),Type.GetData());

    if (0 == notifyMethodId) {
        ALOGD("Not found MethodId of \"OnTarryVectorValueCallback\"");
        localEnv->DeleteLocalRef(jValue);
        localEnv->DeleteLocalRef(jXValue);
        localEnv->DeleteLocalRef(jYValue);
        localEnv->DeleteLocalRef(jZValue);
        localEnv->DeleteLocalRef(jRotationValue);
        localEnv->DeleteLocalRef(jType);
        return;
    }
    localEnv->CallVoidMethod(sCallbackObj, notifyMethodId, localEnv->NewStringUTF(name.c_str()),jValue,jXValue,jYValue,jZValue,jRotationValue,jType);

#endif
}
