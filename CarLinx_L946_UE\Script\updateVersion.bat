@echo off
setlocal enabledelayedexpansion

REM 设置日志文件路径
set LOG_FILE=%~dp0log.txt

REM 记录开始时间
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set start_time=%%I
set START_TIME=!start_time:~0,4!-!start_time:~4,2!-!start_time:~6,2! !start_time:~8,2!:!start_time:~10,2!:!start_time:~12,2!

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
cd /d %SCRIPT_DIR%\..

REM 检查是否为git仓库
git rev-parse --git-dir >nul 2>&1
if errorlevel 1 (
    echo [!START_TIME!] Error: Not a git repository. Using default values.
    echo [!START_TIME!] Error: Not a git repository. Using default values. >> "%LOG_FILE%"
    echo.
    
    REM 设置默认值
    set ORIGINAL_COMMIT_HASH=000000
    set COMMIT_COUNT=0
    set VERSION_TAG=v0.0.0
    set SYNC_STATUS=[Unknown]
    set DIRTY_STATUS=^<Unknown^>
    set PREFIX=
    set LOCAL_BRANCH=Unknown
    set REMOTE_BRANCH=Unknown
) else (
    echo [!START_TIME!] Git repository detected.
    echo [!START_TIME!] Fetching remote updates...
    
    REM 同步远程代码（忽略错误）
    git fetch --all --prune 2>nul
    echo [!START_TIME!] Remote updates fetched.
    
    REM 获取当前本地分支名
    for /f %%i in ('git rev-parse --abbrev-ref HEAD') do set LOCAL_BRANCH=%%i
    echo [!START_TIME!] Current local branch: !LOCAL_BRANCH!
    
    REM 获取远程跟踪分支名（改进的错误处理）
    for /f %%i in ('git rev-parse --abbrev-ref --symbolic-full-name @{u} 2^>nul') do set REMOTE_BRANCH=%%i
    if "!REMOTE_BRANCH!"=="" (
        REM 如果没有上游分支，尝试找到包含当前提交的远程分支
        for /f "tokens=*" %%i in ('git branch -r --contains HEAD 2^>nul ^| findstr /V "Dev-Meng-HPA"') do (
            set REMOTE_BRANCH=%%i
            goto found_remote
        )
        REM 如果还是找不到，使用默认值
        set REMOTE_BRANCH=origin/main
    )
:found_remote
    set REMOTE_BRANCH=!REMOTE_BRANCH: =!
    
    REM 获取原始git信息
    for /f %%i in ('git rev-parse HEAD') do set ORIGINAL_COMMIT_HASH=%%i
    for /f %%i in ('git rev-list HEAD --count') do set COMMIT_COUNT=%%i
    for /f "delims=" %%i in ('git describe --tags --abbrev^=0 2^>nul') do set VERSION_TAG=%%i
    if "!VERSION_TAG!"=="" set VERSION_TAG=v0.0.0
    
    REM 检查本地分支与远程分支差异（改进的错误处理）
    echo [!START_TIME!] Checking branch differences...
    git fetch origin !LOCAL_BRANCH! 2>nul
    for /f %%i in ('git rev-list HEAD...origin/!LOCAL_BRANCH! --count 2^>nul') do set SYNC_COUNT=%%i
    if "!SYNC_COUNT!"=="" set SYNC_COUNT=0
    echo [!START_TIME!] Found !SYNC_COUNT! commits difference.
    
    REM 检查是否有未提交的更改
    echo [!START_TIME!] Checking for uncommitted changes...
    set DIRTY_COUNT=0
    for /f %%i in ('git status --porcelain') do set /a DIRTY_COUNT+=1
    echo [!START_TIME!] Found !DIRTY_COUNT! uncommitted changes.
    
    REM 根据状态设置前缀
    set "PREFIX="
    if !SYNC_COUNT! gtr 0 (
        if !DIRTY_COUNT! gtr 0 (
            set "PREFIX=(*#)"
        ) else (
            set "PREFIX=(#)"
        )
    ) else (
        if !DIRTY_COUNT! gtr 0 (
            set "PREFIX=(*)"
        )
    )
    
    REM 设置状态信息
    if !SYNC_COUNT! gtr 0 (
        set SYNC_STATUS=[Need Push !SYNC_COUNT! Commits]
    ) else (
        set SYNC_STATUS=[Synced]
    )
    
    if !DIRTY_COUNT! gtr 0 (
        set DIRTY_STATUS=^<Has !DIRTY_COUNT! Changed Files^>
    ) else (
        set DIRTY_STATUS=^<Clean^>
    )
)

REM 获取系统信息
for /f %%i in ('hostname') do set COMPUTER_NAME=%%i
for /f %%i in ('echo %USERNAME%') do set USER_NAME=%%i

REM 获取当前时间
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /value') do set datetime=%%I
set BUILD_TIME=!datetime:~0,4!-!datetime:~4,2!-!datetime:~6,2! !datetime:~8,2!:!datetime:~10,2!:!datetime:~12,2!

REM 截取commit hash前8位并添加前缀
set COMMIT_HASH_SHORT=!ORIGINAL_COMMIT_HASH:~0,8!
set COMMIT_HASH=!PREFIX!!COMMIT_HASH_SHORT!

REM 拼接完整版本字符串
set VERSION_STRING=!VERSION_TAG!_!COMMIT_HASH!_!USER_NAME!@!COMPUTER_NAME!_(!BUILD_TIME!)

REM 设置输出路径
set OUTPUT_H_DIR=%SCRIPT_DIR%..\Source\CarLinx_L946_UE\Public
set OUTPUT_TXT_DIR=%SCRIPT_DIR%..\Content\Version

REM 创建输出目录
if not exist "%OUTPUT_H_DIR%" mkdir "%OUTPUT_H_DIR%"
if not exist "%OUTPUT_TXT_DIR%" mkdir "%OUTPUT_TXT_DIR%"

REM 生成Version.h
echo [!START_TIME!] Generating Version.h...
echo [!START_TIME!] Output path: %OUTPUT_H_DIR%\Version.h
echo #pragma once > "%OUTPUT_H_DIR%\Version.h"
echo. >> "%OUTPUT_H_DIR%\Version.h"
echo #define LOCAL_BRANCH "!LOCAL_BRANCH!" >> "%OUTPUT_H_DIR%\Version.h"
echo #define REMOTE_BRANCH "!REMOTE_BRANCH!" >> "%OUTPUT_H_DIR%\Version.h"
echo #define VERSION_TAG "!VERSION_TAG!" >> "%OUTPUT_H_DIR%\Version.h"
echo #define GIT_COMMIT_HASH "!COMMIT_HASH!" >> "%OUTPUT_H_DIR%\Version.h"
echo #define GIT_COMMIT_COUNT "!COMMIT_COUNT!" >> "%OUTPUT_H_DIR%\Version.h"
echo #define BUILD_COMPUTER_NAME "!COMPUTER_NAME!" >> "%OUTPUT_H_DIR%\Version.h"
echo #define BUILD_USER_NAME "!USER_NAME!" >> "%OUTPUT_H_DIR%\Version.h"
echo #define BUILD_TIME "(!BUILD_TIME!)" >> "%OUTPUT_H_DIR%\Version.h"
echo #define GIT_SYNC_STATUS "!SYNC_STATUS!" >> "%OUTPUT_H_DIR%\Version.h"
echo #define GIT_DIRTY_STATUS "!DIRTY_STATUS!" >> "%OUTPUT_H_DIR%\Version.h"
echo #define VERSION_STRING "!VERSION_STRING!" >> "%OUTPUT_H_DIR%\Version.h"

REM 生成Version.txt
echo [!START_TIME!] Generating Version.txt...
echo [!START_TIME!] Output path: %OUTPUT_TXT_DIR%\Version.txt
echo Local Branch: !LOCAL_BRANCH! > "%OUTPUT_TXT_DIR%\Version.txt"
echo Remote Branch: !REMOTE_BRANCH! >> "%OUTPUT_TXT_DIR%\Version.txt"
echo Version Tag: !VERSION_TAG! >> "%OUTPUT_TXT_DIR%\Version.txt"
echo Git Commit Hash: !COMMIT_HASH! >> "%OUTPUT_TXT_DIR%\Version.txt"
echo Git Commit Count: !COMMIT_COUNT! >> "%OUTPUT_TXT_DIR%\Version.txt"
echo Build Computer: !COMPUTER_NAME! >> "%OUTPUT_TXT_DIR%\Version.txt"
echo Build User: !USER_NAME! >> "%OUTPUT_TXT_DIR%\Version.txt"
echo Build Time: !BUILD_TIME! >> "%OUTPUT_TXT_DIR%\Version.txt"
echo Git Sync Status: !SYNC_STATUS! >> "%OUTPUT_TXT_DIR%\Version.txt"
echo Git Dirty Status: !DIRTY_STATUS! >> "%OUTPUT_TXT_DIR%\Version.txt"
echo Version String: !VERSION_STRING! >> "%OUTPUT_TXT_DIR%\Version.txt"

echo [!START_TIME!] Version.h generated successfully.
echo.

echo [!START_TIME!] Version.txt generated successfully.
echo.

echo [!START_TIME!] Summary of version information:
echo    Local Branch: !LOCAL_BRANCH!
echo    Remote Branch: !REMOTE_BRANCH!
echo    Version Tag: !VERSION_TAG!
echo    Commit Hash: !COMMIT_HASH!
echo    Sync Status: !SYNC_STATUS!
echo    Dirty Status: !DIRTY_STATUS!
echo.
echo [!START_TIME!] All version files generated successfully!