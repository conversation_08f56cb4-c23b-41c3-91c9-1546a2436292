// Fill out your copyright notice in the Description page of Project Settings.


#include "ToolFunctionLibrary.h"
#include "..\Public\ToolFunctionLibrary.h"

#include "FileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Serialization/JsonWriter.h"
#include "Serialization/JsonSerializer.h"
#include "ImageUtils.h"  // 用于处理图片数据
#include "Engine/Texture2D.h" 
#include "Engine/TextureRenderTarget2D.h"
#include "Logger.h"

// 在编辑器模式下使用 Windows 路径，否则使用 Android 设备存储路径
#if WITH_EDITOR
#define JSON_FILE_PATH (TEXT("/data/carlinx_data/HPA/HPAMapID/MapID.json"))
#else
#define JSON_FILE_PATH (TEXT("/data/carlinx_data/HPA/HPAMapID/MapID.json"))
#endif


FParkingSpace UToolFunctionLibrary::ReadParkingData(const TMap<FString, FString>& Map, const int32 SlotNumber)
{

	FParkingSpace _parkingSpace;


	_parkingSpace.ID = SlotNumber;
	FVector Point_A(FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotStart.posnX"))) * 10.f,
		FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotStart.posnY"))) * -10.f,
		FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotStart.posnZ"))) * 10.f
	);
	FVector Point_B
	(FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotEnd.posnX"))) * 10.f,
		FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotEnd.posnY"))) * -10.f,
		FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotEnd.posnZ"))) * 10.f
	);
	FVector Point_C(FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotStartRear.posnX"))) * 10.f,
		FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotStartRear.posnY"))) * -10.f,
		FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotStartRear.posnZ"))) * 10.f
	);
	FVector Point_D(FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotEndRear.posnX"))) * 10.f,
		FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotEndRear.posnY"))) * -10.f,
		FCString::Atof(*getMapValue(Map, TEXT("slotBounding.slotEndRear.posnZ"))) * 10.f
	);


	_parkingSpace.CenterPosition = CalculateCenterPoint(Point_A, Point_B, Point_C, Point_D);

	_parkingSpace.Angle = CalculateAngleWithXAxis360(Point_A, Point_B, Point_C, Point_D, _parkingSpace.CenterPosition);

	//_parkingSpace.Lenth = CAtrueOrFalse(Point_A, Point_B, Point_C, Point_D);
	_parkingSpace.Lenth = -1;

	// 计算停车位的长度和宽度
	float length = 0.0f, width = 0.0f;
	CalculateParkingSpaceDimensions(Point_A, Point_B, Point_C, Point_D, length, width);
	_parkingSpace.Length = length;
	_parkingSpace.Width = width;


	_parkingSpace.status = FCString::Atoi(*getMapValue(Map, TEXT("slotSt.slotSts1")));
	_parkingSpace.Number = FCString::Atoi(*getMapValue(Map, TEXT("slotSt.slotNumber")));
	_parkingSpace.Type = FCString::Atoi(*getMapValue(Map, TEXT("slotTyp.slotTypinfo")));
	_parkingSpace.Index = FCString::Atoi(*getMapValue(Map, TEXT("slotID")));
	_parkingSpace.hasWheelBlock = FCString::Atoi(*getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.hasWheelBlock")));
	_parkingSpace.wheelBlockFront = FCString::Atoi(*getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.wheelBlockFront")));
	_parkingSpace.wheelBlockRear = FCString::Atoi(*getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.wheelBlockRear")));
	_parkingSpace.hasGroundLock = FCString::Atoi(*getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.hasGroundLock")));
	_parkingSpace.groundLockStatus = FCString::Atoi(*getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.groundLockStatus")));

	/*CARLINX_LOG(LogCarLinx, Log, TEXT("Convert Slot Data: SlotID: %d, hasWheelBlock: %d, wheelBlockFront: %d, wheelBlockRear: %d, hasGroundLock:%d, groundLockStatus:%d"),
		_parkingSpace.Index, _parkingSpace.hasWheelBlock, _parkingSpace.wheelBlockFront, _parkingSpace.wheelBlockRear, _parkingSpace.hasGroundLock, _parkingSpace.groundLockStatus);
	CARLINX_LOG(LogCarLinx, Log, TEXT("Receive Slot Data: SlotID: %d, hasWheelBlock: %s, wheelBlockFront: %s, wheelBlockRear: %s, hasGroundLock:%s, groundLockStatus:%s"),
		_parkingSpace.Index, *getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.hasWheelBlock")), *getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.wheelBlockFront")),
		*getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.wheelBlockRear")), *getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.hasGroundLock")), *getMapValue(Map, TEXT("slotTyp.obiqueSlotAg.groundLockStatus")));*/
	
	return _parkingSpace;

	/*if (Point_A!=Point_D)
	{

		_parkingSpace.CenterPosition = CalculateCenterPoint(Point_A, Point_B, Point_C, Point_D);

		_parkingSpace.Angle = CalculateAngleWithXAxis360(Point_A, Point_B, Point_C, Point_D, _parkingSpace.CenterPosition);

		_parkingSpace.Lenth = CAtrueOrFalse(Point_A, Point_B, Point_C, Point_D);
		_parkingSpace.status = FCString::Atoi(*getMapValue(Map, TEXT("slotSt.slotSts1")));
		_parkingSpace.Number = FCString::Atoi(*getMapValue(Map, TEXT("slotSt.slotNumber")));
		return _parkingSpace;
	}
	// CARLINX_LOG(LogCarLinx, Log, TEXT("TargetPointSlot ************Null: "));
	return _parkingSpace;*/
}

void UToolFunctionLibrary::CalculateParkingSpaceDimensions(const FVector A, const FVector B, const FVector C, const FVector D, float& OutLength, float& OutWidth)
{
	// 计算所有可能的连线长度
	float distances[6];
	// 使用SizeSquared()避免开方运算,提升性能
	distances[0] = (A - B).SizeSquared();  // AB
	distances[1] = (B - C).SizeSquared();  // BC
	distances[2] = (C - D).SizeSquared();  // CD
	distances[3] = (D - A).SizeSquared();  // DA
	distances[4] = (A - C).SizeSquared();  // AC
	distances[5] = (B - D).SizeSquared();  // BD

	// 使用更高效的排序算法
	for(int i = 0; i < 5; ++i) {
		int minIdx = i;
		for(int j = i + 1; j < 6; ++j) {
			if(distances[j] < distances[minIdx]) {
				minIdx = j;
			}
		}
		if(minIdx != i) {
			float temp = distances[i];
			distances[i] = distances[minIdx];
			distances[minIdx] = temp;
		}
	}

	// 最后再开方,减少开方次数
	OutWidth = (FMath::Sqrt(distances[0]) + FMath::Sqrt(distances[1])) * 0.5f;
	OutLength = (FMath::Sqrt(distances[2]) + FMath::Sqrt(distances[3])) * 0.5f;

	CARLINX_LOG(LogCarLinx, Log, TEXT("停车位尺寸计算: 对角线=%.2f, 长=%.2f, 宽=%.2f"), 
		FMath::Sqrt(distances[5]), OutLength, OutWidth);
}

FString UToolFunctionLibrary::getMapValue(const TMap<FString, FString>& Map, const FString& key)
{
	// FString result = FString::Printf(TEXT("%s"), *key);

	//  // CARLINX_LOG(LogCarLinx, Warning, TEXT("Key: %s"), *result);
	// FString result = FString::Printf(TEXT("%d%s%s"), id, *sloat, *key);
	//result = L"slot6.slotBounding.slotEnd.posnY"

	if (Map.Contains(key)) {
		auto value = Map.Find(key);
		//  // CARLINX_LOG(LogCarLinx, Warning, TEXT("key:%s value: %s"), *key, **value);
		return *value;
	} else {
		//  // CARLINX_LOG(LogCarLinx, Warning, TEXT("Key not found"));
		return TEXT("0.0");
	}
}

FVector UToolFunctionLibrary::CalculateCenterPoint(const FVector A, const FVector B, const FVector C, const FVector D)
{
	FVector Center = (A + B + C + D) / 4.0f;
	return Center;
}

float UToolFunctionLibrary::CalculateAngleWithXAxis360(const FVector A, const FVector B, const FVector C, const FVector D, const FVector CenterPoint)
{
	if (A==D)
	{
		return 0.f;
	}
	FVector VectorCA = (A - C).GetSafeNormal();
	FVector Forward = FVector::UpVector;
	FMatrix RotationMatrix = FRotationMatrix::MakeFromXZ(VectorCA, Forward);
	FRotator Rotator = RotationMatrix.Rotator();

	return Rotator.Yaw;
}

int32 UToolFunctionLibrary::CAtrueOrFalse(const FVector A, const FVector B, const FVector C, const FVector D)
{
	FVector VectorCA = A - C;
	float LengthCA = VectorCA.Size();

	 // CARLINX_LOG(LogCarLinx, Warning, TEXT("AngleInDegrees value: %f"), LengthCA);
	FVector VectorBA = A - B;

	float LengthBA = VectorBA.Size();
	 // CARLINX_LOG(LogCarLinx, Warning, TEXT("AngleInDegrees value: %f"),  LengthBA);
	if (LengthCA> LengthBA)
	{
		return 1;
	}
	if (LengthCA < LengthBA)
	{
		return 0;
	}
	return -1;
}

float UToolFunctionLibrary::AngleConversion(const float& Value)
{
	float Pi = 3.1415926535897932f;
	return (Value * 0.00009765625f - 3.2) * 180 / Pi;
}

int32 UToolFunctionLibrary::ParkingLotTypeManager(const int32& Value)
{
	if (Value == 1 || Value == 4 || Value == 7 || Value == 10 || Value == 13)
	{
		//水平车位类型
		return 0;
	}
	else if (Value == 0) 
	{
		//无效车位类型
		return -1;
	}
	else
	{
		//垂直车位类型
		return 1;
	}
}



bool UToolFunctionLibrary::LeftOrRight(const FVector& MyLocation, const FVector& Forward, const FVector& TargetPoint)
{
	// CARLINX_LOG(LogCarLinx, Log, TEXT("MyLocationCar: %s"), *MyLocation.ToString());
	// CARLINX_LOG(LogCarLinx, Log, TEXT("TargetPointSlot: %s"), *MyLocation.ToString());
	if (MyLocation==TargetPoint)
	{
		return false;
	}
	
	FVector ToTarget = TargetPoint - MyLocation;
	FVector NormalizedForward = Forward.GetSafeNormal();
	FVector NormalizedTarget = ToTarget.GetSafeNormal();
	FVector CrossProduct = FVector::CrossProduct(NormalizedForward, NormalizedTarget);

	float DotProduct = FVector::DotProduct(FVector::UpVector, CrossProduct);
	if (DotProduct > 0.0f)
	{
		// CARLINX_LOG(LogCarLinx, Log, TEXT("TargetPointSlot: Right"));
		return true;
		
	}
	else if (DotProduct < 0.0f)
	{
		// CARLINX_LOG(LogCarLinx, Log, TEXT("TargetPointSlot: Left"));
		return false;
		
	}
	else
	{
		// CARLINX_LOG(LogCarLinx, Log, TEXT("TargetPointSlot  RL"));
		return false;
	}
	return false;
}

ECarSlot UToolFunctionLibrary::SlotType(const FVector& MyLocation, const FVector& Forward, const FVector& TargetPoint)
{
	// CARLINX_LOG(LogCarLinx, Log, TEXT("MyLocationCar : %s"), *MyLocation.ToString());
	// CARLINX_LOG(LogCarLinx, Log, TEXT("TargetPointSlot *: %s"), *TargetPoint.ToString());
	if (MyLocation == TargetPoint)
	{
		return ECarSlot::Unkonw;
	}
	FVector ToTarget = TargetPoint - MyLocation;
	ToTarget.Z = 0.0f;

	FVector RightVector = FVector::CrossProduct(Forward, FVector::UpVector).GetSafeNormal();

	float DotProduct = FVector::DotProduct(RightVector, ToTarget);


	//float DotProduct = FVector::DotProduct(FVector::UpVector, CrossProduct);
	if (DotProduct > 0.0f)
	{
		// CARLINX_LOG(LogCarLinx, Log, TEXT("TargetPointSlot *Right: "));
		return ECarSlot::RightSlot; // ???

	}
	else if (DotProduct < 0.0f)
	{
		// CARLINX_LOG(LogCarLinx, Log, TEXT("TargetPointSlot *Left: "));
		return ECarSlot::LeftSlot; // ???

	}
	return ECarSlot::Unkonw;
}

bool UToolFunctionLibrary::SaveVectorArrayToJsonWithTimestamp(const TArray<FVector>& VectorArray, const FString& SaveDirectory, bool bIsAbsolutePath)
{
	if (VectorArray.Num() == 0)
	{
		 // CARLINX_LOG(LogCarLinx, Warning, TEXT("Vector array is empty, nothing to save."));
		return false;
	}

	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

	TArray<TSharedPtr<FJsonValue>> JsonArray;

	for (const FVector& Vector : VectorArray)
	{
		TSharedPtr<FJsonObject> VectorObject = MakeShareable(new FJsonObject);
		VectorObject->SetNumberField(TEXT("X"), Vector.X);
		VectorObject->SetNumberField(TEXT("Y"), Vector.Y);
		VectorObject->SetNumberField(TEXT("Z"), Vector.Z);

		JsonArray.Add(MakeShareable(new FJsonValueObject(VectorObject)));
	}

	JsonObject->SetArrayField(TEXT("Vectors"), JsonArray);

	FString OutputString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

	FDateTime CurrentTime = FDateTime::Now();
	FString Timestamp = CurrentTime.ToString(TEXT("%Y-%m-%d_%H-%M-%S"));
	FString FileName = FString::Printf(TEXT("SavedVectors_%s.json"), *Timestamp);

	FString FilePath;
	if (bIsAbsolutePath)
	{
		FilePath = SaveDirectory / FileName;
	}
	else
	{
		FilePath = FPaths::ProjectDir() / SaveDirectory / FileName;
	}

	if (FFileHelper::SaveStringToFile(OutputString, *FilePath))
	{
		// CARLINX_LOG(LogCarLinx, Log, TEXT("JSON saved successfully to: %s"), *FilePath);
		return true;
	}
	else
	{
		 // CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to save JSON to: %s"), *FilePath);
		return false;
	}

}

void UToolFunctionLibrary::CreateDrivingSnapshotData()
{

	FString SavedFolder = FPaths::ProjectSavedDir();

	FString NewFolderPath = FPaths::Combine(SavedFolder, TEXT("DrivingSnapshotData"));

	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	if (!PlatformFile.DirectoryExists(*NewFolderPath))
	{
		PlatformFile.CreateDirectory(*NewFolderPath);
	}

	FString FilePath = FPaths::Combine(NewFolderPath, TEXT("snapshot.txt"));

	FString Content = TEXT("1");

	if (FFileHelper::SaveStringToFile(Content, *FilePath))
	{
		// CARLINX_LOG(LogCarLinx, Log, TEXT("ssss: %s"), *FilePath);
	}
	else
	{
	
	}


}

bool UToolFunctionLibrary::SaveTextureToPNG(UTexture2D* Texture, const FString& FileName)
{

	if (!Texture || !Texture->PlatformData || Texture->PlatformData->Mips.Num() == 0)
	{
		 // CARLINX_LOG(LogCarLinx, Error, TEXT("Invalid texture data."));
		return false;
	}

	FTexture2DMipMap& MipMap = Texture->PlatformData->Mips[0];
	int32 ImageWidth = MipMap.SizeX;
	int32 ImageHeight = MipMap.SizeY;

	void* Data = MipMap.BulkData.Lock(LOCK_READ_ONLY);
	if (!Data)
	{
		 // CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to lock texture data."));
		return false;
	}

	TArray<FColor> SrcData;
	SrcData.SetNumUninitialized(ImageWidth * ImageHeight);
	FMemory::Memcpy(SrcData.GetData(), Data, ImageWidth * ImageHeight * sizeof(FColor));
	MipMap.BulkData.Unlock();

	TArray<uint8> CompressedData;
	FImageUtils::CompressImageArray(ImageWidth, ImageHeight, SrcData, CompressedData);

	FString SaveDirectory = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Textures"));
	IFileManager::Get().MakeDirectory(*SaveDirectory, true);
	FString FullPath = FPaths::Combine(SaveDirectory, FileName);

	if (FFileHelper::SaveArrayToFile(CompressedData, *FullPath))
	{
		// CARLINX_LOG(LogCarLinx, Log, TEXT("Saved texture to %s"), *FullPath);
		return true;
	}
	else
	{
		 // CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to save texture to %s"), *FullPath);
		return false;
	}
}

bool UToolFunctionLibrary::SaveRenderTargetToPNG(UTextureRenderTarget2D* RenderTarget, const FString& FullFilePath)
{

	if (!RenderTarget)
	{
		 // CARLINX_LOG(LogCarLinx, Error, TEXT("Invalid RenderTarget."));
		return false;
	}

	FTextureRenderTargetResource* RenderTargetResource = RenderTarget->GameThread_GetRenderTargetResource();
	if (!RenderTargetResource)
	{
		 // CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to get render target resource."));
		return false;
	}

	int32 Width = RenderTarget->SizeX;
	int32 Height = RenderTarget->SizeY;

	TArray<FColor> Bitmap;
	Bitmap.SetNumUninitialized(Width * Height);
	FReadSurfaceDataFlags ReadSurfaceDataFlags(RCM_UNorm);
	ReadSurfaceDataFlags.SetLinearToGamma(false);


	// 读取像素数据
	RenderTargetResource->ReadPixels(Bitmap, ReadSurfaceDataFlags);

	for (FColor& Color : Bitmap)
	{
		Color.A = 255; // Make alpha channel fully opaque
		Color = Color.ReinterpretAsLinear().ToFColor(true);
	}


	TArray<uint8> CompressedData;
	FImageUtils::CompressImageArray(Width, Height, Bitmap, CompressedData);

	FString SaveDirectory = FPaths::GetPath(FullFilePath);
	IFileManager::Get().MakeDirectory(*SaveDirectory, true);
	if (FFileHelper::SaveArrayToFile(CompressedData, *FullFilePath))
	{
		// CARLINX_LOG(LogCarLinx, Log, TEXT("Saved render target to %s"), *FullPath);
		return true;
	}
	else
	{
		 // CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to save render target to %s"), *FullPath);
		return false;
	}
}

int32 UToolFunctionLibrary::SetBitAtPosition(int32 n)
{

	if (n < 0 || n >= 32)
	{
		return 0;
	}
	return (int32)(1U << n);
}

FString UToolFunctionLibrary::SetBitAtPositionAsString(int32 n)
{
	int32 Result = SetBitAtPosition(n);
	return FString::Printf(TEXT("%032b"), Result);  // 格式化为 32 位二进制字符串
}

void UToolFunctionLibrary::GetBitByID(int32 ID, int32& Result)
{
	// 检查输入范围
	if (ID < 0 || ID > 99)
	{
		UE_LOG(LogTemp, Warning, TEXT("ID outline (0-99)!"));
		Result = 0;
		return;
	}

	// 计算偏移量
	int32 Offset = ID % 32;   // 位偏移量
	uint32 TempResult = (1u << Offset);  // 使用 uint32 计算
	Result = static_cast<int32>(TempResult);  // 转换为 int32 返回
}

TArray<int32> UToolFunctionLibrary::ConvertJsonListsToIntArray(const FString& JsonFilePath)
{
	TArray<int32> ResultArray;

	// 检查文件是否存在
	if (!FPaths::FileExists(JsonFilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("JSON file not found at path: %s"), *JsonFilePath);
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] JSON file not found at path: %s"), *JsonFilePath);
		return ResultArray;
	}

	FString JsonContent;
	// 读取文件内容
	if (!FFileHelper::LoadFileToString(JsonContent, *JsonFilePath))
	{
		//UE_LOG(LogTemp, Error, TEXT("Failed to load JSON file: %s"), *JsonFilePath);
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] Failed to load JSON file: %s"),*JsonFilePath);
		return ResultArray;
	}

	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonContent);

	// 解析 JSON 文件
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON file: %s"), *JsonFilePath);
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] Failed to parse JSON file: %s"), *JsonFilePath);
		return ResultArray;
	}

	// 遍历 List1 到 List4
	for (int32 i = 1; i <= 4; i++)
	{
		FString ListKey = FString::Printf(TEXT("List%d"), i);

		// 获取对应的 List 数组
		const TArray<TSharedPtr<FJsonValue>>* JsonArray;
		if (!JsonObject->TryGetArrayField(ListKey, JsonArray))
		{
			UE_LOG(LogTemp, Error, TEXT("List key %s not found or invalid in JSON file."), *ListKey);
			CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] List key %s not found or invalid in JSON file."), *ListKey);

			ResultArray.Add(0); // 添加默认值
			continue;
		}

		// 检查数组大小
		if (JsonArray->Num() != 32)
		{
			UE_LOG(LogTemp, Error, TEXT("%s does not contain exactly 32 elements."), *ListKey);
			CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] %s does not contain exactly 32 elements."), *ListKey);
			ResultArray.Add(0); // 添加默认值
			continue;
		}

		int32 ConvertedValue = 0;
		for (int32 BitIndex = 0; BitIndex < 32; ++BitIndex)
		{
			int32 BitValue = (*JsonArray)[BitIndex]->AsNumber();
			if (BitValue != 0 && BitValue != 1)
			{
				UE_LOG(LogTemp, Error, TEXT("Invalid bit value %d in %s at index %d."), BitValue, *ListKey, BitIndex);
				CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray]Invalid bit value % d in % s at index % d."), BitValue, *ListKey, BitIndex);
				ConvertedValue = 0; // 遇到错误时设置为默认值
				break;
			}

			// 使用位操作将 bit 加入结果
			ConvertedValue |= (BitValue << BitIndex);
		}

		ResultArray.Add(ConvertedValue);
	}

	return ResultArray;
}
//属于哪一个List
int32 UToolFunctionLibrary::DetermineListAndIndex(int32 InputValue)
{
	int32 ListIndex = InputValue / 32;
	return ListIndex + 1;
}

int32 UToolFunctionLibrary::DetermineListIndex(int32 InputValue)
{
	int32 SubIndex = InputValue % 32;
	return SubIndex;
}

TArray<int32> UToolFunctionLibrary::Int32ToBinaryArray(int32 Value)
{
	{
		TArray<int32> BinaryArray;
		BinaryArray.SetNum(31); // 初始化大小为 31

		for (int32 i = 0; i < 31; ++i)
		{
			BinaryArray[i] = (Value >> i) & 1; // 提取第 i 位并存入数组
		}

		return BinaryArray;
	}
}


bool UToolFunctionLibrary::ModifyJsonBasedOnBitIndices(const FString& JsonFilePath, const FString& ListName, int32 InputValue)
{

	if (JsonFilePath.IsEmpty() || ListName.IsEmpty())
	{
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ModifyJsonBasedOnBitIndices] Invalid input: JsonFilePath or ListName is empty."));
		return false;
	}

	TArray<int32> BitIndices;

	// Step 1: 提取二进制中为1的位
	for (int32 i = 31; i >= 0; --i)
	{
		if ((InputValue & (1 << i)) != 0)
		{
			BitIndices.Add(i);
		}
	}

	if (BitIndices.Num() == 0)
	{
		CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonBasedOnBitIndices] No bits are set to 1 in value: %d"), InputValue);
		return true; // 没有需要修改的位，但逻辑正常完成
	}

	// Step 2: 加载 JSON 文件
	FString JsonString;
	if (!FFileHelper::LoadFileToString(JsonString, *JsonFilePath))
	{
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ModifyJsonBasedOnBitIndices] Failed to load JSON file: %s"), *JsonFilePath);
		return false;
	}

	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ModifyJsonBasedOnBitIndices] Failed to parse JSON content."));
		return false;
	}

	// Step 3: 获取目标列表并修改
	const TArray<TSharedPtr<FJsonValue>>* TargetList;
	if (!JsonObject->TryGetArrayField(ListName, TargetList))
	{
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ModifyJsonBasedOnBitIndices] List '%s' not found in JSON."), *ListName);
		return false;
	}

	TArray<TSharedPtr<FJsonValue>> ModifiedList = *TargetList;
	bool bModified = false;

	for (int32 BitIndex : BitIndices)
	{
		if (BitIndex >= 0 && BitIndex < ModifiedList.Num())
		{
			ModifiedList[BitIndex] = MakeShared<FJsonValueNumber>(0);
			bModified = true;
		}
		else
		{
			CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonBasedOnBitIndices] Bit index %d is out of range for list '%s'."), BitIndex, *ListName);
		}
	}

	if (!bModified)
	{
		CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonBasedOnBitIndices] No valid indices to modify in the list '%s'."), *ListName);
		return true; // 没有合法修改，但逻辑正常完成
	}

	JsonObject->SetArrayField(ListName, ModifiedList);

	// Step 4: 保存修改后的 JSON 文件
	FString ModifiedJsonString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ModifiedJsonString);
	if (FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer))
	{
		if (FFileHelper::SaveStringToFile(ModifiedJsonString, *JsonFilePath))
		{
			CARLINX_LOG(LogCarLinx,Log, TEXT("[ModifyJsonBasedOnBitIndices] JSON file modified successfully: %s"), *JsonFilePath);
			return true;
		}
		else
		{
			CARLINX_LOG(LogCarLinx,Error, TEXT("[ModifyJsonBasedOnBitIndices] Failed to save modified JSON file: %s"), *JsonFilePath);
			return false;
		}
	}
	else
	{
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ModifyJsonBasedOnBitIndices] Failed to serialize modified JSON."));
		return false;
	}
}

bool UToolFunctionLibrary::CalculateMapIDsFromBitIndices(int32 InputValue, int32 ListName, TArray<int32>& OutMapIDs)
{

	// Step 2: 计算 bit 位为 1 的索引
	OutMapIDs.Empty();

	// 校验 ListName 是否在合法范围
	if (ListName < 1 || ListName > 4)
	{
		CARLINX_LOG(LogCarLinx,Warning, TEXT("[CalculateMapIDsFromBitIndices] Invalid ListName: %d"), ListName);
		return false;
	}

	// 计算 BaseOffset
	int32 BaseOffset = (ListName - 1) * 32;

	// 遍历 InputValue 的每一位
	for (int32 BitIndex = 0; BitIndex < 32; ++BitIndex)
	{
		if ((InputValue & (1 << BitIndex)) != 0) // 检查当前位是否为 1
		{
			int32 MapID = BaseOffset + BitIndex;
			OutMapIDs.Add(MapID);
			CARLINX_LOG(LogCarLinx,Log, TEXT("[CalculateMapIDsFromBitIndices] Found MapID: %d (ListName: %d, BitIndex: %d, BaseOffset: %d)"), MapID, ListName, BitIndex, BaseOffset);
		}
	}

	// 如果 MapIDs 为空，打印日志
	if (OutMapIDs.Num() == 0)
	{
		CARLINX_LOG(LogCarLinx,Warning, TEXT("[CalculateMapIDsFromBitIndices] No valid MapIDs found for InputValue: %d, ListName: %d"), InputValue, ListName);
		return false;
	}

	return true;
}

bool UToolFunctionLibrary::DeleteFolderByMapID(int32 MapID, const FString& ProjectPath)
{
	// Step 1: 拼接文件夹路径
	FString FolderPath = FPaths::Combine(ProjectPath, FString::Printf(TEXT("%d"), MapID));
	CARLINX_LOG(LogCarLinx,Log, TEXT("[DeleteFolderByMapID] Attempting to delete folder: %s"), *FolderPath);

	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	if (PlatformFile.DirectoryExists(*FolderPath))
	{
		// Step 2: 删除文件夹及其内容
		if (PlatformFile.DeleteDirectoryRecursively(*FolderPath))
		{
			CARLINX_LOG(LogCarLinx,Log, TEXT("[DeleteFolderByMapID] Successfully deleted folder: %s"), *FolderPath);
			return true;
		}
		else
		{
			CARLINX_LOG(LogCarLinx,Error, TEXT("[DeleteFolderByMapID] Failed to delete folder: %s"), *FolderPath);
			return false;
		}
	}
	else
	{
		CARLINX_LOG(LogCarLinx,Warning, TEXT("[DeleteFolderByMapID] Folder does not exist: %s"), *FolderPath);
		return false;
	}
}

bool UToolFunctionLibrary::ReadJsonFile(const FString& FilePath, TArray<int32>& OutData)
{

	// 读取 JSON 文件
	FString JsonString;
	UFileManager::SetCurrentFilePermissions(FilePath, false);
	if (!FFileHelper::LoadFileToString(JsonString, *FilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to load JSON file: %s"), *FilePath);
		return false;
	}

	// 解析 JSON
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON file"));
		return false;
	}

	// 清空数组
	OutData.Empty();

	// 遍历 List1 ~ List4 并合并成一个完整数组
	for (int ListIndex = 1; ListIndex <= 4; ListIndex++)
	{
		FString ListKey = FString::Printf(TEXT("List%d"), ListIndex);
		const TArray<TSharedPtr<FJsonValue>>* JsonArray;

		// 检查键是否存在，并转换为数组
		if (JsonObject->TryGetArrayField(ListKey, JsonArray))
		{
			for (const TSharedPtr<FJsonValue>& Value : *JsonArray)
			{
				OutData.Add(Value->AsNumber());
			}
		}
	}

	// 确保数据完整
	if (OutData.Num() != 128)
	{
		UE_LOG(LogTemp, Error, TEXT("Error: JSON data does not contain 128 elements!"));
		return false;
	}

	return true;
}

int32 UToolFunctionLibrary::GetFirstZeroIndexFromStart()
{
	TArray<int32> Data;
	if (!ReadJsonFile(JSON_FILE_PATH, Data))
	{
		return -1; // 解析失败，返回错误标志
	}

	// 遍历找到第一个 0
	for (int32 Index = 0; Index < Data.Num(); Index++)
	{
		if (Data[Index] == 0)
		{
			return Index;
		}
	}

	return -1; // 没找到 0
}

int32 UToolFunctionLibrary::GetFirstZeroIndexFromIndex50()
{
	TArray<int32> Data;
	if (!ReadJsonFile(JSON_FILE_PATH, Data))
	{
		return -1; // 解析失败，返回错误标志
	}

	// 从索引 50 开始查找
	for (int32 Index = 50; Index < Data.Num(); Index++)
	{
		if (Data[Index] == 0)
		{
			return Index;
		}
	}

	return -1; // 没找到 0
}

FString UToolFunctionLibrary::RemoveTrailingNumbers(const FString& InputString)
{
	CARLINX_LOG(LogCarLinx,Warning, TEXT("[HeGeLog] RemoveTrailingNumbers InputString: %s"), *InputString);
	FRegexPattern Pattern(TEXT("([^0-9]+)")); // 匹配前面的非数字部分
	FRegexMatcher Matcher(Pattern, InputString);

	if (Matcher.FindNext())
	{
		return Matcher.GetCaptureGroup(1); // 返回去掉数字的字符串
	}

	return InputString; // 如果没有匹配，返回原字符串
	CARLINX_LOG(LogCarLinx,Warning, TEXT("[HeGeLog] RemoveTrailingNumbers InputString Return: %s"), *InputString);
}

FString UToolFunctionLibrary::FindFirstPNGFile(const FString& FolderPath)
{
	FString NormalizedPath = FPaths::ConvertRelativePathToFull(FolderPath);

	CARLINX_LOG(LogTemp,Warning, TEXT("FindFirstPNGFile Input Path: %s"), *NormalizedPath);

	// 文件管理器
	IFileManager& FileManager = IFileManager::Get();

	// 文件查找通配符
	FString FilePattern = NormalizedPath / TEXT("*.png");

	TArray<FString> FoundFiles;
	FileManager.FindFiles(FoundFiles, *FilePattern, true, false); // 只查找文件，不查找文件夹

	if (FoundFiles.Num() > 0)
	{
		FString FirstPNGFilePath = NormalizedPath / FoundFiles[0]; // 拼接完整路径

		CARLINX_LOG(LogTemp,Warning, TEXT("Found PNG File: %s"), *FirstPNGFilePath);

		return FirstPNGFilePath;
	}

	CARLINX_LOG(LogTemp,Warning, TEXT("No PNG files found in the given directory."));
	return TEXT(""); // 没找到返回空字符串
}

bool UToolFunctionLibrary::SaveTimesToJsonFile(const FString& FathFlie, const FString& Times)
{

	
		

	// 创建JSON对象
	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
	JsonObject->SetStringField(TEXT("Times"), Times);

	// 将JSON对象转换为字符串
	FString OutputString;
	TSharedRef<TJsonWriter<>> JsonWriter = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(JsonObject.ToSharedRef(), JsonWriter);

	// 确保文件夹存在
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	if (!PlatformFile.DirectoryExists(*FathFlie))
	{
		PlatformFile.CreateDirectory(*FathFlie);
		if (!PlatformFile.DirectoryExists(*FathFlie))
		{
			CARLINX_LOG(LogCarLinx,Error, TEXT("Failed to create directory: %s"), *FathFlie);
			//CARLINX_LOG(LogCarLinx,Warning, TEXT("[DataSource] File does not exist: %s"), *FilePath);
			return false;
		}
	}

	// 构建文件路径
	FString FilePath = FathFlie / TEXT("Times.json");

	// 将JSON字符串写入文件
	if (FFileHelper::SaveStringToFile(OutputString, *FilePath))
	{
		CARLINX_LOG(LogCarLinx,Log, TEXT("Times saved successfully to %s"), *FilePath);
		return true;
	}
	else
	{
		CARLINX_LOG(LogCarLinx,Error, TEXT("Failed to save Times to %s"), *FilePath);
		return false;
	}





}

bool UToolFunctionLibrary::LoadTimesFromJsonFile(const FString& FathFlie, FString& OutTimes)
{       // 构建文件路径
	FString FilePath = FathFlie / TEXT("Times.json");

	// 读取文件内容
	FString FileContent;
	if (!FFileHelper::LoadFileToString(FileContent, *FilePath))
	{
		CARLINX_LOG(LogCarLinx,Error, TEXT("Failed to load file: %s"), *FilePath);
		return false;
	}

	// 解析JSON字符串
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(FileContent);
	if (!FJsonSerializer::Deserialize(JsonReader, JsonObject) || !JsonObject.IsValid())
	{
		CARLINX_LOG(LogCarLinx,Error, TEXT("Failed to parse JSON from file: %s"), *FilePath);
		return false;
	}

	// 获取Times字段
	if (JsonObject->TryGetStringField(TEXT("Times"), OutTimes))
	{
		CARLINX_LOG(LogCarLinx,Log, TEXT("Times loaded successfully: %s"), *OutTimes);
		return true;
	}
	else
	{
		CARLINX_LOG(LogCarLinx,Error, TEXT("Failed to get Times from JSON"));
		return false;
	}

}

void UToolFunctionLibrary::CalculateCenterAndDiameter(const TArray<FVector>& Points, FVector& OutCenter, float& OutDiameter)
{


	if (Points.Num() == 0) return;

	FVector MinBounds = Points[0];
	FVector MaxBounds = Points[0];

	// 遍历所有点，找到最小 & 最大坐标
	for (const FVector& Point : Points)
	{
		MinBounds.X = FMath::Min(MinBounds.X, Point.X);
		MinBounds.Y = FMath::Min(MinBounds.Y, Point.Y);
		MinBounds.Z = FMath::Min(MinBounds.Z, Point.Z);

		MaxBounds.X = FMath::Max(MaxBounds.X, Point.X);
		MaxBounds.Y = FMath::Max(MaxBounds.Y, Point.Y);
		MaxBounds.Z = FMath::Max(MaxBounds.Z, Point.Z);
	}

	// 计算中心点
	OutCenter = (MinBounds + MaxBounds) * 0.5f;

	// 计算直径（对角线长度）
	OutDiameter = FVector::Dist(MinBounds, MaxBounds);



}

FString UToolFunctionLibrary::ConvertSecondsToMinutes(int32 Seconds)
{
	// 将秒数转换为分钟数，保留一位小数
	double Minutes = Seconds / 60.0;

	// 四舍五入到小数点后一位
	Minutes = FMath::RoundToDouble(Minutes * 10) / 10.0;

	// 判断小数点后一位是否为0
	if (Minutes == FMath::FloorToDouble(Minutes))
	{
		// 如果是整数，返回整数
		return FString::Printf(TEXT("%d"), FMath::FloorToInt(Minutes));
	}
	else
	{
		// 否则返回带一位小数的字符串
		return FString::Printf(TEXT("%.1f"), Minutes);
	}

}

FVector UToolFunctionLibrary::CalculateCapturePosition(const TArray<FVector>& Points, float ZOffset, float& OutOrthoWidth)
{
	// 处理空数组情况
	if (Points.Num() == 0)
	{
		OutOrthoWidth = 0.0f;
		return FVector::ZeroVector;
	}

	// 初始化最小/最大值（使用第一个点作为初始值）
	FVector MinPoint = Points[0];
	FVector MaxPoint = Points[0];

	// 单次遍历计算包围盒
	for (const FVector& Point : Points)
	{
		// X轴最小值
		MinPoint.X = FMath::Min(MinPoint.X, Point.X);
		// Y轴最小值
		MinPoint.Y = FMath::Min(MinPoint.Y, Point.Y);
		// Z轴最小值
		MinPoint.Z = FMath::Min(MinPoint.Z, Point.Z);

		// X轴最大值
		MaxPoint.X = FMath::Max(MaxPoint.X, Point.X);
		// Y轴最大值
		MaxPoint.Y = FMath::Max(MaxPoint.Y, Point.Y);
		// Z轴最大值
		MaxPoint.Z = FMath::Max(MaxPoint.Z, Point.Z);
	}

	// 计算包围盒中心（X和Y方向）
	FVector Center;
	Center.X = (MinPoint.X + MaxPoint.X) * 0.5f; // X轴中心
	Center.Y = (MinPoint.Y + MaxPoint.Y) * 0.5f; // Y轴中心
	Center.Z = MaxPoint.Z + ZOffset;             // Z位置在最高点上方加偏移量

	// 计算包围盒尺寸
	const FVector Size = MaxPoint - MinPoint;

	// 取X/Y轴中较大的尺寸作为正交宽度
	OutOrthoWidth = FMath::Max(Size.X, Size.Y);

	// 返回计算得到的中心位置
	return Center;
}

bool UToolFunctionLibrary::SaveCameraData(const FString& Path, float Length, const FVector& Loc)
{
	FString FullPath = FPaths::Combine(Path, TEXT("Camera.json"));

	CARLINX_LOG(LogCarLinx, Error, TEXT("SaveCamera Path: %s"), *FullPath);
	if (UFileManager::FileExists(FullPath))
	{
		UFileManager::SetCurrentFilePermissions(FullPath, false);
	}
	// 构造 JSON 对象
	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
	JsonObject->SetNumberField(TEXT("Length"), Length);

	TSharedPtr<FJsonObject> LocationObject = MakeShareable(new FJsonObject);
	LocationObject->SetNumberField(TEXT("X"), Loc.X);
	LocationObject->SetNumberField(TEXT("Y"), Loc.Y);
	LocationObject->SetNumberField(TEXT("Z"), Loc.Z);
	JsonObject->SetObjectField(TEXT("Location"), LocationObject);

	// 序列化 JSON
	FString OutputString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	if (!FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer))
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to serialize JSON: %s"), *FullPath);
		return false;
	}

	// 写入文件
	if (!FFileHelper::SaveStringToFile(OutputString, *FullPath))
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to save file: %s"), *FullPath);
		return false;
	}

	
	CARLINX_LOG(LogCarLinx, Log, TEXT("Successfully saved Camera.json to: %s"), *FullPath);
	return true;
}

bool UToolFunctionLibrary::LoadCameraData(const FString& Path, float& OutLength, FVector& OutLoc)
{
	FString FullPath = FPaths::Combine(Path, TEXT("Camera.json"));
	FString JsonString;

	if (UFileManager::FileExists(FullPath))
	{
		UFileManager::SetCurrentFilePermissions(FullPath, false);
	}

	// 读取文件内容
	if (!FFileHelper::LoadFileToString(JsonString, *FullPath))
	{
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Failed to load file: %s"), *FullPath);
		return false;
	}

	if (JsonString.IsEmpty())
	{
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Skipping empty file: %s"), *FullPath);
		return false;
	}

	// 解析 JSON
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Failed to parse JSON: %s"), *FullPath);
		return false;
	}

	// 读取 Length
	if (!JsonObject->HasTypedField<EJson::Number>(TEXT("Length")))
	{
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Missing Length field in JSON: %s"), *FullPath);
		return false;
	}
	OutLength = JsonObject->GetNumberField(TEXT("Length"));

	// 读取 Location
	if (!JsonObject->HasTypedField<EJson::Object>(TEXT("Location")))
	{
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Missing Location field in JSON: %s"), *FullPath);
		return false;
	}

	TSharedPtr<FJsonObject> LocationObject = JsonObject->GetObjectField(TEXT("Location"));
	if (!LocationObject.IsValid())
	{
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Invalid Location object in JSON: %s"), *FullPath);
		return false;
	}

	if (!LocationObject->HasTypedField<EJson::Number>(TEXT("X")) ||
		!LocationObject->HasTypedField<EJson::Number>(TEXT("Y")) ||
		!LocationObject->HasTypedField<EJson::Number>(TEXT("Z")))
	{
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Invalid Location fields in JSON: %s"), *FullPath);
		return false;
	}

	OutLoc.X = LocationObject->GetNumberField(TEXT("X"));
	OutLoc.Y = LocationObject->GetNumberField(TEXT("Y"));
	OutLoc.Z = LocationObject->GetNumberField(TEXT("Z"));

	CARLINX_LOG(LogCarLinx, Log, TEXT("Successfully loaded Camera.json from: %s"), *FullPath);
	return true;

}

void UToolFunctionLibrary::SetBitsDeleteMaps(const TArray<int32>& InputNumbers, TArray<int32>& OutputArray)
{

	CARLINX_LOG(LogCarLinx, Log, TEXT("[SetBitsDeleteMaps]Successfully Begin"));
	// 打印输入的数组
	CARLINX_LOG(LogCarLinx,Log, TEXT("Input Numbers:"));
	for (int32 Num : InputNumbers)
	{
		CARLINX_LOG(LogCarLinx,Log, TEXT("  %d"), Num);
	}




	// 初始化 4 组 32 位二进制数（std::bitset<32>）
	TBitArray<> BitLists[4];

	// 预先分配 32 位，防止访问越界
	for (int i = 0; i < 4; i++)
	{
		BitLists[i].Add(false, 32);
	}

	CARLINX_LOG(LogCarLinx,Log, TEXT("Setting bits..."));

	// 遍历输入数组，设置对应位
	for (int32 Num : InputNumbers)
	{
		if (Num < 0 || Num >= 120)
		{
			CARLINX_LOG(LogCarLinx,Warning, TEXT("Invalid number: %d (out of range)"), Num);
			continue; // 跳过非法输入
		}

		int32 Group = Num / 32;  // 确定属于哪一组
		int32 Pos = Num % 32;    // 确定在该组的哪一位

		CARLINX_LOG(LogCarLinx,Log, TEXT("  Setting bit at Group: %d, Position: %d"), Group, Pos);

		BitLists[Group][Pos] = true; // 设置该位
	}

	// 转换为 int32 数组
	OutputArray.SetNumUninitialized(4);

	for (int32 i = 0; i < 4; i++)
	{
		int32 BitValue = 0;
		for (int32 j = 0; j < 32; j++)
		{
			if (BitLists[i][j])
			{
				BitValue |= (1 << j);
			}
		}
		OutputArray[i] = BitValue;

		// 打印二进制表示
		FString BinaryString;
		for (int k = 31; k >= 0; k--)
		{
			BinaryString += (BitLists[i][k] ? TEXT("1") : TEXT("0"));
		}

		CARLINX_LOG(LogCarLinx,Log, TEXT("  List%d: %d (Binary: %s)"), i + 1, OutputArray[i], *BinaryString);
	}
}

float UToolFunctionLibrary::CalculateSpringArmLength(float MaxDistance, float VerticalFOV, float AspectRatio)
{
	// 将垂直 FOV 转换为弧度
	float VerticalFOVRadians = FMath::DegreesToRadians(VerticalFOV);
	CARLINX_LOG(LogCarLinx,Warning, TEXT("[HPAMeng]VerticalFOVRadians: %f"), VerticalFOVRadians);
	// 计算水平 FOV（弧度）
	float HorizontalFOVRadians = 2 * FMath::Atan(FMath::Tan(VerticalFOVRadians / 2) * AspectRatio);
	CARLINX_LOG(LogCarLinx,Warning, TEXT("[HPAMeng]HorizontalFOVRadians: %f"), HorizontalFOVRadians);
	// 计算 TargetArmLength
	float TargetArmLength = MaxDistance / FMath::Tan(HorizontalFOVRadians / 2);
	CARLINX_LOG(LogCarLinx,Warning, TEXT("[HPAMeng]TargetArmLength: %f"), TargetArmLength);
	return TargetArmLength;
}

float UToolFunctionLibrary::CalculateTargetArmLength(FVector MapMin, FVector MapMax, FVector CenterPos, FRotator SpringArmRotation, float FOV, float ScreenAspectRatio,float ArmLengthOffest)
{
	//生成地图包围盒的8个顶点
	TArray<FVector> MapCorners;
	MapCorners.Add(FVector(MapMin.X, MapMin.Y, MapMin.Z));
	MapCorners.Add(FVector(MapMin.X, MapMin.Y, MapMax.Z));
	MapCorners.Add(FVector(MapMin.X, MapMax.Y, MapMin.Z));
	MapCorners.Add(FVector(MapMin.X, MapMax.Y, MapMax.Z));
	MapCorners.Add(FVector(MapMax.X, MapMin.Y, MapMin.Z));
	MapCorners.Add(FVector(MapMax.X, MapMin.Y, MapMax.Z));
	MapCorners.Add(FVector(MapMax.X, MapMax.Y, MapMin.Z));
	MapCorners.Add(FVector(MapMax.X, MapMax.Y, MapMax.Z));

	//构建SpringArm的变换矩阵
	const FQuat RotationQuat = SpringArmRotation.Quaternion();
	const FTransform SpringArmTransform(RotationQuat, CenterPos);

	//将顶点转换到局部坐标系
	TArray<FVector> LocalCorners;
	for (const FVector& Corner : MapCorners) {
		LocalCorners.Add(SpringArmTransform.InverseTransformPosition(Corner));
	}

	//计算局部坐标系中的水平（Y轴）和垂直（Z轴）跨度
	FVector LocalMin(FLT_MAX, FLT_MAX, FLT_MAX);
	FVector LocalMax(-FLT_MAX, -FLT_MAX, -FLT_MAX);
	for (const FVector& Corner : LocalCorners) {
		LocalMin.X = FMath::Min(LocalMin.X, Corner.X);
		LocalMin.Y = FMath::Min(LocalMin.Y, Corner.Y);
		LocalMin.Z = FMath::Min(LocalMin.Z, Corner.Z);
		LocalMax.X = FMath::Max(LocalMax.X, Corner.X);
		LocalMax.Y = FMath::Max(LocalMax.Y, Corner.Y);
		LocalMax.Z = FMath::Max(LocalMax.Z, Corner.Z);
	}

	//计算水平方向（基于Y轴跨度）
	const float HorizontalSpan = LocalMax.Y - LocalMin.Y;
	const float HalfHorizontalFOV = FMath::DegreesToRadians(FOV) / 2.0f;
	const float RequiredHorizontalLength = (HorizontalSpan / 2.0f) / FMath::Tan(HalfHorizontalFOV);

	//计算垂直方向（基于Z轴跨度和推导的垂直FOV）
	const float VerticalSpan = LocalMax.Z - LocalMin.Z;
	const float VerticalFOV = 2.0f * FMath::Atan(FMath::Tan(HalfHorizontalFOV) / ScreenAspectRatio);
	const float RequiredVerticalLength = (VerticalSpan / 2.0f) / FMath::Tan(VerticalFOV / 2.0f);

	//返回最大值并乘以补偿值
	return FMath::Max(RequiredHorizontalLength, RequiredVerticalLength)* ArmLengthOffest;
}

float UToolFunctionLibrary::CalculateTargetArmLengthByDis(float MaxDistance, FVector CenterPos, FTransform SpringArmLocalTransform, FTransform ParentWorldTransform, float FOV, float ScreenRatio,float ArmLengthOffest)
{
	//生成地图的MapMin和MapMax（假设为立方体，各轴跨度相同）
	const FVector HalfL(MaxDistance / 2.0f, MaxDistance / 2.0f, MaxDistance / 2.0f);
	const FVector MapMin = CenterPos - HalfL;
	const FVector MapMax = CenterPos + HalfL;

	//生成地图包围盒的8个顶点
	TArray<FVector> MapCorners;
	MapCorners.Add(FVector(MapMin.X, MapMin.Y, MapMin.Z));
	MapCorners.Add(FVector(MapMin.X, MapMin.Y, MapMax.Z));
	MapCorners.Add(FVector(MapMin.X, MapMax.Y, MapMin.Z));
	MapCorners.Add(FVector(MapMin.X, MapMax.Y, MapMax.Z));
	MapCorners.Add(FVector(MapMax.X, MapMin.Y, MapMin.Z));
	MapCorners.Add(FVector(MapMax.X, MapMin.Y, MapMax.Z));
	MapCorners.Add(FVector(MapMax.X, MapMax.Y, MapMin.Z));
	MapCorners.Add(FVector(MapMax.X, MapMax.Y, MapMax.Z));

	//计算SpringArm的世界变换（包含父组件和局部旋转）
	
	const FTransform WorldTransform = SpringArmLocalTransform * ParentWorldTransform;

	//将顶点转换到SpringArm的局部坐标系
	TArray<FVector> LocalCorners;
	for (const FVector& Corner : MapCorners) {
		LocalCorners.Add(WorldTransform.InverseTransformPosition(Corner));
	}

	//计算局部坐标系中的水平（Y轴）和垂直（Z轴）跨度（仅处理前方顶点）
	FVector LocalMin(FLT_MAX, FLT_MAX, FLT_MAX);
	FVector LocalMax(-FLT_MAX, -FLT_MAX, -FLT_MAX);
	for (const FVector& Corner : LocalCorners) {
		if (Corner.X > 0) { // 过滤后方顶点
			LocalMin.Y = FMath::Min(LocalMin.Y, Corner.Y);
			LocalMin.Z = FMath::Min(LocalMin.Z, Corner.Z);
			LocalMax.Y = FMath::Max(LocalMax.Y, Corner.Y);
			LocalMax.Z = FMath::Max(LocalMax.Z, Corner.Z);
		}
	}

	//计算水平方向需求（基于Y轴跨度）
	const float HorizontalSpan = LocalMax.Y - LocalMin.Y;
	const float HalfHorizontalFOV = FMath::DegreesToRadians(FOV) / 2.0f;
	const float RequiredHorizontalLength = (HorizontalSpan / 2.0f) / FMath::Tan(HalfHorizontalFOV);

	//计算垂直方向需求（基于Z轴跨度和推导的垂直FOV）
	const float VerticalSpan = LocalMax.Z - LocalMin.Z;
	const float VerticalFOV = 2.0f * FMath::Atan(FMath::Tan(HalfHorizontalFOV) / ScreenRatio);
	const float RequiredVerticalLength = (VerticalSpan / 2.0f) / FMath::Tan(VerticalFOV / 2.0f);
	//取最大值并乘以补偿值确保全覆盖
	float TargetArmLength = FMath::Max(RequiredHorizontalLength, RequiredVerticalLength) * ArmLengthOffest;

	return TargetArmLength;
}





