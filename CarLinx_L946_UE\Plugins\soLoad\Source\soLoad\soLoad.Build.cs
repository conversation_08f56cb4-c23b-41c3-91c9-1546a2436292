// Some copyright should be here...
using System.IO;
using UnrealBuildTool;

using Tools.DotNETCommon;

public class soLoad : ModuleRules
{
	public soLoad(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		bEnableUndefinedIdentifierWarnings = false;
		
		string EngineDir = Path.GetFullPath(Target.RelativeEnginePath);
		
		PublicIncludePaths.AddRange(
			new string[] {
				ModuleDirectory + "/Public",
				ModuleDirectory + "/Public/Generated",
				Path.Combine(EngineDir, "Source/Runtime/Core/Public"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Misc"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Modules"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Containers"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/HAL"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Templates"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Math"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/GenericPlatform"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Windows"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Async"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Serialization"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Logging"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Internationalization"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Stats"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Memory"),
				Path.Combine(EngineDir, "Source/Runtime/Core/Public/Delegates"),
				Path.Combine(EngineDir, "Source/Runtime/CoreUObject/Public"),
				Path.Combine(EngineDir, "Source/Runtime/CoreUObject/Public/UObject"),
				Path.Combine(EngineDir, "Source/Runtime/Engine/Classes"),
				Path.Combine(EngineDir, "Source/Runtime/Engine/Public"),
				Path.Combine(EngineDir, "Source/Runtime/Networking/Public"),
				Path.Combine(EngineDir, "Source/Runtime/Sockets/Public"),
				Path.Combine(EngineDir, "Source/Runtime/SlateCore/Public"),
				Path.Combine(EngineDir, "Source/Runtime/Slate/Public")
			}
		);
		
		PrivateIncludePaths.AddRange(
			new string[] {
				ModuleDirectory + "/Private",
				Path.Combine(EngineDir, "Source/Runtime/Core/Private"),
				Path.Combine(EngineDir, "Source/Runtime/CoreUObject/Private"),
				Path.Combine(EngineDir, "Source/Runtime/Engine/Private")
			}
		);
		
		PublicDependencyModuleNames.AddRange(
			new string[] {
				"Core",
				"CoreUObject",
				"Engine",
				"InputCore",
				"Slate",
				"SlateCore",
				"Networking",
				"Sockets",
				"RenderCore",
				"RHI",
				"Json",
				"JsonUtilities"

			}
		);
		
		PrivateDependencyModuleNames.AddRange(
			new string[] {
				"CoreUObject",
				"Engine",
				"Slate",
				"SlateCore",
				"Networking",
				"Sockets",
				"RenderCore",
				"RHI"
			}
		);
		
		PrivatePCHHeaderFile = "Public/soLoad.h";
		
		System.IO.Directory.CreateDirectory(Path.Combine(ModuleDirectory, "Public/Generated"));

		// 添加预处理器定义
		PublicDefinitions.AddRange(
			new string[] {
				"WITH_ENGINE=1",
				"_CRT_SECURE_NO_WARNINGS"
			}
		);

		if (Target.bBuildEditor)
		{
			PublicDefinitions.Add("WIN32=1");
			PublicDefinitions.Add("WITH_EDITOR=1");
			// PublicDefinitions.Add("WITH_PLUGIN_SUPPORT=1");
		}
		else
		{
			PublicDefinitions.Add("WITH_EDITOR=0");
			// PublicDefinitions.Add("WITH_UNREAL_DEVELOPER_TOOLS=0");
			// PublicDefinitions.Add("WITH_PLUGIN_SUPPORT=0");
		}

		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
		);
	}
}
