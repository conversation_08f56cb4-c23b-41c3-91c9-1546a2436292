#include "ParkingDataProcessor2UE.h"
#include "Logger.h"
#include "DataTypes.h"
#include "GlobalConfig.h"
#include "Logger.h"

// 静态成员变量定义
bool ParkingDataProcessor2UEInternal::bIsRecording = false;
bool ParkingDataProcessor2UEInternal::bHasRecordedData = false;

FVector ParkingDataProcessor2UEInternal::MinRange = FVector(MAX_FLT, MAX_FLT, MAX_FLT);
FVector ParkingDataProcessor2UEInternal::MaxRange = FVector(-MAX_FLT, -MAX_FLT, -MAX_FLT);

bool ParkingDataProcessor2UEInternal::bNearSmallOffset = false;
float ParkingDataProcessor2UEInternal::RecordingTraceOffset = 0.0f;
int ParkingDataProcessor2UEInternal::TraceID = 0;

TMap<int32, FParkingSlotData> ParkingDataProcessor2UEInternal::SlotHistory;
bool ParkingDataProcessor2UEInternal::bIsRecordingSlots = false;

// 蓝图接口实现
void UParkingDataProcessor2UE::StartRecordingRange()
{
    ParkingDataProcessor2UEInternal::StartRecordingRange();
}

bool UParkingDataProcessor2UE::GetRangeInfo(FVector& OutCenter, float& OutDistance)
{
    return ParkingDataProcessor2UEInternal::GetRangeInfo(OutCenter, OutDistance);
}

void UParkingDataProcessor2UE::StartRecordingSlots()
{
    ParkingDataProcessor2UEInternal::StartRecordingSlots();
}

void UParkingDataProcessor2UE::ClearSlotHistory()
{
    ParkingDataProcessor2UEInternal::ClearSlotHistory();
}

void UParkingDataProcessor2UE::GetSlotHistory(TMap<int32, FParkingSlotData>& OutSlotHistory)
{
    OutSlotHistory = ParkingDataProcessor2UEInternal::GetSlotHistory();
}

// 内部类实现
void ParkingDataProcessor2UEInternal::StartRecordingRange()
{
    bIsRecording = true;
    bHasRecordedData = false;
    MinRange = FVector(0, 0, 0);
    MaxRange = FVector(0, 0, 0);
}

void ParkingDataProcessor2UEInternal::StartRecordingSlots()
{
    bIsRecordingSlots = true;
    ClearSlotHistory();
}

void ParkingDataProcessor2UEInternal::ClearSlotHistory()
{
    SlotHistory.Empty();
}

const TMap<int32, FParkingSlotData>& ParkingDataProcessor2UEInternal::GetSlotHistory()
{
    return SlotHistory;
}

void ParkingDataProcessor2UEInternal::ProcessCarRange(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& selfData)
{
    if (!bIsRecording)
    {
        return;
    }

    float x = float(selfData.vehPosn__.vehPosnX__);
    float y = float(selfData.vehPosn__.vehPosnY__) * (-1.0f);
    float z = float(selfData.vehPosn__.vehPosnZ__);

    MinRange.X = FMath::Min(MinRange.X, x);
    MinRange.Y = FMath::Min(MinRange.Y, y);
    MinRange.Z = FMath::Min(MinRange.Z, z);

    MaxRange.X = FMath::Max(MaxRange.X, x);
    MaxRange.Y = FMath::Max(MaxRange.Y, y);
    MaxRange.Z = FMath::Max(MaxRange.Z, z);

    CARLINX_LOG(LogCarLinx, Warning, TEXT("Camera MinRange value x=%f, y=%f, z=%f"), MinRange.X, MinRange.Y, MinRange.Z);
    CARLINX_LOG(LogCarLinx, Warning, TEXT("Camera MaxRange value x=%f, y=%f, z=%f"), MaxRange.X, MaxRange.Y, MaxRange.Z);

    bHasRecordedData = true;
}

bool ParkingDataProcessor2UEInternal::GetRangeInfo(FVector& OutCenter, float& OutDistance)
{
    if (!bHasRecordedData)
    {
        return false;
    }

    // 计算中心点
    OutCenter = (MinRange + MaxRange) * 0.5f;
    
    // 计算最大距离（使用三维空间中两点之间的距离公式）
    OutDistance = FVector::Distance(MaxRange, MinRange);

    // 停止记录
    bIsRecording = false;

    return true;
}

bool ParkingDataProcessor2UEInternal::UpdateMapValue(TMap<FString, FString>& Map, const FString& Key, const FString& Value) {
    bool result = false;
    if (!Map.Contains(Key) || Map[Key] != Value) {
        Map.Add(Key, Value);
        result = true;
    }
    return result;
}

FString ParkingDataProcessor2UEInternal::FloatToString(float Value) {
    FString result = FString::Printf(TEXT("%.2f"), Value);
    return result;
}

FString ParkingDataProcessor2UEInternal::Int32ToString(int32 Value) {
    FString result = FString::Printf(TEXT("%d"), Value);
    return result;
}

FString ParkingDataProcessor2UEInternal::UInt8ToString(uint8_t Value) {
    FString result = FString::Printf(TEXT("%u"), Value);
    return result;
}

FString ParkingDataProcessor2UEInternal::BoolToString(bool Value) {
    FString result = Value ? TEXT("true") : TEXT("false");
    return result;
}

bool ParkingDataProcessor2UEInternal::DeserializeSelfCarData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& selfData, 
                                                TMap<FString, FString>& SelfCarMap) {
    // CARLINX_LOG(LogCarLinx, Log, TEXT("Begin"));
    bool bHasChanged = false;
    
    // 处理范围记录
    ProcessCarRange(selfData);
    
    bHasChanged |= UpdateMapValue(SelfCarMap, TEXT("vehHeadingAg.roll"), Int32ToString(selfData.vehHeadingAg__.roll__));
    bHasChanged |= UpdateMapValue(SelfCarMap, TEXT("vehHeadingAg.pitch"), Int32ToString(selfData.vehHeadingAg__.pitch__));
    bHasChanged |= UpdateMapValue(SelfCarMap, TEXT("vehHeadingAg.yaw"), Int32ToString(selfData.vehHeadingAg__.yaw__));
    
    bHasChanged |= UpdateMapValue(SelfCarMap, TEXT("vehWave1"), UInt8ToString(static_cast<uint8_t>(selfData.vehWave1__)));
    
    bHasChanged |= UpdateMapValue(SelfCarMap, TEXT("vehArrowhead.vehArrowheadDirection"), 
                                 UInt8ToString(static_cast<uint8_t>(selfData.vehArrowhead__.vehArrowheadDirection__)));
    bHasChanged |= UpdateMapValue(SelfCarMap, TEXT("vehArrowhead.vehArrowheadRoll"), 
                                 Int32ToString(selfData.vehArrowhead__.vehArrowheadRoll__));
    
    bHasChanged |= UpdateMapValue(SelfCarMap, TEXT("vehPosn.vehPosnX"), Int32ToString(selfData.vehPosn__.vehPosnX__));
    bHasChanged |= UpdateMapValue(SelfCarMap, TEXT("vehPosn.vehPosnY"), Int32ToString(selfData.vehPosn__.vehPosnY__));
    bHasChanged |= UpdateMapValue(SelfCarMap, TEXT("vehPosn.vehPosnZ"), Int32ToString(selfData.vehPosn__.vehPosnZ__));
    
    if (bHasChanged) {
        CARLINX_LOG(LogCarLinx, Log, TEXT("Self Car Position Changed - X: %d, Y: %d, Z: %d"), 
            selfData.vehPosn__.vehPosnX__,
            selfData.vehPosn__.vehPosnY__,
            selfData.vehPosn__.vehPosnZ__);
    }

    return bHasChanged;
}

bool ParkingDataProcessor2UEInternal::DeserializeSlotData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot& slotData,
                                             TMap<FString, FString>& SlotMap) {
    // CARLINX_LOG(LogCarLinx, Log, TEXT("Begin"));
    /*if (slotData.slotID__ == 0) {
        return false;
    }*/
    bool bHasChanged = false;
    
    // 基本信息
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotID"), Int32ToString(slotData.slotID__));
    
    // SlotTyp
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.slotYawAg"), Int32ToString(slotData.slotTyp__.slotYawAg__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.slotrollAg"), Int32ToString(slotData.slotTyp__.slotrollAg__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.slotpitchAg"), Int32ToString(slotData.slotTyp__.slotpitchAg__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.obiqueSlotAg"), Int32ToString(slotData.slotTyp__.obiqueSlotAg__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.slotTypinfo"), UInt8ToString(slotData.slotTyp__.slotTypinfo__));
    
    // SlotBounding
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotStart.posnX"), Int32ToString(slotData.slotBounding__.slotStart__.posnX__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotStart.posnY"), Int32ToString(slotData.slotBounding__.slotStart__.posnY__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotStart.posnZ"), Int32ToString(slotData.slotBounding__.slotStart__.posnZ__));
    
    // End
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotEnd.posnX"), Int32ToString(slotData.slotBounding__.slotEnd__.posnX__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotEnd.posnY"), Int32ToString(slotData.slotBounding__.slotEnd__.posnY__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotEnd.posnZ"), Int32ToString(slotData.slotBounding__.slotEnd__.posnZ__));
    
    // StartRear
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotStartRear.posnX"), Int32ToString(slotData.slotBounding__.slotStartRear__.posnX__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotStartRear.posnY"), Int32ToString(slotData.slotBounding__.slotStartRear__.posnY__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotStartRear.posnZ"), Int32ToString(slotData.slotBounding__.slotStartRear__.posnZ__));
    
    // EndRear
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotEndRear.posnX"), Int32ToString(slotData.slotBounding__.slotEndRear__.posnX__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotEndRear.posnY"), Int32ToString(slotData.slotBounding__.slotEndRear__.posnY__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotBounding.slotEndRear.posnZ"), Int32ToString(slotData.slotBounding__.slotEndRear__.posnZ__));
    
    // SlotSt
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotSt.slotSts1"), UInt8ToString(static_cast<uint8_t>(slotData.slotSt__.slotSts1__)));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotSt.slotNumber"), UInt8ToString(static_cast<uint8_t>(slotData.slotSt__.slotNumber__)));
    
    // SlotCod
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotCod.slotCod1"), UInt8ToString(slotData.slotCod__.slotCod1__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotCod.slotCod2"), UInt8ToString(slotData.slotCod__.slotCod2__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotCod.slotCod3"), UInt8ToString(slotData.slotCod__.slotCod3__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotCod.slotCod4"), UInt8ToString(slotData.slotCod__.slotCod4__));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotCod.slotCod5"), UInt8ToString(slotData.slotCod__.slotCod5__));
    

    // 处理PrkgSlotEvt信号的各个bit位
    int8_t obiqueSlotAg = slotData.slotTyp__.obiqueSlotAg__;

    // BIT0: 车位是否有轮挡 (0:没有 1:有)
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.obiqueSlotAg.hasWheelBlock"), 
                                 UInt8ToString((obiqueSlotAg & 0x01) != 0));
    
    // BIT1: 轮挡所在车位位置 (0:无 1:车位前方)
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.obiqueSlotAg.wheelBlockFront"), 
                                 UInt8ToString((obiqueSlotAg & 0x02) != 0));
    
    // BIT2: 轮挡所在车位位置 (0:无 1:车位后方)
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.obiqueSlotAg.wheelBlockRear"), 
                                 UInt8ToString((obiqueSlotAg & 0x04) != 0));
    
    // BIT3: 车位是否有地锁 (0:没有 1:有)
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.obiqueSlotAg.hasGroundLock"), 
                                 UInt8ToString((obiqueSlotAg & 0x08) != 0));
    
    // BIT4: 地锁状态 (0:关闭 1:打开)
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.obiqueSlotAg.groundLockStatus"), 
                                 UInt8ToString((obiqueSlotAg & 0x10) != 0));
    
    // BIT5-7: Reserve位
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.obiqueSlotAg.reserve1"), 
                                 UInt8ToString((obiqueSlotAg & 0x20) != 0));
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.obiqueSlotAg.reserve2"), 
                                 UInt8ToString((obiqueSlotAg & 0x40) != 0)); 
    bHasChanged |= UpdateMapValue(SlotMap, TEXT("slotTyp.obiqueSlotAg.reserve3"), 
                                 UInt8ToString((obiqueSlotAg & 0x80) != 0));

    /*CARLINX_LOG(LogCarLinx, Log, TEXT("SlotID: %d, hasWheelBlock: %d, wheelBlockFront: %d, wheelBlockRear: %d, hasGroundLock:%d, groundLockStatus:%d, obiqueSlotAg: 0x%02X"),
        slotData.slotID__, (obiqueSlotAg & 0x01) != 0, (obiqueSlotAg & 0x02) != 0, (obiqueSlotAg & 0x04) != 0, (obiqueSlotAg & 0x08) != 0, (obiqueSlotAg & 0x10) != 0, obiqueSlotAg);*/

    // CARLINX_LOG(LogCarLinx, Log, TEXT("End"));

     // 如果正在记录车位且车位ID不为0，则记录车位数据
    if (bHasChanged && UGlobalConfig::GetInstance()->GetCurrentState() == EParkingState::HPA) {
        ProcessSlotData(slotData);
    }

    return bHasChanged;
}

bool ParkingDataProcessor2UEInternal::DeserializeObstacleData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle& obstacleData,
                                                 TMap<FString, FString>& ObstacleMap) {

	/*if (obstacleData.objID__ == 0) {
        return false;
    }*/
	auto processedData = obstacleData;
    // 如果障碍物是柱子或者车，则进行滤波
    /*if(obstacleData.objTyp__ == 1 || obstacleData.objTyp__ == 10 || obstacleData.objTyp__ == 15) {
        processedData = FilterAlgorithm::Filter(obstacleData, FilterAlgorithm::FilterType::MovingAverage);
    }*/
    // CARLINX_LOG(LogCarLinx, Log, TEXT("Begin"));
    bool bHasChanged = false;
    
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objSpd"), Int32ToString(processedData.objSpd__));
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objTyp"), Int32ToString(processedData.objTyp__));
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objID"), Int32ToString(processedData.objID__));
    
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objPosn.posnX"), Int32ToString(processedData.objPosn__.posnX__));
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objPosn.posnY"), Int32ToString(processedData.objPosn__.posnY__));
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objPosn.posnZ"), Int32ToString(processedData.objPosn__.posnZ__));
    
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objHeading.roll"), Int32ToString(processedData.objHeading__.roll__));
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objHeading.pitch"), Int32ToString(processedData.objHeading__.pitch__));
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objHeading.yaw"), Int32ToString(processedData.objHeading__.yaw__));
    
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objLi"), Int32ToString(processedData.objLi__));
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objdoor"), Int32ToString(processedData.objdoor__));
    bHasChanged |= UpdateMapValue(ObstacleMap, TEXT("objColor"), UInt8ToString(static_cast<uint8_t>(processedData.objColor__)));
    
    // CARLINX_LOG(LogCarLinx, Log, TEXT("End"));
    return bHasChanged;
}

bool ParkingDataProcessor2UEInternal::DeserializeRoadPointData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint& pointData,
                                                  TMap<FString, FString>& RoadPointMap) {
    // CARLINX_LOG(LogCarLinx, Log, TEXT("Begin"));
    bool bHasChanged = false;
    
    bHasChanged |= UpdateMapValue(RoadPointMap, TEXT("sts"), UInt8ToString(static_cast<uint8_t>(pointData.sts__)));
    bHasChanged |= UpdateMapValue(RoadPointMap, TEXT("pointX"), Int32ToString(pointData.pointX__));
    bHasChanged |= UpdateMapValue(RoadPointMap, TEXT("pointY"), Int32ToString(pointData.pointY__));
    bHasChanged |= UpdateMapValue(RoadPointMap, TEXT("pointZ"), Int32ToString(pointData.pointZ__));
    
    // CARLINX_LOG(LogCarLinx, Log, TEXT("End"));
    return bHasChanged;
}

bool ParkingDataProcessor2UEInternal::DeserializeTrackPlanningData(const gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning& planningData,
                                                      TMap<FString, FString>& TrackPlanningMap) {
    // CARLINX_LOG(LogCarLinx, Log, TEXT("Begin"));
    bool bHasChanged = false;
    
    bHasChanged |= UpdateMapValue(TrackPlanningMap, TEXT("ttrackPlanningX"), Int32ToString(planningData.ttrackPlanningX__));
    bHasChanged |= UpdateMapValue(TrackPlanningMap, TEXT("ttrackPlanningY"), Int32ToString(planningData.ttrackPlanningY__));
    bHasChanged |= UpdateMapValue(TrackPlanningMap, TEXT("sTS"), UInt8ToString(planningData.sTS__));
    
    // CARLINX_LOG(LogCarLinx, Log, TEXT("End"));
    return bHasChanged;
}

bool ParkingDataProcessor2UEInternal::DeserializeTrackPlanningData(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& left,
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& right, const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& selfData,
    TArray<FSTCTRACK>& TrackPlanningPoints, int& LineType)
{
    TrackPlanningPoints.Empty();
    bool bHasValidPoints = false;

    FSTCTRACK result;

    TrackPlanningPoints.Reserve(270);

    FVector TempStartPoint;
    FVector TempEndPoint;

    for (int i = 0; i < left.size(); i++)
    {
        //精度为dm，转到UE里需要*10化为cm。坐标系的转换需要Y轴取反
        result.PlanningPoint = FVector(float((left[i].ttrackPlanningX__ - 32768)), float((left[i].ttrackPlanningY__ - 32768) * -1), float(selfData.vehPosn__.vehPosnZ__));

        //转到UE里涉及到坐标系转换，因此需要乘以 -1 
        result.PointRotation = (float(right[i].ttrackPlanningY__) * 0.0000976563f - 3.2) * 180 / -3.1415926535f;

        result.PointGroupID = int32(right[i].ttrackPlanningX__) - 32768;

        result.MoveType = right[i].sTS__;

        result.TraceID = TraceID;

        //轨迹点状态值在1到4范围内，判断为有效数据
        if (left[i].sTS__ < 1 || left[i].sTS__ > 4)
        {
            LineType = 0;
            continue;
        }
        else
        {
            if (TrackPlanningPoints.Num() == 0)
            {
                TempStartPoint = result.PlanningPoint;
            }

            TrackPlanningPoints.Emplace(result);
            if (left[i].sTS__ == 2 || left[i].sTS__ == 4)
            {
              //  if (UGlobalConfig::GetInstance()->GetCurrentState() != EParkingState::HPA)//新添加
            //    {
                
                    LineType = left[i].sTS__ / 2;
                
               // }
                //轨迹是多步还是单步信息传到外部做停止线逻辑
                bHasValidPoints = true;
                TempEndPoint = result.PlanningPoint;
                TraceID = CheckTraceID(50, 100, TempStartPoint, TempEndPoint);
                //CARLINX_LOG(LogCarLinx,Log, TEXT("HPA ParkingTrace EndX: %f"), TempEndPoint.X);
                break;
            }
        }
    }
    return bHasValidPoints;
}

bool ParkingDataProcessor2UEInternal::DeserializeHpaRoadPoint(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& Left,
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& Right, TArray<FRoadPoint>& HPARoadPoints)
{

    FRoadPoint Temp;
    static int32 HPARoadFlag = 0;
    if (Right[0].pointY__ <= Right[0].pointZ__ && Right[0].pointZ__ >= 1 && Right[0].pointY__ >= 1 && CheckBitStatus(HPARoadFlag) != Right[0].pointY__)
    {
        HPARoadFlag |= 1 << (Right[0].pointY__ - 1);
        //CARLINX_LOG(LogCarLinx,Log, TEXT("HPA Road Point [DeserizlizeHpaRoadPoint] Flag: %d"), HPARoadFlag);

        for (int i = 0; i < Left.size(); i++)
        {
            Temp.PointLocation = FVector(float((Left[i].pointX__ - 32768)) * 10.0f, float((Left[i].pointY__ - 32768)) * (-10.0f), float((Left[i].pointZ__ - 127)) * 10.0f);
            Temp.Type = static_cast<int>(Left[i].sts__);
            Temp.Width = (Right[i].pointX__ - 32768) * 10;	//dm 转  cm
            Temp.FrameID = Right[i].pointY__;
            Temp.TotalFrameNum = Right[i].pointZ__;
            Temp.PointSts = static_cast<int>(Right[i].sts__);

            if (static_cast<int>(Left[i].sts__) != 0)
            {
                HPARoadPoints.Add(Temp);
            }
        }
    }
    if (CheckBitStatus(HPARoadFlag) == Right[0].pointZ__ && Right[0].pointZ__ != 0 && Right[0].pointZ__ == Right[0].pointY__)
    {
        HPARoadFlag = 0;
        UGlobalConfig::GetInstance()->DisableHPACreatingFlag();
        return true;
    }
    else
    {
        return false;
    }
}

bool ParkingDataProcessor2UEInternal::DeserializeTrafficSignData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgTrafficsign& signData,
                                                    TMap<FString, FString>& TrafficSignMap) {
    // CARLINX_LOG(LogCarLinx, Log, TEXT("Begin"));
    bool bHasChanged = false;
    
    bHasChanged |= UpdateMapValue(TrafficSignMap, TEXT("trafficSignID"), Int32ToString(signData.trafficSignID__));
    bHasChanged |= UpdateMapValue(TrafficSignMap, TEXT("trafficSignTyp"), UInt8ToString(static_cast<uint8_t>(signData.trafficSignTyp__)));
    bHasChanged |= UpdateMapValue(TrafficSignMap, TEXT("trafficSignColour"), UInt8ToString(static_cast<uint8_t>(signData.trafficSignColour__)));
    
    bHasChanged |= UpdateMapValue(TrafficSignMap, TEXT("trafficSignPosn.posnX"), Int32ToString(signData.trafficSignPosn__.posnX__));
    bHasChanged |= UpdateMapValue(TrafficSignMap, TEXT("trafficSignPosn.posnY"), Int32ToString(signData.trafficSignPosn__.posnY__));
    bHasChanged |= UpdateMapValue(TrafficSignMap, TEXT("trafficSignPosn.posnZ"), Int32ToString(signData.trafficSignPosn__.posnZ__));
    
    bHasChanged |= UpdateMapValue(TrafficSignMap, TEXT("trafficSignHeadingAg.roll"), Int32ToString(signData.trafficSignHeadingAg__.roll__));
    bHasChanged |= UpdateMapValue(TrafficSignMap, TEXT("trafficSignHeadingAg.pitch"), Int32ToString(signData.trafficSignHeadingAg__.pitch__));
    bHasChanged |= UpdateMapValue(TrafficSignMap, TEXT("trafficSignHeadingAg.yaw"), Int32ToString(signData.trafficSignHeadingAg__.yaw__));
    
    // CARLINX_LOG(LogCarLinx, Log, TEXT("End"));
    return bHasChanged;
}

bool ParkingDataProcessor2UEInternal::DeserializePerspectiveMode(uint8_t mode, TMap<FString, FString>& PerspectiveMode) {
    // CARLINX_LOG(LogCarLinx, Log, TEXT("Begin"));
    bool bHasChanged = false;
    
    bHasChanged |= UpdateMapValue(PerspectiveMode, TEXT("perspectiveMode"), UInt8ToString(mode));

    // CARLINX_LOG(LogCarLinx, Log, TEXT("End"));
    return bHasChanged;
} 

int ParkingDataProcessor2UEInternal::CheckBitStatus(int32 value)
{
    //检查当前数有多少位是1
    int count = 0;
    while (value) {
        value &= value - 1; // 移除最低位的1
        ++count;
    }
    CARLINX_LOG(LogCarLinx,Log, TEXT("HPA Road Point Numbers: %d"), count);
    return count;
}

int ParkingDataProcessor2UEInternal::CheckTraceID(float MiniOffset, float ChangeOffset, FVector StartPoint, FVector EndPoint)
{

    float RoadLength = FVector::Distance(StartPoint, EndPoint);

   //CARLINX_LOG(LogCarLinx,Log, TEXT("HPA ParkingTrace RoadLength: %f"), RoadLength);
   //CARLINX_LOG(LogCarLinx,Log, TEXT("HPA ParkingTrace start point x value: %f"), StartPoint.X);
   //CARLINX_LOG(LogCarLinx,Log, TEXT("HPA ParkingTrace end point x value: %f, %f, %f"), EndPoint.X, EndPoint.Y, EndPoint.Z);

    if (RoadLength <= MiniOffset) 
    {
        bNearSmallOffset = true;
        RecordingTraceOffset = RoadLength;
        //CARLINX_LOG(LogCarLinx,Log, TEXT("HPA ParkingTrace near small offset."));
    }

    else if(bNearSmallOffset && RoadLength > MiniOffset && RoadLength > ChangeOffset)
    {
        TraceID += 1;
        bNearSmallOffset = false;
        //CARLINX_LOG(LogCarLinx,Log, TEXT("HPA ParkingTrace ID self add."));
    }

    //CARLINX_LOG(LogCarLinx,Log, TEXT("HPA ParkingTrace ID: %d"), TraceID);
    return TraceID;

}

void ParkingDataProcessor2UEInternal::ProcessSlotData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot& slotData)
{
    if (!bIsRecordingSlots || slotData.slotID__ == 0)
    {
        return;
    }

    FParkingSlotData SlotData;

    // 基本信息
    SlotData.SlotID = slotData.slotID__;

    // SlotTyp
    SlotData.SlotYawAg = slotData.slotTyp__.slotYawAg__;
    SlotData.SlotRollAg = slotData.slotTyp__.slotrollAg__;
    SlotData.SlotPitchAg = slotData.slotTyp__.slotpitchAg__;
    SlotData.ObiqueSlotAg = slotData.slotTyp__.obiqueSlotAg__;
    SlotData.SlotTypInfo = slotData.slotTyp__.slotTypinfo__;

    // SlotBounding - 坐标转换: 乘以10转为cm,Y轴取反
    SlotData.SlotStart = FVector(
        slotData.slotBounding__.slotStart__.posnX__ * 10.f,
        slotData.slotBounding__.slotStart__.posnY__ * -10.f,
        slotData.slotBounding__.slotStart__.posnZ__ * 10.f
    );

    SlotData.SlotEnd = FVector(
        slotData.slotBounding__.slotEnd__.posnX__ * 10.f,
        slotData.slotBounding__.slotEnd__.posnY__ * -10.f,
        slotData.slotBounding__.slotEnd__.posnZ__ * 10.f
    );

    SlotData.SlotStartRear = FVector(
        slotData.slotBounding__.slotStartRear__.posnX__ * 10.f,
        slotData.slotBounding__.slotStartRear__.posnY__ * -10.f,
        slotData.slotBounding__.slotStartRear__.posnZ__ * 10.f
    );

    SlotData.SlotEndRear = FVector(
        slotData.slotBounding__.slotEndRear__.posnX__ * 10.f,
        slotData.slotBounding__.slotEndRear__.posnY__ * -10.f,
        slotData.slotBounding__.slotEndRear__.posnZ__ * 10.f
    );

    // 计算中心点
    SlotData.CenterPosition = (SlotData.SlotStart + SlotData.SlotEnd + SlotData.SlotStartRear + SlotData.SlotEndRear) / 4.0f;

    // 计算角度
    if (SlotData.SlotStartRear != SlotData.SlotEndRear)
    {
        FVector VectorCA = (SlotData.SlotStart - SlotData.SlotStartRear).GetSafeNormal();
        FVector Forward = FVector::UpVector;
        FMatrix RotationMatrix = FRotationMatrix::MakeFromXZ(VectorCA, Forward);
        FRotator Rotator = RotationMatrix.Rotator();
        SlotData.Angle = Rotator.Yaw;
    }
    else
    {
        SlotData.Angle = 0.f;
    }

    // SlotSt
    SlotData.SlotSts1 = static_cast<uint8>(slotData.slotSt__.slotSts1__);
    SlotData.SlotNumber = static_cast<uint8>(slotData.slotSt__.slotNumber__);

    // SlotCod
    SlotData.SlotCod1 = slotData.slotCod__.slotCod1__;
    SlotData.SlotCod2 = slotData.slotCod__.slotCod2__;
    SlotData.SlotCod3 = slotData.slotCod__.slotCod3__;
    SlotData.SlotCod4 = slotData.slotCod__.slotCod4__;
    SlotData.SlotCod5 = slotData.slotCod__.slotCod5__;

    // 处理PrkgSlotEvt信号的各个bit位
    int8_t obiqueSlotAg = slotData.slotTyp__.obiqueSlotAg__;
    SlotData.HasWheelBlock = (obiqueSlotAg & 0x01) != 0;
    SlotData.WheelBlockFront = (obiqueSlotAg & 0x02) != 0;
    SlotData.WheelBlockRear = (obiqueSlotAg & 0x04) != 0;
    SlotData.HasGroundLock = (obiqueSlotAg & 0x08) != 0;
    SlotData.GroundLockStatus = (obiqueSlotAg & 0x10) != 0;

    // 更新到历史记录中
    if (SlotHistory.Contains(slotData.slotID__))
    {
        SlotHistory[slotData.slotID__] = SlotData;
        CARLINX_LOG(LogCarLinx, Log, TEXT("Updated slot data for ID: %d"), slotData.slotID__);
    }
    else
    {
        SlotHistory.Add(slotData.slotID__, SlotData);
        CARLINX_LOG(LogCarLinx, Log, TEXT("Added new slot data for ID: %d"), slotData.slotID__);
    }
}
