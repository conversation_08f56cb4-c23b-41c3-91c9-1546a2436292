// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

// UE4 Core headers
#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "UObject/Object.h"
#include "GameFramework/Actor.h"
#include "Engine/World.h"

// UE4 Networking headers
#include "Networking/Public/Interfaces/IPv4/IPv4Address.h"
#include "Networking/Public/Interfaces/IPv4/IPv4Endpoint.h"
#include "Sockets.h"
#include "SocketSubsystem.h"
#include "Common/UdpSocketBuilder.h"

// UE4 Threading headers
#include "HAL/Runnable.h"
#include "HAL/RunnableThread.h"
#include "HAL/ThreadSafeBool.h"

// UE4 Container headers
#include "Containers/Queue.h"
#include "Containers/Array.h"
#include "Containers/Map.h"

// UE4 Misc headers
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"
#include "Logging/LogMacros.h"

class FsoLoadModule : public IModuleInterface
{
public:

	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;
};
