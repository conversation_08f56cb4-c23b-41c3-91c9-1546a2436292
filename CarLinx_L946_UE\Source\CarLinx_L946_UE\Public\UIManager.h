// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "UIManager.generated.h"

UCLASS()
class CARLINX_L946_UE_API AUIManager : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	AUIManager();

	UFUNCTION(BlueprintCallable)
	void OpenUI(const FString PaneName, const bool HideLastUI);

	UFUNCTION(BlueprintCallable)
	void CLoseUI();

	UFUNCTION(BlueprintCallable)
	void CLoseAllUI();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

public:
	UPROPERTY(EditAnywhere)
	TMap<FString, TSubclassOf<UUserWidget>> PanelInfo;

	TArray<UUserWidget*> PanelStack;

};
