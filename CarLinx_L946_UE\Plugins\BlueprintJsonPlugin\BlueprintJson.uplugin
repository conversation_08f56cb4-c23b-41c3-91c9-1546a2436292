{"FileVersion": 3, "Version": 1, "VersionName": "1.0.6", "FriendlyName": "BlueprintJSON", "Description": "A JSON parser for Blueprints", "Category": "JSON", "CreatedBy": "Pandores", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/product/4d3abe312cef4826999700204bb4d52e", "SupportURL": "mailto:<EMAIL>", "EngineVersion": "4.27.0", "CanContainContent": false, "Installed": true, "Modules": [{"Name": "BlueprintJson", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault", "WhitelistPlatforms": ["Win64", "Win32", "<PERSON>", "IOS", "Android", "Linux"]}, {"Name": "BlueprintJsonEditor", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Win32", "<PERSON>", "Linux"]}]}