// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CustomDelegate.generated.h"

/**
 * 
 */
UCLASS(Blueprintable)
class CARLINX_L946_UE_API UCustomDelegate : public UObject
{
	GENERATED_BODY()
public:
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FIntEventSignature, const FString&, Message, int32, Value);

	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FFloatEventSignature, const FString&, Message, float, Value);

	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FStringEventSignature, const FString&, Message, FString, Value);

	UPROPERTY(BlueprintAssignable, BlueprintCallable, Category = "EventDispatcher")
	FIntEventSignature OnIntEventTriggered;

	UPROPERTY(BlueprintAssignable, BlueprintCallable, Category = "EventDispatcher")
	FFloatEventSignature OnFloatEventTriggered;

	UPROPERTY(BlueprintAssignable, BlueprintCallable, Category = "EventDispatcher")
	FStringEventSignature OnStringEventTriggered;

	UFUNCTION(BlueprintCallable, Category = "EventDispatcher")
	void TriggerIntEvent(FString Message, int32 Value);

	UFUNCTION(BlueprintCallable, Category = "EventDispatcher")
	void TriggerFloatEvent(FString Message, float Value);

	UFUNCTION(BlueprintCallable, Category = "EventDispatcher")
	void TriggerStringEvent(const FString& Message, FString Value);
	
};
