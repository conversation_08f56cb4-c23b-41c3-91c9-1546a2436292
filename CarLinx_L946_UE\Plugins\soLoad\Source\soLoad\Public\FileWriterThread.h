#pragma once

// UE4 Core headers
#include "CoreMinimal.h"
#include "HAL/Runnable.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "HAL/ThreadSafeCounter.h"
#include "Containers/Queue.h"

// Standard headers
#include <vector>

// Project headers
#include "DataTypes.h"

struct FParkingDataFileHeader;

class SOLOAD_API FFileWriterThread : public FRunnable
{
public:
    explicit FFileWriterThread(const FString& FilePath);
    virtual ~FFileWriterThread();

    // FRunnable接口
    virtual bool Init() override;
    virtual uint32 Run() override;
    virtual void Stop() override;
    virtual void Exit() override;

    // 线程控制函数
    bool Start();
    bool IsRunning() const { return Thread != nullptr && bRunning; }
    bool Restart();

    // 文件操作
    bool OpenFile(const FString& FilePath);
    void CloseFile();
    
    // 数据操作
    void EnqueueData(const FParkingDataFrame& Frame);
    void UpdateHeader(uint64_t frameCount = 0, int64_t endTime = 0, int64_t startTime = 0);
    void InitHeader(int64_t startTime);
    void MonitorQueuePerformance();

    // 数据写入接口
    bool UpdateFilePath(const FString& NewFilePath);
    bool IsFileOpened() const { return bFileOpened; }

protected:
    static constexpr int32 MAX_QUEUE_SIZE = 1000;
    static constexpr int32 WARNING_QUEUE_SIZE = 500;

    FString ThreadFilePath;
    FString CurrentFilePath;
    FILE* FileHandle;
    bool bFileOpened;
    FRunnableThread* Thread;
    bool bRunning;
    
    // 使用FParkingDataFrame的队列
    TQueue<FParkingDataFrame, EQueueMode::Mpsc> DataQueue;
    FCriticalSection CriticalSection;
    
    // 性能监控
    FThreadSafeCounter TotalFramesReceived;
    FThreadSafeCounter TotalFramesWritten;
    FThreadSafeCounter PendingDataCount;
    int32 LastQueueSize;
    double LastCheckTime;
    double QueueGrowthRate;
    
    // 文件头
    FParkingDataFileHeader CurrentHeader;
    bool bHeaderNeedsUpdate;

    // 内部函数
    bool WriteHeader();
    void ProcessQueuedData();
};