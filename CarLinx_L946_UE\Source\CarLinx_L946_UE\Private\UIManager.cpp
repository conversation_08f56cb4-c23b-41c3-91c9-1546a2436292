// Fill out your copyright notice in the Description page of Project Settings.


#include "UIManager.h"

#include "Blueprint/UserWidget.h"

// Sets default values
AUIManager::AUIManager()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

void AUIManager::OpenUI(const FString PaneName, const bool HideLastUI)
{
	if (PanelInfo.Contains(PaneName) == false)
	{
		return;
	}

	TSubclassOf<UUserWidget> t = PanelInfo[PaneName];

	if (PanelStack.Num() == 0)
	{
		UUserWidget* temp = CreateWidget<UUserWidget>(GetWorld(), *PanelInfo[PaneName]);

		temp->AddToViewport();
		PanelStack.Push(temp);
		return;
	}

	UUserWidget* last = PanelStack[PanelStack.Num() - 1];
	if (HideLastUI)
	{
		last->SetVisibility(ESlateVisibility::Hidden);
	}
	last->SetIsEnabled(false);

	UUserWidget* temp = CreateWidget(GetWorld(), *PanelInfo[PaneName]);
	temp->AddToViewport();
	PanelStack.Push(temp);
}

void AUIManager::CLoseUI()
{
	if (PanelStack.Num() == 0)
	{
		return;
	}

	UUserWidget* umg = PanelStack.Pop();
	umg->RemoveFromViewport();

	if (PanelStack.Num() == 0)
	{
		return;
	}

	UUserWidget* NewUmg = PanelStack[PanelStack.Num() - 1];
	NewUmg->SetVisibility(ESlateVisibility::Visible);
	NewUmg->SetIsEnabled(true);
}

void AUIManager::CLoseAllUI()
{
	while (PanelStack.Num() > 0)
	{
		UUserWidget* umg = PanelStack.Pop();
		umg->RemoveFromViewport();
	}
}

// Called when the game starts or when spawned
void AUIManager::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AUIManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

