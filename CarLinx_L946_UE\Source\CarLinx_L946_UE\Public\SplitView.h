// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "SplitView.generated.h"

USTRUCT(BlueprintType)
struct FSplitData1
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(EditAnywhere)
	float SizeX;
	UPROPERTY(EditAnywhere)
	float SizeY;
	UPROPERTY(EditAnywhere)
	float OriginX;
	UPROPERTY(EditAnywhere)
	float OriginY;

	FSplitData1()
	{
		SizeX = 0;
		SizeY = 0;
		OriginX = 0;
		OriginY = 0;
	}

	FSplitData1(float x, float y, float sizex, float sizey) :
		OriginX(x),
		OriginY(y),
		SizeX(sizex),
		SizeY(sizey)
	{

	}
};

UCLASS()
class CARLINX_L946_UE_API ASplitView : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	ASplitView();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable)
	void SplitScreen(TArray<FSplitData1> DataArray);

};
