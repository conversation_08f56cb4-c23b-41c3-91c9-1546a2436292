﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "HistoryParkingLotBase.generated.h"

enum class ECarSlot : uint8;

UCLASS()
class CARLINX_L946_UE_API AHistoryParkingLotBase : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AHistoryParkingLotBase();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	// Called every frame
	virtual void Tick(float DeltaTime) override;

public:

	//数字旋转
	UFUNCTION(BlueprintImplementableEvent, Category="ParkingLot")
	void NumberRotation(ECarSlot Car);

	//车位数字显示
	UFUNCTION(BlueprintImplementableEvent, Category="ParkingLot")
	void NumberCtl(int32 Status, int32 Number);

	//更新车位数据
	UFUNCTION(BlueprintCallable,Category="ParkingLot")
	void UpdateHistoryParkingLotInfo(int32 Status, int32 Number, FVector Location, float Rotation, ECarSlot CarSlotType, float InWidth);
	
	//车位数字颜色更新
	UFUNCTION(BlueprintImplementableEvent, Category="ParkingLot")
	void NumberColorCtrl();
	
	//更新显示材质
	UFUNCTION(BlueprintImplementableEvent, Category="ParkingLot")
	void UpdateDisMat(int32 Status);
	
	//初始化车位大小
	UFUNCTION(BlueprintCallable, Category="ParkingLot")
	void IniteParkingSize(float sizeX, float sizeY);

	//设置车位可视性
	UFUNCTION(BlueprintImplementableEvent, Category="ParkingLot")
	void SetNumberVisibility(bool Show);


protected:

	//屏幕组件
	UPROPERTY(EditDefaultsOnly,BlueprintReadWrite,meta=(AllowPrivateAccess=true))
	USceneComponent* Root;
	
	//UI组件
	UPROPERTY(EditAnywhere,BlueprintReadWrite,meta=(AllowPrivateAccess=true))
	class UWidgetComponent* SlotWidget;
	
	//车位正常材质
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category="ParkingLotMaterial")
	UMaterialInterface* M_Normal;

	//车位选中材质
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category="ParkingLotMaterial")
	UMaterialInterface* M_Selected;

	//车位无效材质
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category="ParkingLotMaterial")
	UMaterialInterface* M_Invalid;

private:

	//车位宽度
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category="Width",meta=(AllowPrivateAccess=true))
	float Width;
};

