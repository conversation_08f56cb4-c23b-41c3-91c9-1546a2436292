// Fill out your copyright notice in the Description page of Project Settings.


#include "NewTrack.h"
#include "Kismet/KismetMathLibrary.h"
#include "Kismet/KismetMaterialLibrary.h"
#include "KismetProceduralMeshLibrary.h"

// Sets default values
ANewTrack::ANewTrack()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	// 初始化 ProceduralMeshComponent
	ProceduralMesh = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("ProceduralMesh"));
	RootComponent = ProceduralMesh;

	// 默认轨迹宽度
	TrackWidth = 10.0f;

}

// Called when the game starts or when spawned
void ANewTrack::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void ANewTrack::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

TArray<float> ANewTrack::GenerateGaussianKernel(int32 KernelSize, float Sigma)
{
    TArray<float> Kernel;
    Kernel.SetNumUninitialized(KernelSize);
    float Sum = 0.0f;

    int32 HalfSize = KernelSize / 2;
    for (int32 i = 0; i < KernelSize; ++i)
    {
        float X = i - HalfSize;
        Kernel[i] = FMath::Exp(-FMath::Pow(X, 2) / (2 * FMath::Pow(Sigma, 2)));

        Sum += Kernel[i];
    }

    // 归一化核，使得其总和为1
    for (int32 i = 0; i < KernelSize; ++i)
    {
        Kernel[i] /= Sum;
    }

    return Kernel;
}

TArray<FVector> ANewTrack::ApplyGaussianSmoothing(const TArray<FVector>& Data, int32 KernelSize, float Sigma)
{
    TArray<FVector> SmoothedData;
    int32 DataSize = Data.Num();
    if (DataSize == 0)
    {
        return SmoothedData;
    }

    // 生成高斯核
    TArray<float> Kernel = GenerateGaussianKernel(KernelSize, Sigma);
    int32 HalfKernelSize = KernelSize / 2;

    // 对每个点进行平滑
    for (int32 i = 0; i < DataSize; ++i)
    {
        FVector SmoothedValue(0.0f, 0.0f, 0.0f);
        float WeightSum = 0.0f;

        // 对当前点的周围点进行加权平均
        for (int32 j = 0; j < KernelSize; ++j)
        {
            int32 DataIndex = i + j - HalfKernelSize;
            if (DataIndex >= 0 && DataIndex < DataSize)
            {
                float Weight = Kernel[j];
                SmoothedValue = SmoothedValue + Data[DataIndex] * Weight;
                WeightSum += Weight;
            }
        }

        // 归一化加权和
        SmoothedValue = SmoothedValue / WeightSum;

        SmoothedData.Add(SmoothedValue);
    }

    return SmoothedData;
}

TArray<FVector> ANewTrack::ApplyGaussianSmoothing1(const TArray<FVector>& Data1, int32 KernelSize, float Sigma)
{

    if (Data1.Num()<=3)
    {
        return Data1;
    }

    TArray<FVector> Data2 = Data1;
    for(int i=0;i< Data2.Num()-1;i++)
    {
        if (FVector::Distance(Data2[i], Data2[i+1])<5)
        {
            Data2.RemoveAt(i);
            i = i-1;
        }
    }

    TArray<FVector> Data = ExtendBoundary(Data2,3);

    TArray<FVector> SmoothedData;
    int32 DataSize = Data.Num();
    if (DataSize == 0)
    {
        return SmoothedData;
    }

    // 生成高斯核
    TArray<float> Kernel = GenerateGaussianKernel(KernelSize, Sigma);
    int32 HalfKernelSize = KernelSize / 2;

    // 对每个点进行平滑
    for (int32 i = 0; i < DataSize; ++i)
    {
        FVector SmoothedValue(0.0f, 0.0f, 0.0f);
        float WeightSum = 0.0f;

        // 对当前点的周围点进行加权平均
        for (int32 j = 0; j < KernelSize; ++j)
        {
            int32 DataIndex = i + j - HalfKernelSize;
            if (DataIndex >= 0 && DataIndex < DataSize)
            {
                float Weight = Kernel[j];
                SmoothedValue = SmoothedValue + Data[DataIndex] * Weight;
                WeightSum += Weight;
            }
        }

        // 归一化加权和
        SmoothedValue = SmoothedValue / WeightSum;

        SmoothedData.Add(SmoothedValue);
    }


    if (SmoothedData.Num()> Data2.Num())
    {
        SmoothedData.RemoveAt(0, 3); // 移除首部扩展
        SmoothedData.RemoveAt(SmoothedData.Num() - 3, 3);
    }

    return SmoothedData;
}

void ANewTrack::ApplyGaussianSmoothingHX(const TArray<FVector>& Data1,const TArray<float>& Rotations, const TArray<int>& Moves, TArray<FVector>& Data2, TArray<float>& Rotations2, TArray<int>& Moves2, int32 KernelSize, float Sigma)
{

   Data2 = Data1;

    Rotations2 = Rotations;

    Moves2 = Moves;

    for (int i = 0; i < Data2.Num() - 1; i++)
    {
        if (FVector::Distance(Data2[i], Data2[i + 1]) < 5)
        {
            Data2.RemoveAt(i);
            Rotations2.RemoveAt(i);
            Moves2.RemoveAt(i);
            i = i - 1;
        }
    }

    if (Data2.Num()<3)
    {
       // return;
    }

    TArray<FVector> Data = ExtendBoundary(Data2, 3);

    TArray<FVector> SmoothedData;
    int32 DataSize = Data.Num();
    if (DataSize == 0)
    {
        return;
    }

    // 生成高斯核
    TArray<float> Kernel = GenerateGaussianKernel(KernelSize, Sigma);
    int32 HalfKernelSize = KernelSize / 2;

    // 对每个点进行平滑
    for (int32 i = 0; i < DataSize; ++i)
    {
        FVector SmoothedValue(0.0f, 0.0f, 0.0f);
        float WeightSum = 0.0f;

        // 对当前点的周围点进行加权平均
        for (int32 j = 0; j < KernelSize; ++j)
        {
            int32 DataIndex = i + j - HalfKernelSize;
            if (DataIndex >= 0 && DataIndex < DataSize)
            {
                float Weight = Kernel[j];
                SmoothedValue = SmoothedValue + Data[DataIndex] * Weight;
                WeightSum += Weight;
            }
        }

        // 归一化加权和
        SmoothedValue = SmoothedValue / WeightSum;

        SmoothedData.Add(SmoothedValue);
    }


    if (SmoothedData.Num() > Data2.Num())
    {
        SmoothedData.RemoveAt(0, 3); // 移除首部扩展
        SmoothedData.RemoveAt(SmoothedData.Num() - 3, 3);
    }

    Data2 = SmoothedData;
}

void ANewTrack::ApplyGaussianSmoothing2(const TArray<FVector>& Data1, const TArray<float>& Width, TArray<FVector>& NewData, TArray<float>& NewWidth, int32 KernelSize, float Sigma)
{

    NewData = Data1;

    NewWidth = Width;

    for (int i = 0; i < NewData.Num() - 1; i++)
    {
        if (FVector::Distance(NewData[i], NewData[i + 1]) < 5)
        {
            NewData.RemoveAt(i);
            NewWidth.RemoveAt(i);
            i = i - 1;
        }
    }

    if (NewData.Num() < 3)
    {
        // return;
    }

    TArray<FVector> Data = ExtendBoundary(NewData, 3);

    TArray<FVector> SmoothedData;
    int32 DataSize = Data.Num();
    if (DataSize == 0)
    {
        return;
    }

    // 生成高斯核
    TArray<float> Kernel = GenerateGaussianKernel(KernelSize, Sigma);
    int32 HalfKernelSize = KernelSize / 2;

    // 对每个点进行平滑
    for (int32 i = 0; i < DataSize; ++i)
    {
        FVector SmoothedValue(0.0f, 0.0f, 0.0f);
        float WeightSum = 0.0f;

        // 对当前点的周围点进行加权平均
        for (int32 j = 0; j < KernelSize; ++j)
        {
            int32 DataIndex = i + j - HalfKernelSize;
            if (DataIndex >= 0 && DataIndex < DataSize)
            {
                float Weight = Kernel[j];
                SmoothedValue = SmoothedValue + Data[DataIndex] * Weight;
                WeightSum += Weight;
            }
        }

        // 归一化加权和
        SmoothedValue = SmoothedValue / WeightSum;

        SmoothedData.Add(SmoothedValue);
    }


    if (SmoothedData.Num() > NewData.Num())
    {
        SmoothedData.RemoveAt(0, 3); // 移除首部扩展
        SmoothedData.RemoveAt(SmoothedData.Num() - 3, 3);
    }

    NewData = SmoothedData;
}

float ANewTrack::CalculatePrimater(TArray<FVector> Points)
{
    float Primater = 0.f;
    int32 Length = Points.Num();
    for (int i = 0; i < Length; ++i)
    {
        int NextIndex = i + 1;
        if (NextIndex < Length)
        {
            Primater = Primater + FVector::Distance(Points[i], Points[NextIndex]);
        }
    }

    return Primater;
}

void ANewTrack::GenerateTrackMesh(const TArray<FVector>& Points, const int32& Wid)
{
    TrackWidth = Wid;

    TrackPoints = Points;

    if (TrackPoints.Num() < 2) return; // 至少需要两点

    TArray<float> Widths;
    GenerateAdjustedTrajectoryWidth(TrackPoints, TrackWidth, Widths);

    //计算周长
    float Primater = this->CalculatePrimater(TrackPoints);

    float CurrentUVLength = 0.f;

    TArray<FVector> Vertices;
    TArray<int32> Triangles;
    TArray<FVector> Normals;
    TArray<FVector2D> UVs;
    TArray<FColor> VertexColors;
    TArray<FProcMeshTangent> Tangents;

    // 遍历轨迹点
    for (int32 i = 0; i <= TrackPoints.Num() - 1; ++i)
    {
        FVector Tangent;
        if (i == TrackPoints.Num() - 1)
        {
            // 计算切线方向
            Tangent = (TrackPoints[i] - TrackPoints[i - 1]).GetSafeNormal();

            // 计算法向量（垂直于切线方向）
            FVector Normal = FVector::CrossProduct(Tangent, FVector::UpVector).GetSafeNormal();

            // 生成左右边界点
            FVector LeftPoint = TrackPoints[i] - Normal * (Widths[i] / 2.0f);
            FVector RightPoint = TrackPoints[i] + Normal * (Widths[i] / 2.0f);

            // 添加顶点
            Vertices.Add(LeftPoint);
            Vertices.Add(RightPoint);

            Normals.Add(FVector::UpVector);
            Normals.Add(FVector::UpVector);

            Tangents.Add(FProcMeshTangent(Tangent, true));
            Tangents.Add(FProcMeshTangent(Tangent, true));

            UVs.Add(FVector2D(1.f, 1));
            UVs.Add(FVector2D(0.f, 1));

            VertexColors.Add(FColor::White);
            VertexColors.Add(FColor::White);
            break;
        }

        FVector CurrentPoint = TrackPoints[i];
        FVector NextPoint = TrackPoints[i + 1];

        Tangent = (NextPoint - CurrentPoint).GetSafeNormal();

        // 计算法向量（垂直于切线方向）
        FVector Normal = FVector::CrossProduct(Tangent, FVector::UpVector).GetSafeNormal();

        // 生成左右边界点
        FVector LeftPoint = CurrentPoint - Normal * (Widths[i] / 2.0f);
        FVector RightPoint = CurrentPoint + Normal * (Widths[i] / 2.0f);

        // 添加顶点
        int32 VertexIndex = Vertices.Num();
        Vertices.Add(LeftPoint);
        Vertices.Add(RightPoint);

        Normals.Add(FVector::UpVector);
        Normals.Add(FVector::UpVector);
        //Normals.Add(FVector::UpVector);

        Tangents.Add(FProcMeshTangent(Tangent, true));
        Tangents.Add(FProcMeshTangent(Tangent, true));

        if (i == 0)
        {
            CurrentUVLength = 0;
        }
        else
        {
            CurrentUVLength = CurrentUVLength + FVector::Distance(TrackPoints[i], TrackPoints[i - 1]);
        }

        UVs.Add(FVector2D(1.f, CurrentUVLength / Primater));
        UVs.Add(FVector2D(0.f, CurrentUVLength / Primater));

        VertexColors.Add(FColor::White);
        VertexColors.Add(FColor::White);
    }

    //计算三角面数
    UKismetProceduralMeshLibrary::CreateGridMeshTriangles(Vertices.Num() / 2, 2, true, Triangles);

    // 创建网格
   // ProceduralMesh->ClearMeshSection(0);
    ProceduralMesh->CreateMeshSection(0, Vertices, Triangles, Normals, UVs, VertexColors, Tangents, true);

    if (TrackMat)
    {
        UMaterialInstanceDynamic* TrackDynamicMat = UKismetMaterialLibrary::CreateDynamicMaterialInstance(GetWorld(), TrackMat, NAME_None);

        ProceduralMesh->SetMaterial(0, TrackDynamicMat);
    }
}


void ANewTrack::GenerateTrackMesh1(const TArray<FVector>& Points, const TArray<float>& Rotations, const TArray<int>& Moves, const int32& Wid, float FirstLength)
{

    TrackWidth = Wid;

    TrackPoints = Points;

    if (TrackPoints.Num() < 2) return; // 至少需要两点

    //TArray<float> Widths;
    //GenerateAdjustedTrajectoryWidth(TrackPoints, TrackWidth, Widths);

    //计算周长
    float Primater = this->CalculatePrimater(TrackPoints);
    
   // if (bFirst)
    //{
    //    FirstTrackLength = Primater;
   // }

   // float bili = Primater / FirstTrackLength;

    float CurrentUVLength = 0.f;

    TArray<FVector> Vertices;
    TArray<int32> Triangles;
    TArray<FVector> Normals;
    TArray<FVector2D> UVs;
    TArray<FColor> VertexColors;
    TArray<FProcMeshTangent> Tangents;

    // 遍历轨迹点
    for (int32 i = 0; i <= TrackPoints.Num() - 1; ++i)
    {
        FVector CurrentPoint = TrackPoints[i];
        //FVector NextPoint = TrackPoints[i + 1];

        FVector Tangent;

        // 计算切线方向
        //Tangent = (TrackPoints[i] - TrackPoints[i - 1]).GetSafeNormal();

        // 计算法向量（垂直于切线方向）
        //FVector Normal = FVector::CrossProduct(Tangent, FVector::UpVector).GetSafeNormal();
        FVector Normal = UKismetMathLibrary::GetRightVector(FRotator(0, Rotations[i], 0)) * (TrackWidth / 2);
        FVector LeftPoint;
        FVector RightPoint;
        if (Moves[i]==1)
        {
            LeftPoint = CurrentPoint + Normal;
            RightPoint = CurrentPoint - Normal;
        }
        else
        {
            LeftPoint = CurrentPoint - Normal;
            RightPoint = CurrentPoint + Normal;
        }
        

        // 生成左右边界点
       //LeftPoint = CurrentPoint - Normal;
       //RightPoint = CurrentPoint + Normal;

        // 添加顶点
        int32 VertexIndex = Vertices.Num();
        Vertices.Add(LeftPoint);
        Vertices.Add(RightPoint);

        Normals.Add(FVector::UpVector);
        Normals.Add(FVector::UpVector);

        if (i == 0)
        {
            CurrentUVLength = 0;
        }
        else
        {
            CurrentUVLength = CurrentUVLength + FVector::Distance(TrackPoints[i], TrackPoints[i - 1]);
        }

        UVs.Add(FVector2D(1.f, (FirstLength - Primater + CurrentUVLength) / FirstLength));
        UVs.Add(FVector2D(0.f, (FirstLength - Primater + CurrentUVLength) / FirstLength));

        VertexColors.Add(FColor::White);
        VertexColors.Add(FColor::White);
    }

    //计算三角面数
    UKismetProceduralMeshLibrary::CreateGridMeshTriangles(Vertices.Num() / 2, 2, true, Triangles);

    // 创建网格
    //ProceduralMesh->ClearMeshSection(0);
    ProceduralMesh->CreateMeshSection(0, Vertices, Triangles, Normals, UVs, VertexColors, Tangents, true);

    if (TrackMat)
    {
        UMaterialInstanceDynamic* TrackDynamicMat = UKismetMaterialLibrary::CreateDynamicMaterialInstance(GetWorld(), TrackMat, NAME_None);

        ProceduralMesh->SetMaterial(0, TrackDynamicMat);
    }
}

void ANewTrack::GenerateTrackMesh2(const TArray<FVector>& Points, const TArray<float>& Widths)
{
    //TrackWidth = Wid;

    TrackPoints = Points;

    if (TrackPoints.Num() < 2) return; // 至少需要两点

    //TArray<float> Widths;
    //GenerateAdjustedTrajectoryWidth(TrackPoints, TrackWidth, Widths);

    //计算周长
    float Primater = this->CalculatePrimater(TrackPoints);

    float CurrentUVLength = 0.f;

    TArray<FVector> Vertices;
    TArray<int32> Triangles;
    TArray<FVector> Normals;
    TArray<FVector2D> UVs;
    TArray<FColor> VertexColors;
    TArray<FProcMeshTangent> Tangents;

    // 遍历轨迹点
    for (int32 i = 0; i <= TrackPoints.Num() - 1; ++i)
    {
        FVector Tangent;
        if (i == TrackPoints.Num() - 1)
        {
            // 计算切线方向
            Tangent = (TrackPoints[i] - TrackPoints[i - 1]).GetSafeNormal();

            // 计算法向量（垂直于切线方向）
            FVector Normal = FVector::CrossProduct(Tangent, FVector::UpVector).GetSafeNormal();

            // 生成左右边界点
            FVector LeftPoint = TrackPoints[i] - Normal * (Widths[i] / 2.0f);
            FVector RightPoint = TrackPoints[i] + Normal * (Widths[i] / 2.0f);

            // 添加顶点
            Vertices.Add(LeftPoint);
            Vertices.Add(RightPoint);

            Normals.Add(FVector::UpVector);
            Normals.Add(FVector::UpVector);

            Tangents.Add(FProcMeshTangent(Tangent, true));
            Tangents.Add(FProcMeshTangent(Tangent, true));

            UVs.Add(FVector2D(1.f, 1));
            UVs.Add(FVector2D(0.f, 1));

            VertexColors.Add(FColor::White);
            VertexColors.Add(FColor::White);
            break;
        }

        FVector CurrentPoint = TrackPoints[i];
        FVector NextPoint = TrackPoints[i + 1];

        Tangent = (NextPoint - CurrentPoint).GetSafeNormal();

        // 计算法向量（垂直于切线方向）
        FVector Normal = FVector::CrossProduct(Tangent, FVector::UpVector).GetSafeNormal();

        // 生成左右边界点
        FVector LeftPoint = CurrentPoint - Normal * (Widths[i] / 2.0f);
        FVector RightPoint = CurrentPoint + Normal * (Widths[i] / 2.0f);

        // 添加顶点
        int32 VertexIndex = Vertices.Num();
        Vertices.Add(LeftPoint);
        Vertices.Add(RightPoint);

        Normals.Add(FVector::UpVector);
        Normals.Add(FVector::UpVector);
        //Normals.Add(FVector::UpVector);

        Tangents.Add(FProcMeshTangent(Tangent, true));
        Tangents.Add(FProcMeshTangent(Tangent, true));

        if (i == 0)
        {
            CurrentUVLength = 0;
        }
        else
        {
            CurrentUVLength = CurrentUVLength + FVector::Distance(TrackPoints[i], TrackPoints[i - 1]);
        }

        UVs.Add(FVector2D(1.f, CurrentUVLength / Primater));
        UVs.Add(FVector2D(0.f, CurrentUVLength / Primater));

        VertexColors.Add(FColor::White);
        VertexColors.Add(FColor::White);
    }

    //计算三角面数
    UKismetProceduralMeshLibrary::CreateGridMeshTriangles(Vertices.Num() / 2, 2, true, Triangles);

    // 创建网格
   // ProceduralMesh->ClearMeshSection(0);
    ProceduralMesh->CreateMeshSection(0, Vertices, Triangles, Normals, UVs, VertexColors, Tangents, true);

    if (TrackMat)
    {
        UMaterialInstanceDynamic* TrackDynamicMat = UKismetMaterialLibrary::CreateDynamicMaterialInstance(GetWorld(), TrackMat, NAME_None);

        ProceduralMesh->SetMaterial(0, TrackDynamicMat);
    }
}

float ANewTrack::CalculateCurvature(const FVector& Prev, const FVector& Curr, const FVector& Next)
{
    FVector Dir1 = (Curr - Prev).GetSafeNormal();
    FVector Dir2 = (Next - Curr).GetSafeNormal();
    float Angle = FMath::Acos(FVector::DotProduct(Dir1, Dir2));
    return Angle; // 返回曲率（弧度表示）
}

float ANewTrack::AdjustWidthByCurvature(float BaseWidth, float Curvature)
{
    // 曲率越大，宽度越小（避免重叠）
    return BaseWidth * FMath::Clamp(1.0f - Curvature / PI, 0.3f, 1.0f);
}

void ANewTrack::GenerateAdjustedTrajectoryWidth(const TArray<FVector>& SmoothedPoints, float BaseWidth, TArray<float>& OutWidths)
{
    for (int32 i = 1; i < SmoothedPoints.Num() - 1; i++)
    {
        float Curvature = CalculateCurvature(SmoothedPoints[i - 1], SmoothedPoints[i], SmoothedPoints[i + 1]);
        float AdjustedWidth = AdjustWidthByCurvature(BaseWidth, Curvature);
        OutWidths.Add(AdjustedWidth);
    }
    OutWidths.Insert(BaseWidth, 0); // 起点和终点保持默认宽度
    OutWidths.Add(BaseWidth);
}

TArray<FVector> ANewTrack::ExtendBoundary(const TArray<FVector>& Points, int32 ExtensionSize)
{
    if (ExtensionSize> Points.Num()-1)
    {
      return Points;
    }
    TArray<FVector> ExtendedPoints;

    // 添加前扩展点
    for (int32 i = ExtensionSize; i > 0; i--)
    {
        ExtendedPoints.Add(Points[0] + (Points[0] - Points[i]));
    }

    // 添加原始点
    ExtendedPoints.Append(Points);

    // 添加后扩展点
    int32 LastIndex = Points.Num() - 1;
    for (int32 i = 1; i <= ExtensionSize; i++)
    {
        ExtendedPoints.Add(Points[LastIndex] + (Points[LastIndex] - Points[LastIndex - i]));


    }

    return ExtendedPoints;
}

FVector ANewTrack::CubicBezier(const FVector& P0, const FVector& P1, const FVector& P2, const FVector& P3, float T)
{
    float U = 1 - T;
    return U * U * U * P0 +
        3 * U * U * T * P1 +
        3 * U * T * T * P2 +
        T * T * T * P3;
}

TArray<FVector> ANewTrack::GenerateBezierCurve(const TArray<FVector>& ControlPoints, float StepSize)
{
    TArray<FVector> InterpolatedPoints;

    // 确保点数足够
    if (ControlPoints.Num() < 4)
        return InterpolatedPoints;

    for (int32 i = 0; i <= ControlPoints.Num() - 4; i += 3)
    {
        FVector P0 = ControlPoints[i];
        FVector P1 = ControlPoints[i + 1];
        FVector P2 = ControlPoints[i + 2];
        FVector P3 = ControlPoints[i + 3];

        for (float T = 0; T <= 1.0f; T += StepSize)
        {
            InterpolatedPoints.Add(CubicBezier(P0, P1, P2, P3, T));
        }
    }
    //InterpolatedPoints.Add(ControlPoints[ControlPoints.Num()-3]);
    InterpolatedPoints.Add(ControlPoints[ControlPoints.Num() - 2]);
    InterpolatedPoints.Add(ControlPoints[ControlPoints.Num() - 1]);

    return InterpolatedPoints;
}





