#include "ParkingDataProcessManager.h"
#include "Logger.h"
#include "ParkingDataProcessor2UE.h"
#include "GlobalConfig.h"

UParkingDataProcessManager* UParkingDataProcessManager::Instance = nullptr;

UParkingDataProcessManager* UParkingDataProcessManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UParkingDataProcessManager>();
        Instance->AddToRoot();
    }
    return Instance;
}

void UParkingDataProcessManager::ProcessReceivedData(const std::vector<uint8_t>& Data)
{
    // 反序列化到ParkingDataManager
    if (!ParkingDataManager::getInstance().deserialize(Data))
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to deserialize data, data size: %d"), Data.size());
        return;
    }

    // 处理自车数据
    if (ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_SELF_ST))
    {
        const auto& selfData = ParkingDataManager::getInstance().getPrkgSelfST();
        ProcessSelfCarData(selfData);
    }

    // 处理车位数据
    if (ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_SLOT_ARR))
    {
        const auto& slotArr = ParkingDataManager::getInstance().getPrkgSlotArr();
        for (int32 i = 0; i < 32; ++i)
        {
            ProcessSlotData(slotArr[i], i);
        }
    }

    // 处理障碍物数据
    if (ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_OBSTACLE_ARR))
    {
        //static int32 counter = 0;
        //// 每4帧处理一次障碍物数据
        //if (++counter > 3) {
        //    counter = 0;
            const auto& obstacleArr = ParkingDataManager::getInstance().getPrkgObstacleArr();
            for (int32 i = 0; i < 60; ++i)
            {
                ProcessObstacleData(obstacleArr[i], i);
            }
        //}
    }

    // 处理交通标志数据
    if (ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_TRAFFICSIGN_ARR))
    {
        const auto& trafficSignArr = ParkingDataManager::getInstance().getPrkgTrafficsignArr();
        for (int32 i = 0; i < 10; ++i)
        {
            ProcessTrafficSignData(trafficSignArr[i], i);
        }
    }

    // 处理道路点数据toMap, 暂时不需要解析为map了，蓝图从下面获取整合后的道路点数据 DeserializeHpaRoadPoint
/*    if (ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_ROAD_RIGHT_POINT_ARR))
    {
        const auto& roadRightArr = ParkingDataManager::getInstance().getPrkgRoadRightPointArr();
        for (int32 i = 0; i < 100; ++i)
        {
            ProcessRoadPointData(roadRightArr[i], i, true);
        }
    }

    if (ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_ROAD_LEFT_POINT_ARR))
    {
        const auto& roadLeftArr = ParkingDataManager::getInstance().getPrkgRoadLeftPointArr();
        for (int32 i = 0; i < 100; ++i)
        {
            ProcessRoadPointData(roadLeftArr[i], i, false);
        }
    }
*/
    // 处理视角模式数据
    if (ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_PERSPECTIVE_MODE))
    {
        uint8_t mode = ParkingDataManager::getInstance().getPerspectiveMode();
        ProcessPerspectiveMode(mode);
    }

    // 处理轨迹规划数据
    if (ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_TTRACK_PLANNING_L_ARR))
    {
        const auto& trackPlanningLArr = ParkingDataManager::getInstance().getPrkgTtrackPlanningLArr();
        const auto& trackPlanningRArr = ParkingDataManager::getInstance().getPrkgTtrackPlanningRArr();
        const auto& trackselfData = ParkingDataManager::getInstance().getPrkgSelfST();
        ProcessTrackPlanningData(trackPlanningLArr, trackPlanningRArr, trackselfData);
    }

    // 处理HPA全局道路点数据
    if (ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_ROAD_LEFT_POINT_ARR) && ParkingDataManager::getInstance().isFlagSet(ParkingDataManager::FLAG_ROAD_RIGHT_POINT_ARR) &&
        UGlobalConfig::GetInstance()->IsInHPA())
    {
        const auto& roadLeftArr = ParkingDataManager::getInstance().getPrkgRoadLeftPointArr();
        const auto& roadRightArr = ParkingDataManager::getInstance().getPrkgRoadRightPointArr();
        DeserializeHpaRoadPoint(roadLeftArr, roadRightArr);
    }
}

void UParkingDataProcessManager::ProcessSelfCarData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& Data)
{
    FScopeLock Lock(&DataLock);
    if (ParkingDataProcessor2UEInternal::DeserializeSelfCarData(Data, SelfCarData))
    {
        FFunctionGraphTask::CreateAndDispatchWhenReady([this]() {
            OnSelfCarDataProcessed.Broadcast();
        }, TStatId(), nullptr, ENamedThreads::GameThread);
    }
}

void UParkingDataProcessManager::ProcessSlotData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot& Data, int32 Index)
{
    FScopeLock Lock(&DataLock);
    if (ParkingDataProcessor2UEInternal::DeserializeSlotData(Data, SlotData[Index]))
    {
        FFunctionGraphTask::CreateAndDispatchWhenReady([this, Index]() {
            OnSlotDataProcessed.Broadcast(Index);
        }, TStatId(), nullptr, ENamedThreads::GameThread);
    }
}

void UParkingDataProcessManager::ProcessObstacleData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle& Data, int32 Index)
{
    FScopeLock Lock(&DataLock);
    if (ParkingDataProcessor2UEInternal::DeserializeObstacleData(Data, ObstacleData[Index]))
    {
        FFunctionGraphTask::CreateAndDispatchWhenReady([this, Index]() {
            OnObstacleDataProcessed.Broadcast(Index);
        }, TStatId(), nullptr, ENamedThreads::GameThread);
    }
}

void UParkingDataProcessManager::ProcessTrafficSignData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgTrafficsign& Data, int32 Index)
{
    FScopeLock Lock(&DataLock);
    if (ParkingDataProcessor2UEInternal::DeserializeTrafficSignData(Data, TrafficSignData[Index]))
    {
        FFunctionGraphTask::CreateAndDispatchWhenReady([this, Index]() {
            OnTrafficSignDataProcessed.Broadcast(Index);
        }, TStatId(), nullptr, ENamedThreads::GameThread);
    }
}

void UParkingDataProcessManager::ProcessRoadPointData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint& Data, int32 Index, bool bIsRight)
{
    FScopeLock Lock(&DataLock);
    if (bIsRight)
    {
        if (ParkingDataProcessor2UEInternal::DeserializeRoadPointData(Data, RoadRightPointData[Index]))
        {
            FFunctionGraphTask::CreateAndDispatchWhenReady([this, Index]() {
                OnRoadRightPointDataProcessed.Broadcast(Index);
            }, TStatId(), nullptr, ENamedThreads::GameThread);
        }
    }
    else
    {
        if (ParkingDataProcessor2UEInternal::DeserializeRoadPointData(Data, RoadLeftPointData[Index]))
        {
            FFunctionGraphTask::CreateAndDispatchWhenReady([this, Index]() {
                OnRoadLeftPointDataProcessed.Broadcast(Index);
            }, TStatId(), nullptr, ENamedThreads::GameThread);
        }
    }
}

void UParkingDataProcessManager::ProcessPerspectiveMode(uint8_t Mode)
{
    FScopeLock Lock(&DataLock);
    if (ParkingDataProcessor2UEInternal::DeserializePerspectiveMode(Mode, PerspectiveModeData))
    {
        FFunctionGraphTask::CreateAndDispatchWhenReady([this]() {
            OnPerspectiveModeProcessed.Broadcast();
        }, TStatId(), nullptr, ENamedThreads::GameThread);
    }
}

void UParkingDataProcessManager::ProcessTrackPlanningData(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& left, 
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& right, 
    const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& selfData)
{
    FScopeLock Lock(&DataLock);
    if (ParkingDataProcessor2UEInternal::DeserializeTrackPlanningData(left, right, selfData,TrackPlanningPoints, LineType))
    {
        FFunctionGraphTask::CreateAndDispatchWhenReady([this]() {
            OnTrackPlanningProcessed.Broadcast();
            }, TStatId(), nullptr, ENamedThreads::GameThread);
    }
}

TMap<FString, FString> UParkingDataProcessManager::GetSelfCarData() const
{
    FScopeLock Lock(&DataLock);
    return SelfCarData;
}

TMap<FString, FString> UParkingDataProcessManager::GetSlotData(int32 SlotNumber) const
{
    FScopeLock Lock(&DataLock);
    return SlotData[SlotNumber];
}

TMap<FString, FString> UParkingDataProcessManager::GetObstacleData(int32 ObstacleNumber) const
{
    FScopeLock Lock(&DataLock);
    return ObstacleData[ObstacleNumber];
}

TMap<FString, FString> UParkingDataProcessManager::GetTrafficSignData(int32 SignNumber) const
{
    FScopeLock Lock(&DataLock);
    return TrafficSignData[SignNumber];
}

TMap<FString, FString> UParkingDataProcessManager::GetRoadRightPointData(int32 PointNumber) const
{
    FScopeLock Lock(&DataLock);
    return RoadRightPointData[PointNumber];
}

TMap<FString, FString> UParkingDataProcessManager::GetRoadLeftPointData(int32 PointNumber) const
{
    FScopeLock Lock(&DataLock);
    return RoadLeftPointData[PointNumber];
}

TMap<FString, FString> UParkingDataProcessManager::GetPerspectiveModeData() const
{
    FScopeLock Lock(&DataLock);
    return PerspectiveModeData;
}

TArray<FSTCTRACK> UParkingDataProcessManager::GetTrackPlanningPoints() const
{
    FScopeLock Lock(&DataLock);
    return TrackPlanningPoints;
}
TArray<FRoadPoint> UParkingDataProcessManager::GetHPARoadPoints() const
{
    FScopeLock Lock(&DataLock);
    return HPARoadPoints;
}

void UParkingDataProcessManager::ResetHPARoadPoints()
{
    FScopeLock Lock(&DataLock);
    HPARoadPoints.Empty();
}

int UParkingDataProcessManager::GetLineType() const
{
    FScopeLock Lock(&DataLock);
    return LineType;
}


void UParkingDataProcessManager::DeserializeHpaRoadPoint(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& Left, const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& Right)
{
    FScopeLock Lock(&DataLock);
    
    //建立标记位，检测当前数据帧是否有效
    if ( !UGlobalConfig::GetInstance()->IsInHPACreating() ) {
        CARLINX_LOG(LogCarLinx,Log, TEXT("HPA Road Point [DeserializeHpaRoadPoint] HPACreating: %d, quit datafactory."), UGlobalConfig::GetInstance()->IsInHPACreating());
        return;
    }

    //根据校验位判断当前数据是否接收完整，否则继续接收
    auto result = ParkingDataProcessor2UEInternal::DeserializeHpaRoadPoint(Left, Right, HPARoadPoints);
    CARLINX_LOG(LogCarLinx,Log, TEXT("HPA Road Point [DeserializeHpaRoadPoint] value change status %d."), result);
    if (result)
    {
        UGlobalConfig::GetInstance()->DisableHPACreatingFlag();
        FFunctionGraphTask::CreateAndDispatchWhenReady([this]() {
            OnRoadPointDataUpdated.Broadcast();
            }, TStatId(), nullptr, ENamedThreads::GameThread);
    }
}

bool UParkingDataProcessManager2UE::GetProcessedRangeInfo(FVector& OutCenter, float& OutDistance)
{
    const double StartTime = FPlatformTime::Seconds();

    FVector MinRange = FVector(MAX_FLT, MAX_FLT, MAX_FLT);
    FVector MaxRange = FVector(-MAX_FLT, -MAX_FLT, -MAX_FLT);

    // 直接访问处理后的缓存，不要从蓝图再转一次
    auto points = UParkingDataProcessManager::GetInstance()->GetHPARoadPoints();

    if(points.Num() == 0)
    {
        const double EndTime = FPlatformTime::Seconds();
        const double ElapsedMs = (EndTime - StartTime) * 1000.0;
        CARLINX_LOG(LogCarLinx, Log, TEXT("GetProcessedRangeInfo empty points cost: %.2f ms"), ElapsedMs);
        return false;
    }

    // 循环访问points中的数据，计算最大最小值，更新到MinRange、MaxRange中
    for (const auto& point : points)
    {
        MinRange.X = FMath::Min(MinRange.X, point.PointLocation.X);
        MinRange.Y = FMath::Min(MinRange.Y, point.PointLocation.Y);
        MinRange.Z = FMath::Min(MinRange.Z, point.PointLocation.Z);

        MaxRange.X = FMath::Max(MaxRange.X, point.PointLocation.X);
        MaxRange.Y = FMath::Max(MaxRange.Y, point.PointLocation.Y);
        MaxRange.Z = FMath::Max(MaxRange.Z, point.PointLocation.Z);
    }

    // 计算中心点
    OutCenter = (MinRange + MaxRange) * 0.5f;

    // 计算最大距离(对角线长度的一半)
    OutDistance = FVector::Distance(MinRange, MaxRange) * 0.5f;

    const double EndTime = FPlatformTime::Seconds();
    const double ElapsedMs = (EndTime - StartTime) * 1000.0;
    CARLINX_LOG(LogCarLinx, Log, TEXT("GetProcessedRangeInfo cost: %.2f ms, points: %d"), ElapsedMs, points.Num());

    return true;
}