#pragma once
#if PLATFORM_ANDROID
#include <jni.h>
#include <android/log.h>



#define JNI_METHOD			__attribute__ ((visibility ("default"))) extern "C"
#define LOG_TAG "C++"

//定义日志打印宏函数
#define ALOGI(...)  __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define ALOGE(...)  __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define ALOGW(...)  __android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__)
#define ALOGD(...)  __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#endif

#include <string>
class UDatasourceImpl;

void registerDatasource(UDatasourceImpl* datasource);
void PrintLog(std::string str);

#if PLATFORM_ANDROID
JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetStringValue(JNIEnv* env, jclass clazz, jstring name, jstring value);

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetIntValue(JNIEnv* env, jclass clazz, jstring name, jint value);

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetFloatValue(JNIEnv* env, jclass clazz, jstring name, jfloat value);

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetTarryFloatValue(JNIEnv* env, jclass clazz, jstring name, jfloatArray value);

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetTarryVectorValue(JNIEnv* env, jclass clazz, jstring name, jfloatArray XValue,jfloatArray YValue,jfloatArray ZValue);

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_SetTMapVectorValue(JNIEnv* env, jclass clazz, jstring name,jintArray Value,jfloatArray XValue,jfloatArray YValue,jfloatArray ZValue,jfloatArray RotationValue,jintArray Type);

JNI_METHOD void Java_com_hege_datafeeder_AndroidDataFeeder_RegisterCallback(JNIEnv* env, jclass clazz, jobject callback);


#endif
void CallbackPlatformIntValue(std::string name, int value);
void CallbackPlatformFloatValue(std::string name, float value);
void CallbackPlatformStringValue(std::string name, std::string value);
void CallbackPlatformTarryFloatValue(std::string name, TArray<float> value);
void CallbackPlatformTarryVectorValue(std::string name, TArray<float> XValue, TArray<float> YValue, TArray<float> ZValue);
void CallbackPlatformTMapVectorValue(std::string name, TArray<int> Value,TArray<float> XValue, TArray<float> YValue, TArray<float> ZValue, TArray<float> RotationValue, TArray<int> Type);

