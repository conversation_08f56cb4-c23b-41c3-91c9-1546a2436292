// Fill out your copyright notice in the Description page of Project Settings.


#include "DrawLine.h"

#include <string>

// Sets default values
ADrawLine::ADrawLine()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void ADrawLine::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void ADrawLine::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

void ADrawLine::SetLineValue(TArray<FVector> Inpoints, float Breadth)
{
	if (Inpoints.Num() < 2)
		return;
	
	LinePoints.Empty();
	for (int32 i = 0; i < Inpoints.Num(); i++)
	{
		if (i == 0)	//输入点坐标为0时的值
		{
			FVector2D VectorCoord (Inpoints[1] - Inpoints[0]);
			float K = - VectorCoord.X / VectorCoord.Y;
			float C = Inpoints[0].Y - K * Inpoints[0].X;	// y = K * x + C;
			float ThirdSide = (Breadth * Breadth) / (1 + K * K);
			float Distance = sqrt(ThirdSide);
			float x1 = Inpoints[0].X + Distance;
			float x2 = Inpoints[0].X - Distance;
			float y1 = K * x1 + C;
			float y2 = K * x2 + C;
			
			if (VectorCoord.Y > 0)
			{
				FVector EvenPointsFirst(x1,y1,Inpoints[0].Z);
				LinePoints.Add(EvenPointsFirst);
				FVector  OddPointsFirst(x2,y2,Inpoints[0].Z);
				LinePoints.Add(OddPointsFirst);
			} else if (VectorCoord.Y < 0)
			{
				FVector EvenPointsFirst(x2,y2,Inpoints[0].Z);
				LinePoints.Add(EvenPointsFirst);
				FVector  OddPointsFirst(x1,y1,Inpoints[0].Z);
				LinePoints.Add(OddPointsFirst);
			} else if (VectorCoord.Y == 0 && VectorCoord.X > 0)
			{
				FVector EvenPoints(Inpoints[0].X,Inpoints[0].Y - Breadth,Inpoints[0].Z);
				LinePoints.Add(EvenPoints);
				FVector  OddPoints(Inpoints[0].X,Inpoints[0].Y + Breadth,Inpoints[0].Z);
				LinePoints.Add(OddPoints);
			} else if (VectorCoord.Y == 0 && VectorCoord.X < 0)
			{
				FVector EvenPoints(Inpoints[0].X,Inpoints[0].Y + Breadth,Inpoints[0].Z);
				LinePoints.Add(EvenPoints);
				FVector  OddPoints(Inpoints[0].X,Inpoints[0].Y - Breadth,Inpoints[0].Z);
				LinePoints.Add(OddPoints);
			}
		} else if (i == (Inpoints.Num() - 1))	//输入最后一个点坐标的值
		{
			FVector2D VectorCoord ((Inpoints[i] - Inpoints[i - 1]));
			float K = - VectorCoord.X / VectorCoord.Y;
			float C = Inpoints[i].Y - K * Inpoints[i].X;	// y = K * x + C;
			float ThirdSide = (Breadth * Breadth) / (1 + K * K);
			float Distance = sqrt(ThirdSide);
			float x1 = Inpoints[i].X + Distance;
			float x2 = Inpoints[i].X - Distance;
			float y1 = K * x1 + C;
			float y2 = K * x2 + C;
			
			if (VectorCoord.Y > 0)
			{
				FVector EvenPointsFirst(x1,y1,Inpoints[i].Z);
				LinePoints.Add(EvenPointsFirst);
				FVector  OddPointsFirst(x2,y2,Inpoints[i].Z);
				LinePoints.Add(OddPointsFirst);
			} else if (VectorCoord.Y < 0)
			{
				FVector EvenPointsFirst(x2,y2,Inpoints[i].Z);
				LinePoints.Add(EvenPointsFirst);
				FVector  OddPointsFirst(x1,y1,Inpoints[i].Z);
				LinePoints.Add(OddPointsFirst);
			} else if (VectorCoord.Y == 0 && VectorCoord.X > 0)
			{
				FVector EvenPoints(Inpoints[i].X,Inpoints[i].Y - Breadth,Inpoints[0].Z);
				LinePoints.Add(EvenPoints);
				FVector  OddPoints(Inpoints[i].X,Inpoints[i].Y + Breadth,Inpoints[0].Z);
				LinePoints.Add(OddPoints);
			} else if (VectorCoord.Y == 0 && VectorCoord.X < 0)
			{
				FVector EvenPoints(Inpoints[i].X,Inpoints[i].Y + Breadth,Inpoints[0].Z);
				LinePoints.Add(EvenPoints);
				FVector  OddPoints(Inpoints[i].X,Inpoints[i].Y - Breadth,Inpoints[0].Z);
				LinePoints.Add(OddPoints);
			}
		}
		else
		{
			//根据三个点的值求中间点扩大的坐标 A、B、C三个点
			FVector2D AB(Inpoints[i] - Inpoints[i - 1]);
			FVector2D BA(Inpoints[i - 1] - Inpoints[i]);
			FVector2D BC(Inpoints[i + 1] - Inpoints[i]);
			FVector2D AC(Inpoints[i + 1] - Inpoints[i - 1]);
			//求AC向量的垂直向量
			FVector2D ACVertical(AC.Y,- AC.X);
			
			if (AB.Y == 0 && BC.Y == 0) //A`B`C三点为一条水平线
			{
				if (AC.X > 0)
				{
					FVector EvenPoints(Inpoints[i].X,Inpoints[i].Y - Breadth,Inpoints[0].Z);
					LinePoints.Add(EvenPoints);
					FVector  OddPoints(Inpoints[i].X,Inpoints[i].Y + Breadth,Inpoints[0].Z);
					LinePoints.Add(OddPoints);
				} else if (AC.X < 0)
				{
					FVector EvenPoints(Inpoints[i].X,Inpoints[i].Y + Breadth,Inpoints[0].Z);
					LinePoints.Add(EvenPoints);
					FVector  OddPoints(Inpoints[i].X,Inpoints[i].Y - Breadth,Inpoints[0].Z);
					LinePoints.Add(OddPoints);
				}
			} else if ((AB.Y / AB.X) == (BC.Y / BC.X))	//A`B`C为三点为一条直线
			{
				double AngleAC = atan(ACVertical.Y / ACVertical.X);
				float KAC = ACVertical.Y / ACVertical.X;
				float BAC = Inpoints[i].Y - KAC * Inpoints[i].X;
				float XLenth = Breadth * cos(AngleAC);
				float XAC1 = Inpoints[i].X + XLenth;
				float XAC2 = Inpoints[i].X - XLenth;
				float YAC1 = KAC * XAC1 + BAC;
				float YAC2 = KAC * XAC2 + BAC;
				if (AC.X > 0)
				{
					FVector EvenPoints(XAC1,YAC1,Inpoints[0].Z);
					LinePoints.Add(EvenPoints);
					FVector  OddPoints(XAC2,YAC2,Inpoints[0].Z);
					LinePoints.Add(OddPoints);
				} else if (AC.X < 0)
				{
					FVector EvenPoints(XAC2,YAC2,Inpoints[0].Z);
					LinePoints.Add(EvenPoints);
					FVector  OddPoints(XAC1,YAC1,Inpoints[0].Z);
					LinePoints.Add(OddPoints);
				}
			}
			else
			{
				//求BA和BC的模长
				float ModeLengthBA = sqrt(BA.X * BA.X + BA.Y * BA.Y);
				float ModeLengthBC = sqrt(BC.X * BC.X + BC.Y * BC.Y);
				//求BA和BC的角平分值
				FVector2D AngularBisectorA(BA / ModeLengthBA);
				FVector2D AngularBisectorB(BC / ModeLengthBC);
				FVector2D AngularBisector(AngularBisectorA + AngularBisectorB);
				//求BA和BC的夹角
				double AngleA = 0.5 * acos((BA.X * BC.X + BA.Y * BC.Y) / (ModeLengthBA * ModeLengthBC));
				//求第三遍长度
				float ThirdLength = Breadth / sin(AngleA);
				//求模长夹角
				double AngleB = atan(AngularBisector.Y / AngularBisector.X);
				float K = AngularBisector.Y / AngularBisector.X;
				float B = Inpoints[i].Y - K * Inpoints[i].X;
				float XLength = ThirdLength * cos(AngleB);
				float x1 = Inpoints[i].X + XLength;
				float x2 = Inpoints[i].X - XLength;
				float y1 = K * x1 + B;
				float y2 = K * x2 + B;
				//判断线段延申方向
				if (AC.Y > 0)
				{
					FVector EvenPoints(x1,y1,Inpoints[i].Z);
					LinePoints.Add(EvenPoints);
					FVector  OddPoints(x2,y2,Inpoints[i].Z);
					LinePoints.Add(OddPoints);
				} else if (AC.Y < 0)
				{
					FVector EvenPoints(x2,y2,Inpoints[i].Z);
					LinePoints.Add(EvenPoints);
					FVector  OddPoints(x1,y1,Inpoints[i].Z);
					LinePoints.Add(OddPoints);
				}
			}
		}
	}
	
	LineTriangles.Empty();
	if (LinePoints.Num() < 4)
		return;
	
	for (int32 i = 0; i < LinePoints.Num(); i++)
	{
		if (i % 2 == 1)
		{
			LineTriangles.Add(i);
			LineTriangles.Add(i + 1);
			LineTriangles.Add(i - 1);
			LineTriangles.Add(i);
			LineTriangles.Add(i + 2);
			LineTriangles.Add(i + 1);
		}
	}
}