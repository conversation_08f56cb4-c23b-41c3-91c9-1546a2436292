#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "BFL_soLoad.generated.h"

/**
 * soLoad插件的全局静态接口类
 */
UCLASS()
class SOLOAD_API UBFL_soLoad : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	// 获取当前FPS
	UFUNCTION(BlueprintCallable, Category = "soLoad|Performance")
	static float GetCurrentFPS();

	// 获取当前帧时间（毫秒）
	UFUNCTION(BlueprintCallable, Category = "soLoad|Performance")
	static float GetCurrentFrameTime();

	// 获取平均帧时间（毫秒）
	UFUNCTION(BlueprintCallable, Category = "soLoad|Performance")
	static float GetAverageFrameTime();

	// 打印性能日志（动态信息）
	UFUNCTION(BlueprintCallable, Category = "soLoad|Performance")
	static void LogPerformanceInfo();

	// 获取UE版本
	UFUNCTION(BlueprintCallable, Category = "soLoad|Engine")
	static FString GetUEVersion();

	// 获取引擎状态信息（静态信息）
	UFUNCTION(BlueprintCallable, Category = "soLoad|Engine")
	static void LogEngineInfo();

	// 获取平台信息
	UFUNCTION(BlueprintCallable, Category = "soLoad|Platform")
	static FString GetPlatformInfo();

	// 获取系统静态信息（不会变化的信息）
	UFUNCTION(BlueprintCallable, Category = "soLoad|Platform")
	static void LogSystemStaticInfo();

	// 获取系统动态信息（会变化的信息）
	UFUNCTION(BlueprintCallable, Category = "soLoad|Platform")
	static void LogSystemDynamicInfo();

	// 获取所有静态信息（启动时调用一次）
	UFUNCTION(BlueprintCallable, Category = "soLoad|Info")
	static void LogAllStaticInfo();

	// 获取所有动态信息（定期调用）
	UFUNCTION(BlueprintCallable, Category = "soLoad|Info")
	static void LogAllDynamicInfo();

	// 强制打印所有动态信息（忽略时间间隔）
	UFUNCTION(BlueprintCallable, Category = "soLoad|Info")
	static void ForceLogAllDynamicInfo();

	// 获取系统CPU使用率
	UFUNCTION(BlueprintCallable, Category = "soLoad|Performance")
	static float GetSystemCPUUsage();

	// 获取当前进程CPU使用率
	UFUNCTION(BlueprintCallable, Category = "soLoad|Performance")
	static float GetProcessCPUUsage();

	// 打印自定义字符串
	UFUNCTION(BlueprintCallable, Category = "soLoad|Debug", meta = (DisplayName = "打印字符串"))
	static void LogCustomString(const FString& Message);

	// 更新CPU使用率采样
	static void UpdateCPUSamples();

private:
	static const int32 CPU_SAMPLE_COUNT = 10;  // 采样数量
	static const float CPU_SAMPLE_INTERVAL; // 采样间隔（秒）
	static const float LOG_INTERVAL; // 日志打印间隔（秒）

	struct FCPUSample
	{
		double TimeStamp;
		double CPUTime;
		float Usage;

		FCPUSample() : TimeStamp(0), CPUTime(0), Usage(0) {}
	};

	static FCriticalSection SampleDataLock;
	static TArray<FCPUSample> SystemCPUSamples;  // 系统CPU使用率采样
	static TArray<FCPUSample> ProcessCPUSamples; // 进程CPU使用率采样
	
	static double LastSampleTime;  // 上次采样时间
	static double LastLogTime;     // 上次日志打印时间


	// 计算采样平均值
	static float CalculateAverageUsage(const TArray<FCPUSample>& Samples);
	// 打印动态信息的内部实现
	static void LogDynamicInfoImpl();
};