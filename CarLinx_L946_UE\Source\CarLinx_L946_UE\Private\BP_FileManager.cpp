// Fill out your copyright notice in the Description page of Project Settings.


#include "BP_FileManager.h"

#include "FileManager.h"
#include "Serialization/JsonWriter.h"
#include "Serialization/JsonSerializer.h"
#include "Dom/JsonObject.h"
#include "Engine/Texture2D.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "JsonObjectConverter.h"
#include "Engine/Texture2D.h"
#include "HAL/PlatformFilemanager.h"
#include "IImageWrapper.h"
#include "IImageWrapperModule.h"
#include "Modules/ModuleManager.h"
#include "Rendering/Texture2DResource.h"
#include "Logger.h"

// Sets default values
ABP_FileManager::ABP_FileManager()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

#if 1

    BasePath = TEXT("/data/carlinx_data/HPA");
    FlieName = TEXT("/data/carlinx_data/HPA/Into/");
    FlieOutName = TEXT("/data/carlinx_data/HPA/Out/");
    AllMapIDName = TEXT("/data/carlinx_data/HPA/HPAMapID/");

    UFileManager::CreateDirectory(BasePath);
    UFileManager::CreateDirectory(FlieName);
    UFileManager::CreateDirectory(FlieOutName);
    UFileManager::CreateDirectory(AllMapIDName);

#else
    FlieName = TEXT("/data/carlinx_data/HPA/Into/");
    FlieOutName = TEXT("/data/carlinx_data/HPA/Out/");
    AllMapIDName = TEXT("/data/carlinx_data/HPA/HPAMapID/");
#endif


}

// Called when the game starts or when spawned
void ABP_FileManager::BeginPlay()
{
	Super::BeginPlay();
    CreateFile();
    UFileManager::CreateJsonFile(AllMapIDName);
    HasReadAndWritePermissions(AndroidFile);

}

// Called every frame
void ABP_FileManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

void ABP_FileManager::CreateFile()
{
    // ��ȡĿ��·��
    FString FolderPaths = FlieName;

    CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMengCreateLuJing:]FolderPaths:"), *FolderPaths);
    // ��ȡƽ̨�ļ��ӿ�
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    // �����ļ����Ƿ���ڣ���������ڣ��򴴽�
    if (!UFileManager::FileExists(*FolderPaths))
    {
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]NoCuiZaiFolderPaths: %s"), *FolderPaths);
        if (UFileManager::CreateDirectory(*FolderPaths))
        {
            //UE_LOG(LogTemp, Log, TEXT("Flie Create Chenggong��%s"), *FolderPaths);
            CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Flie Create Chenggong: %s"), *FolderPaths);
        }
        else
        {
            //UE_LOG(LogTemp, Warning, TEXT("No Create Shibai��%s"), *FolderPaths);
            CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]No Create Shibai: %s"), *FolderPaths);
        }
    }
    else
    {
        //UE_LOG(LogTemp, Log, TEXT("Flie is Cuizai��%s"), *FolderPaths);
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Flie is Cuizai: %s"), *FolderPaths);
    }

    if (!UFileManager::FileExists(*FlieOutName))
    {
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Flie is NoCuizaiFlieOutName: %s"), *FlieOutName);
        if (UFileManager::CreateDirectory(*FlieOutName))
        {
            CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Flie is ChenggongFlieOutName: %s"), *FlieOutName);
            //UE_LOG(LogTemp, Log, TEXT("No��%s"), *FlieOutName);
        }
        else
        {
            CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Flie is ShibaiFlieOutName: %s"), *FlieOutName);
            //UE_LOG(LogTemp, Warning, TEXT("NoFlie��%s"), *FlieOutName);
        }
    }
    else
    {
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Flie is CuizaiFlieOutName: %s"), *FlieOutName);
        //UE_LOG(LogTemp, Log, TEXT("yijing��%s"), *FlieOutName);
    }
    if (UFileManager::FileExists(*BasePath))
    {
        UFileManager::SetCurrentFilePermissions(BasePath, true);
    }
}

TArray<FString> ABP_FileManager::GetFoldersInDirectory(const FString& DirectoryPath)
{
    TArray<FString> SubDirectoryNames;

    // ��ȡƽ̨�ļ�ʵ��
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();

    // ���·���Ƿ���Ч
    if (!UFileManager::FileExists(*DirectoryPath))
    {
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]DirectoryPathWuxiaoName: %s"), *DirectoryPath);
        //UE_LOG(LogTemp, Warning, TEXT("Directory does not exist: %s"), *DirectoryPath);
        return SubDirectoryNames; // ���ؿ�����
    }

    // ������ǰĿ¼���ǵݹ飩
    PlatformFile.IterateDirectory(*DirectoryPath, [&SubDirectoryNames](const TCHAR* FilenameOrDirectory, bool bIsDirectory) {
        // ������ļ��У��������ִ洢��������
        if (bIsDirectory)
        {
            FString DirectoryName = FPaths::GetCleanFilename(FilenameOrDirectory);
            CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Found Directory Name: %s"), *DirectoryName);
            SubDirectoryNames.Add(DirectoryName);
            //UE_LOG(LogTemp, Log, TEXT("Found Directory Name: %s"), *DirectoryName);
        }
        return true; // ���� true ��ʾ��������
        });

    // ���û���ҵ��κ��ļ��У���¼��־
    if (SubDirectoryNames.Num() == 0)
    {
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]No subdirectories found: %s"), *DirectoryPath);
        //UE_LOG(LogTemp, Warning, TEXT("No subdirectories found in: %s"), *DirectoryPath);
    }

    return SubDirectoryNames;
}

TArray<FString> ABP_FileManager::GetFolder(const int32 Num)
{

    TArray<FString> SubDirNames;
    FString _name;
    if(Num==0)
    {
        _name = FlieName;
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Name: %s"), *_name);
        SubDirNames = GetFoldersInDirectory(_name);
        return SubDirNames;
    }
    else
    {
        _name = FlieOutName;
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Name: %s"), *_name);
        SubDirNames = GetFoldersInDirectory(_name);
        return SubDirNames;
    }
 
}

FString ABP_FileManager::CreateIDFlie(const FString& Index)
{
    CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]Index: %s"), *Index);
    int32 IntValue = FCString::Atoi(*Index);

    FString IDName;
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (IntValue <50)
    {
        FString Name = FlieName;
        IDName = Name.Append(Index);
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]IDName: %s"), *IDName);
        

        if (!UFileManager::FileExists(*IDName))
        {
            CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]IDNamebucuizai: %s"), *IDName);
            if (UFileManager::CreateDirectory(*IDName))
            {
                CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]IDName Create Chenggong: %s"), *IDName);
                //UE_LOG(LogTemp, Log, TEXT("Flie Create Chenggong��%s"), *FolderPaths);
            }
            else
            {
                CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]IDName Create Shibai: %s"), *IDName);
                //UE_LOG(LogTemp, Warning, TEXT("No Create Shibai��%s"), *FolderPaths);
            }
        }
        else
        {
            CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]IDNameCunzai: %s"), *IDName);
            //UE_LOG(LogTemp, Log, TEXT("Flie is Cuizai��%s"), *FolderPaths);
        }
   
    }
    else
    {
        FString Name = FlieOutName;
        IDName = Name.Append(Index);
        CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]FlieOutName50: %s"), *IDName);
        if (!UFileManager::FileExists(*IDName))
        {
            CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]FlieOutName50Bucuizai: %s"), *IDName);
            if (UFileManager::CreateDirectory(*IDName))
            {
                CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]FlieOutName50Chenggong: %s"), *IDName);
                //UE_LOG(LogTemp, Log, TEXT("Flie Create Chenggong��%s"), *FolderPaths);
            }
            else
            {
                CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]FlieOutName50Shibai: %s"), *IDName);
                //UE_LOG(LogTemp, Warning, TEXT("No Create Shibai��%s"), *FolderPaths);
            }
        }
        else
        {
            CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]FlieOutName50Cuizai: %s"), *IDName);
            //UE_LOG(LogTemp, Log, TEXT("Flie is Cuizai��%s"), *FolderPaths);
        }
    }
    CARLINX_LOG(LogCarLinx,Log, TEXT("[HPAMeng:]IDName End: %s"), *IDName);
    return IDName;
}

void ABP_FileManager::SaveRoadPoints(const TArray<FRoadPoint>& Points, const FString& SavePath)
{
    CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPASaveRoadPointsBegin:]"), *SavePath);
    TArray<TSharedPtr<FJsonValue>> JsonArray;
    for (const FRoadPoint& Point : Points)
    {
        TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject());
        if (FJsonObjectConverter::UStructToJsonObject(FRoadPoint::StaticStruct(), &Point, JsonObject.ToSharedRef(), 0, 0))
        {
            JsonArray.Add(MakeShareable(new FJsonValueObject(JsonObject)));
        }
    }
    // �� JSON ����д���ַ���
    FString JsonOutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonOutputString);
    FJsonSerializer::Serialize(JsonArray, Writer);

    // ȷ������·������
    FString FullPath = FPaths::ConvertRelativePathToFull(SavePath);
    CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPASaveRoadPointsBeginFullPath:]"), *FullPath);
    FPaths::NormalizeDirectoryName(FullPath);
    IFileManager& FileManager = IFileManager::Get();
    if (!FileManager.DirectoryExists(*FPaths::GetPath(FullPath)))
    {
        FileManager.MakeDirectory(*FPaths::GetPath(FullPath), true);
    }

    // �� JSON д���ļ�
    if (FFileHelper::SaveStringToFile(JsonOutputString, *FullPath))
    {
        UE_LOG(LogTemp, Log, TEXT("File saved successfully to %s"), *FullPath);
        CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPAFile saved successfully to:]"), *FullPath);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to save file to %s"), *FullPath);
        CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPAFailed to save file to:]"), *FullPath);
    }
}

TArray<FRoadPoint> ABP_FileManager::LoadRoadPointsFromJson(const FString& LoadPath)
{
    TArray<FRoadPoint> Points; // ���ڴ洢�����л�������
    FString JsonInputString;

    // ���ļ���ȡ JSON �ַ���
    if (!FFileHelper::LoadFileToString(JsonInputString, *LoadPath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load JSON file: %s"), *LoadPath);
        return Points; // ���ؿ�����
    }

    // �� JSON �ַ�������Ϊ����
    TArray<TSharedPtr<FJsonValue>> JsonArray;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonInputString);
    if (!FJsonSerializer::Deserialize(Reader, JsonArray))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON file: %s"), *LoadPath);
        return Points; // ���ؿ�����
    }

    // ��ÿ�� JSON ����ת��Ϊ FRoadPoint
    for (const TSharedPtr<FJsonValue>& JsonValue : JsonArray)
    {
        TSharedPtr<FJsonObject> JsonObject = JsonValue->AsObject();
        if (JsonObject.IsValid())
        {
            FRoadPoint Point;
            if (FJsonObjectConverter::JsonObjectToUStruct(JsonObject.ToSharedRef(), FRoadPoint::StaticStruct(), &Point, 0, 0))
            {
                Points.Add(Point);
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Loaded %d RoadPoints from JSON file: %s"), Points.Num(), *LoadPath);
    return Points;
}

FDatajson ABP_FileManager::ReadJsonFile(const FString& FilePath)
{
    FDatajson _data;
    // JSON �ļ�·��
  //  FString FilePath = FPaths::Combine(FPaths::ProjectDir(), TEXT("Into/0/Data.json"));

    // �ļ����ݴ洢�ַ���
    FString JsonString;

    // ����ļ��Ƿ�ɹ���ȡ
    if (FFileHelper::LoadFileToString(JsonString, *FilePath))
    {
        // ���� JSON Reader
        TSharedRef<TJsonReader<TCHAR>> JsonReader = TJsonReaderFactory<TCHAR>::Create(JsonString);

        // ���� JSON ����
        TSharedPtr<FJsonObject> JsonObject;
        if (FJsonSerializer::Deserialize(JsonReader, JsonObject) && JsonObject.IsValid())
        {
            // �� JSON ����ȡ����
           _data.ID = JsonObject->GetStringField(TEXT("ID"));
            _data.Name = JsonObject->GetStringField(TEXT("Name"));
            _data.Time = JsonObject->GetStringField(TEXT("Date"));
            _data.XunhangTime = JsonObject->GetStringField(TEXT("XunhangTime"));
            CARLINX_LOG(LogCarLinx, Log, TEXT("parse JSON file Success"));
            return _data;
        }
        else
        {
            //UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON file."));
            CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to parse JSON file."));
            return _data;
        }
    }
    else
    {
        //UE_LOG(LogTemp, Error, TEXT("Failed to load file: %s"), *FilePath);
        CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to load file: %s"), *FilePath);
        return _data;
    }
}

UTexture2D* ABP_FileManager::LoadTextureFromPNG(const FString& FilePath)
{
    // ��֤�ļ��Ƿ����
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("File does not exist: %s"), *FilePath);
        return nullptr;
    }

    // ��ȡ�ļ�Ϊ����������
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *FilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load file: %s"), *FilePath);
        return nullptr;
    }

    // ��ȡͼ�������ģ��
    IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked<IImageWrapperModule>(FName("ImageWrapper"));
    TSharedPtr<IImageWrapper> ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::PNG);

    // ���� PNG ����
    if (ImageWrapper.IsValid() && ImageWrapper->SetCompressed(FileData.GetData(), FileData.Num()))
    {
        TArray<uint8> UncompressedBGRA;
        if (ImageWrapper->GetRaw(ERGBFormat::BGRA, 8, UncompressedBGRA)) // �޸�Ϊ��������
        {
            // ���� UTexture2D
            UTexture2D* Texture = UTexture2D::CreateTransient(
                ImageWrapper->GetWidth(),
                ImageWrapper->GetHeight(),
                PF_B8G8R8A8
            );

            if (!Texture)
            {
                UE_LOG(LogTemp, Error, TEXT("Failed to create texture."));
                return nullptr;
            }

            // ����������д����������
            void* TextureData = Texture->PlatformData->Mips[0].BulkData.Lock(LOCK_READ_WRITE);
            FMemory::Memcpy(TextureData, UncompressedBGRA.GetData(), UncompressedBGRA.Num());
            Texture->PlatformData->Mips[0].BulkData.Unlock();

            // ��������
            Texture->UpdateResource();

            return Texture;
        }
    }

    UE_LOG(LogTemp, Error, TEXT("Failed to decode image file: %s"), *FilePath);
    return nullptr;
}

bool ABP_FileManager::CreateJsonFile(const FString& AllMapIDFileName)
{

    FString FilePath = FPaths::Combine(AllMapIDFileName, TEXT("MapID.json"));

    // ����ļ��Ƿ��Ѵ���
    if (FPaths::FileExists(FilePath))
    {
        UE_LOG(LogTemp, Warning, TEXT("File already exists: %s"), *FilePath);
        return false; // �������ļ�
    }

    // ���� JSON ����
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);

    TArray<TSharedPtr<FJsonValue>> DefaultList;
    for (int32 i = 0; i < 32; ++i)
    {
        DefaultList.Add(MakeShareable(new FJsonValueNumber(0)));
    }

    JsonObject->SetArrayField(TEXT("List1"), DefaultList);
    JsonObject->SetArrayField(TEXT("List2"), DefaultList);
    JsonObject->SetArrayField(TEXT("List3"), DefaultList);
    JsonObject->SetArrayField(TEXT("List4"), DefaultList);

    // �� JSON ���л�Ϊ�ַ���
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);

    if (!FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to serialize JSON content."));
        return false;
    }

    // ȷ���ļ�·�����洢����ĿĿ¼�£�
  //  FString FilePath = FPaths::Combine(AllMapIDFileName, TEXT("MapID.json"));

    // ���浽�ļ�
    if (!FFileHelper::SaveStringToFile(OutputString, *FilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to save file: %s"), *FilePath);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("JSON file created successfully at: %s"), *FilePath);
    return true;
}
/*����1���ļ�·��
  ����2��ListName
  ����3��λ��
  ����4���޸ĵ�ֵ0/1
*/
bool ABP_FileManager::ModifyJsonValue(const FString& ListName, int32 Index, int32 ModifyValue)
{

    FString IDFilePath = FPaths::Combine(AllMapIDName, TEXT("MapID.json"));

    CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue] ModifyJsonValueFile '%s'."), *IDFilePath);
    // ����ļ��Ƿ��Ѵ���
    if (FPaths::FileExists(IDFilePath))
    {
        CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue] File already exists: %s"), *IDFilePath);
        UE_LOG(LogTemp, Warning, TEXT("File already exists: %s"), *IDFilePath);
      //  return false; // �������ļ�

        if (Index < 0 || Index > 31)
        {
            UE_LOG(LogTemp, Error, TEXT("Index out of range. It should be between 0 and 31."));
            CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue] It should be between 0 and 31"));
            return false;
        }

        // ��ȡ JSON �ļ�
        FString FileContent;
        if (!FFileHelper::LoadFileToString(FileContent, *IDFilePath))
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to load file: %s"), *IDFilePath);
            CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue] Failed to load file:"), *IDFilePath);
            return false;
        }

        // ���� JSON ����
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(FileContent);

        if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON content."));
            CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue] Failed to parse JSON content."), *IDFilePath);
            return false;
        }

        // ����Ƿ����ָ���� List
        if (!JsonObject->HasField(ListName))
        {
            UE_LOG(LogTemp, Error, TEXT("JSON does not contain the field: %s"), *ListName);
            CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue] JSON does not contain the field: ."), *ListName);
            return false;
        }

        // ��ȡ���鲢�޸�ָ��λ�õ�ֵ
        TArray<TSharedPtr<FJsonValue>> ListArray = JsonObject->GetArrayField(ListName);
        if (Index >= ListArray.Num())
        {
            UE_LOG(LogTemp, Error, TEXT("Index exceeds array bounds for list: %s"), *ListName);
            CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue] Index exceeds array bounds for list: %s "), *ListName);
            return false;
        }

        // ��ָ��λ�õ�ֵ�޸�Ϊ 1
        ListArray[Index] = MakeShared<FJsonValueNumber>(ModifyValue);
        JsonObject->SetArrayField(ListName, ListArray);

        // ���޸ĺ�� JSON д���ļ�
        FString UpdatedContent;
        TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&UpdatedContent);

        if (!FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer))
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to serialize JSON content."));
            CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue] Failed to serialize JSON content."));
            return false;
        }

        if (!FFileHelper::SaveStringToFile(UpdatedContent, *IDFilePath))
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to save file: %s"), *IDFilePath);
            CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue]Failed to save file : % s"), *IDFilePath);
            return false;
        }

        UE_LOG(LogTemp, Log, TEXT("Successfully updated %s[%d] to 1 in JSON file: %s"), *ListName, Index, *IDFilePath);
        CARLINX_LOG(LogCarLinx,Warning, TEXT("[ModifyJsonValue]Successfully updated %s[%d] to 1 in JSON file: %s"), *ListName, Index, *IDFilePath);
        return UFileManager::SetCurrentFilePermissions(IDFilePath, false);;
    }
    return false;
}

bool ABP_FileManager::HasReadAndWritePermissions(const FString& Path)
{
    // ���Ŀ¼�Ƿ����
    if (!FPaths::DirectoryExists(Path))
    {
        CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPA]Directory does not exist: %s"), *Path);
        return false;
    }

    // ����ȡȨ��
    FString TestReadFilePath = FPaths::Combine(Path, TEXT("test_read.txt"));
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*TestReadFilePath))
    {
      //  UE_LOG(LogTemp, Warning, TEXT("Read permission not available: %s"), *TestReadFilePath);
        CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPA]Read permission not available: %s"), *TestReadFilePath);
        return false;
    }

    // ���д��Ȩ��
    FString TestWriteFilePath = FPaths::Combine(Path, TEXT("test_write.txt"));
    if (!FFileHelper::SaveStringToFile(TEXT("test"), *TestWriteFilePath))
    {
       // UE_LOG(LogTemp, Warning, TEXT("Write permission not available: %s"), *TestWriteFilePath);
        CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPA]Write permission not available: %s"), *TestWriteFilePath);
        return false;
    }

    // �ɹ������ж�ȡ��д��Ȩ��
  //  UE_LOG(LogTemp, Log, TEXT("Read and Write permissions available: %s"), *Path);
    CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPA]Read and Write permissions available: %s"), *Path);
    return true;
}

void ABP_FileManager::UpdateJsonWithTArrays(const TArray<int32>& List1, const TArray<int32>& List2, const TArray<int32>& List3, const TArray<int32>& List4)
{

    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject());

    // �� TArray<int32> ת��Ϊ JSON ����
    TArray<TSharedPtr<FJsonValue>> JsonArray1, JsonArray2, JsonArray3, JsonArray4;

    for (int32 Value : List1) JsonArray1.Add(MakeShareable(new FJsonValueNumber(Value)));
    for (int32 Value : List2) JsonArray2.Add(MakeShareable(new FJsonValueNumber(Value)));
    for (int32 Value : List3) JsonArray3.Add(MakeShareable(new FJsonValueNumber(Value)));
    for (int32 Value : List4) JsonArray4.Add(MakeShareable(new FJsonValueNumber(Value)));

    // ��� JSON ����
    JsonObject->SetArrayField(TEXT("List1"), JsonArray1);
    JsonObject->SetArrayField(TEXT("List2"), JsonArray2);
    JsonObject->SetArrayField(TEXT("List3"), JsonArray3);
    JsonObject->SetArrayField(TEXT("List4"), JsonArray4);

    // �� JSON �������л�Ϊ�ַ���
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    // д���ļ�
    if (FFileHelper::SaveStringToFile(OutputString, *AllMapIDName))
    {
        CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPA]JSON successfully updated at: %s"), *AllMapIDName);
    }
    else
    {
        CARLINX_LOG(LogCarLinx,Log, TEXT("[MengHPA]Failed to save JSON at: %s"), *AllMapIDName);
    }

}

