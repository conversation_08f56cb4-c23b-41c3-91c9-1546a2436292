﻿#pragma once
#include <iostream>
#include <string>
#include <vector>
#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "UObject/Object.h"
#include" ../../../../soLoad/Source/soLoad/Public/Logger.h"
#include "Subscriber.generated.h"








UENUM(BlueprintType)
enum class EVehicleEventType : uint8
{
	System_Change,
	GearType,
	DayNightMode,
	PDCVolume,
	RCTAWarning,
	Widget_Finish,
	PDCVolume_Sts,
	DebugShow,
	CameraSts,
	RPAWarn,

	// 车轮和车门状态
	FWheelRotateDir,
	RWheelRotateDir,
	FWheelRotateSpeed,
	RWheelRotateSpeed,
	WhlRotToothCntr,
	DrvDoorSts,
	PassDoorSts,
	LeReDoorSts,
	RiReDoorSts,
	HoodSts,
	TrunkSts,
	HpaSettingInterface,
	// 窗户和天窗
	DrvWinPer,
	PassWinPer,
	LeReWinPer,
	RiReWinPer,
	SunroofPer,

	// 后视镜状态
	DrvMirrSts,
	PassMirrSts,

	// 灯光和座椅状态
	LowBeam,
	HighBeam,
	TurnLeftLightSts,
	TurnRightLightSts,
	DoubleFlash,
	BreakLightSts,
	DrvSeatfoldSts,

	// 车辆运动状态
	CarRoll,
	CarPitch,
	CarYaw,
	DrivePosition,
	CarSpeed,
	SteeringAngle,
	DoubleFlashStatus,

	// AVM相关
	Avm5sTimer,
	AvmWanringA0,
	AvmWanringA1,
	AvmWanringA2,
	AvmWanringA3,
	AvmWanringB1,
	AvmWanringB2,
	AvmWanringB3,
	AvmWanringB4,
	AvmTrunkWarning,
	AvmCurrViewType,
	AvmViewTwoD,
	AvmViewThreeD,
	AvmSettingPage,
	AvmTransparentCar,
	AvmSteeringlinkage,
	AvmNarrowActive,
	AvmFactoryStatus,
	AvmVersionNum,
	AvmCalib,
	AvmCalibProgress,
	AvmCamFaultFront,
	AvmCamFaultRear,
	AvmCamFaultLeft,
	AvmCamFaultRight,

	// APA相关
	DrvrAsscSysSts,
	APABtnStatus,
	APA_Quit,
	APA_RadarMute,
	APA_To360,
	APA_ViewSwitching,
	APA_ParkinReq,
	APA_ParkoutReq,
	APA_Setting,
	APA_ContPark,
	APA_ParkingWithoutFeeling,
	APA_LanguageBroadcast,
	APA_BroadcastMode,
	APA_SRViewChange,
	APA_CenterIn,
	APA_LeftIn,
	APA_RightIn,
	APA_LeftOut,
	APA_RightOut,
	APA_StartParkOut,
	APA_WorkSts,
	APA_TouchX,
	APA_TouchY,
	APA_FingerIndex,
	APA_TouchEnvTyp,

	APA_FrntAndReSwt,
	APA_FrntAndReSwt_Stat,
	APA_ViewSwitching_BtnStat,

	APA_Quit_Stat,
	APA_RadarMute_Stat,
	APA_To360_Stat,
	APA_ViewSwitching_Stat,

	APA_ParkinReq_Stat,
	APA_ParkoutReq_Stat,
	APA_Setting_Stat,
	APA_SettingBtnSts_Stat,
	APA_ContPark_Stat,
	APA_ViewID_Stat,
	APA_ParkingWithoutFeeling_Stat,
	APA_LanguageBroadcast_Stat,
	APA_BroadcastMode_Stat,
	APA_SRViewChange_Stat,
	APA_CenterIn_Stat,
	APA_LeftIn_Stat,
	APA_RightIn_Stat,
	APA_LeftOut_Stat,
	APA_RightOut_Stat,
	APA_StartParkOut_Stat,

	APA_StrtPrkg_Stat,

	APA_PerFrontLeftOut_Stat,
	APA_PerFrontRightOut_Stat,
	APA_PerBackLeftOut_Stat,
	APA_PerBackRightOut_Stat,

	APA_ParkingPercent,
	APA_ParkOutInterface_Stat,
	APA_WarningID,
	APA_WarningAID,
	APA_ParkInSurplusDistance_Stat,

	//RPA
	RPA_CarInPark_Stat,
	RPA_RemCtrlPrkg_Stat,
	PrkgAssiSysRemPrkgSts,

	//HPA
	HPASettingPage,
	SmarkGeneration,
	FuncPush,
	HPAVoiceSts,
	MyRouteSts,
	ParkInRouteNum,
	ParkOutRouteNum,
	BuildDistance,//记忆距离
	CrossNum,//路口数量
	RampNum,
	DecZoneNum,
	ParkInDisLeft,

	BuildDistanceVisible,
	CrossNumVisible,
	DecZoneNumVisible,
	DistanceToEndVisible,
	AvoidPersonsVisible,
	AvoidCarsVisible,
	RampNumVisible,
	ParkInDisLeftVisible,

	CuriseTimeLeft,
	AvoidPersons,
	AvoidCars,
	//DistanceToEnd,
	TotalCurisingTime,
	FirstParkMapTime,
	//FirstParkMapDistance,
	//FirstParkMapSts,
	Recentlyused,
	FirstParkPhoneDis,
	SecParkMapTime,
	SecParkMapDistance,
	SecParkMapSts,
	SecParkPhoneDis,
	HPASts,
	PhoneCtrlSts,
	HPASumPage,
	HPARecoverBtn,
	//case到这
	HPAFinishBtn,
	BtnSettingSts,
	BtnExitSts,
	BtnParkInSts,
	BtnParkOutSts,
	HPABtnMuteSts,
	HPAPDCVolume,
	ParkInRouteCover,
	ParkOutRouteCover,
	RouteSelectionVisibility,
	StartMemoParking,
	BuildRouteCard,
	ForwardRouteCard,
	ParkInRouteOver,
	ParkOutRouteOver,
	HPACurentViewType,
	HPAWorkingSts,
	HPACarport,
	HPATargetVehicle,
	HPADrawRoad,
	HPAWarn,
	HPASmallWarn,
	HPASystemWarn,
	HPASystemWarnType,
	LSDAWarn,
	HPAViewName,
	GlobalViewBtn,
	PartViewBtn,
	CruiseTime,//预计巡航时间

	//PDC
	RML_RadarStatus,
	RMR_RadarStatus,
	RL_RardarStatus,
	RR_RadarStatus,
	FL_RadarStatus,
	FR_RadarStatus,
	FML_RadarStatus,
	FMR_RadarStatus,
	LFSide_RadarStatus,
	RFSide_RadarStatus,
	LRSide_RadarStatus,
	RRSide_RadarStatus,
	AvmFrontRadarNotise,
	AvmRearRadarNotise,
	AvmFRadarDistance,
	AvmRRadarDistance,
	AvmFRadarNumLocation,
	AvmRRadarNumLocation,

	//LSDA
	LSDA_Forward,
	LSDA_Backwards,
	LSDA_Continue,
	LSDA_Setting,
	LSDA_FeaturePush,
	LSDA_WarningA,
	LSDA_WarningB,
	TBA_WarningA,
	TBA_WarningB,
	LSDA_ForwardStatus,
	LSDA_BackwardsStatus,
	LSDA_ContinueStatus,
	LSDA_SettingStatus,
	LSDA_FeaturePushStatus,
	LSDA_ViewSwitchingStatus,
	LSDA_ParkingPercent,
	LSDA_QuitStatus,

	LSDA_SettingPageStatus,

	ValtPrkgMapGeneperctg,//路线生成进度
	ParkInPercent,//泊车进度
	HPA_ViewSwitching_Stat,//切换视图
	ValtPrkgViewDisp,

	//
	FirstParkMapID,
	FirstParkMapDistance,
	FirstParkMapSts,
	FirstParkMapRecentlyused,

	UsgModSts,//车机状态
	//校验删除
	ValtPrkgMapSyncnReq,
	ValtPrkgMapSyncnReqMapList1,
	ValtPrkgMapSyncnReqMapList2,
	ValtPrkgMapSyncnReqMapList3,
	ValtPrkgMapSyncnReqMapList4,
	MapInfoIsVis,
	//巡航时间
	ValtPrkgEstdCrsTi,
	ValtPrkgTotCrsTi,
	ValtPrkgCrsRemiTi,
	//1 泊入 2泊出
	createHpaRoute,
	HPA_BeginMoveBtn,
	//安卓控制显示和退出设置页
	HPA_IsOpenSettingPage,
	APA_ParkInSurplusDistance,
	Unknown
};

UENUM(BlueprintType)
enum class EReserved : uint8
{

	//预留
	MapIDandSts_A,
	MapIDandSts_B,
	MapIDandSts_C,
	MapIDandSts_D,
	MapIDandSts_E,

	MapIDandSts_F,
	MapIDandSts_G,
	MapIDandSts_H,
	MapIDandSts_I,
	MapIDandSts_J,

	Unknown
};




UCLASS()
class ASubscriber : public AActor {
	GENERATED_BODY()
public:





	UFUNCTION(BlueprintCallable, Category = "Subscriber")
		void AddTopic(FString topic);

	UFUNCTION(BlueprintCallable, Category = "Subscriber")
		void AddTopics(TArray<FString> topics);

	UFUNCTION(BlueprintCallable, Category = "Subscriber")
		void RemoveTopic(FString topic);

	UFUNCTION(BlueprintCallable, Category = "Subscriber")
		void RemoveAllTopic();

	UFUNCTION(BlueprintCallable, DisplayName = "AddTopicWithFile", Category = "File")
		bool AddTopicWithFile(FString filePath);

	std::vector<FString> mTopicVec;






	//UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "IntValueUpdateEvent", Keywords = "Subscriber On Int Value Update"), Category = "Subscriber")
	void IntValueUpdateEvent(const FString& name, int32 Value);

	UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "FloatValueUpdateEvent", Keywords = "Subscriber On Float Value Update"), Category = "Subscriber")
		void FloatValueUpdateEvent(const FString& name, float value);

	//UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "FStringValueUpdateEvent", Keywords = "Subscriber On FString Value Update"), Category = "Subscriber")
	void FStringValueUpdate(const FString& name, const FString& value);

	UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "FStringValueUpdateEvent", Keywords = "Subscriber On FString Value Update"), Category = "Subscriber")
		void FStringValueUpdateEvent(const FString& name, const FString& value);

	UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "TarryFloatValueUpdateEvent", Keywords = "Subscriber On TarryFloat Value Update"), Category = "Subscriber")
		void TarryFloatValueUpdateEvent(const FString& name, const TArray<float>& value);

	UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "TarryVectorValueUpdateEvent", Keywords = "Subscriber On TarryVector Value Update"), Category = "Subscriber")
		void TarryVectorValueUpdateEvent(const FString& name, const TArray<FVector>& value);

	UFUNCTION(BlueprintImplementableEvent, meta = (DisplayName = "TMapVectorValueUpdateEvent", Keywords = "Subscriber On TMapVector Value Update"), Category = "Subscriber")
		void TMapVectorValueUpdateEvent(const FString& name, const TMap<int, FVector>& value, const TArray<float>& RotationValue, const TArray<int>& Type);






public:
	// 静态成员变量声明，不在类内初始化
	static const TMap<FString, EVehicleEventType> NameToEnumMap;

	static const TMap<FString, EReserved> reservedToEnumMap;

	static EVehicleEventType StringToVehicleEnum(const FString& Name);

	static EReserved StringToReservedEnum(const FString& Name);

	//ufunction
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleSystemChange(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleGearType(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDayNightMode(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandlePDCVolume(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRCTAWarning(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleWidgetFinish(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandlePDCVolumeSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDebugShow(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleCameraSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRPAWarn(int32 Value);


	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFWheelRotateDir(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRWheelRotateDir(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFWheelRotateSpeed(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRWheelRotateSpeed(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleWhlRotToothCntr(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDrvDoorSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandlePassDoorSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLeReDoorSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRiReDoorSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHoodSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleTrunkSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDrvWinPer(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandlePassWinPer(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLeReWinPer(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRiReWinPer(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleSunroofPer(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDrvMirrSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandlePassMirrSts(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPAParkInSurplusDistance(int32 Value);
	

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLowBeam(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHighBeam(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleTurnLeftLightSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleTurnRightLightSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDoubleFlash(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleBreakLightSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDrvSeatfoldSts(int32 Value);



	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleCarRoll(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleCarPitch(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleCarYaw(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDrivePosition(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleCarSpeed(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleSteeringAngle(int32 Value);



	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvm5sTimer(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmWanringA0(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmWanringA1(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmWanringA2(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmWanringA3(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmWanringB1(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmWanringB2(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmWanringB3(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmWanringB4(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmTrunkWarning(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmCurrViewType(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmViewTwoD(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmViewThreeD(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmSettingPage(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmTransparentCar(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmSteeringlinkage(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmNarrowActive(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmFactoryStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmVersionNum(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmCalib(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmCalibProgress(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmCamFaultFront(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmCamFaultRear(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmCamFaultLeft(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmCamFaultRight(int32 Value);


	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPABtnStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_Quit(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_RadarMute(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_To360(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ViewSwitching(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ParkinReq(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ParkoutReq(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_Setting(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ContPark(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ParkingWithoutFeeling(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_LanguageBroadcast(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_BroadcastMode(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_SRViewChange(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_CenterIn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_LeftIn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_RightIn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_LeftOut(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_RightOut(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_StartParkOut(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_WorkSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_TouchX(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_TouchY(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_FingerIndex(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_TouchEnvTyp(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_Quit_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_RadarMute_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_To360_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ViewSwitching_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ParkinReq_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ParkoutReq_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_Setting_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_SettingBtnSts_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ContPark_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ViewID_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ParkingWithoutFeeling_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_LanguageBroadcast_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_BroadcastMode_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_SRViewChange_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_CenterIn_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_LeftIn_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_RightIn_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_StrtPrkg_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_LeftOut_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_RightOut_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_PerFrontLeftOut_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_PerFrontRightOut_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_PerBackLeftOut_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_PerBackRightOut_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_StartParkOut_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ParkingPercent(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ParkOutInterface_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_WarningID(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_WarningAID(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ParkInSurplusDistance_Stat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDrvrAsscSysSts(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_ViewSwitching_BtnStat(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_FrntAndReSwt(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAPA_FrntAndReSwt_Stat(int32 Value);

	//RPA
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRPA_CarInPark_Stat(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRPA_RemCtrlPrkg_Stat(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandlePrkgAssiSysRemPrkgSts(int32 Value);
		


	//HPA
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPASettingPage(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleSmarkGeneration(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFuncPush(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPAVoiceSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMyRouteSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleParkInRouteNum(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleParkOutRouteNum(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleBuildDistance(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRampNum(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDecZoneNum(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleParkInDisLeft(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleCuriseTimeLeft(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvoidPersons(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvoidCars(int32 Value);
	//UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
	//	void HandleDistanceToEnd(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleTotalCurisingTime(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFirstParkMapTime(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFirstParkMapDistance(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFirstParkMapSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRecentlyused(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFirstParkPhoneDis(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleSecParkMapTime(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleSecParkMapDistance(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleSecParkMapSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleSecParkPhoneDis(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPASts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandlePhoneCtrlSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPASumPage(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPARecoverBtn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPAFinishBtn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleBtnSettingSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleBtnExitSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleBtnParkInSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleBtnParkOutSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPABtnMuteSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPAPDCVolume(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleParkInRouteCover(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleParkOutRouteCover(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRouteSelectionVisibility(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleStartMemoParking(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleBuildRouteCard(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleForwardRouteCard(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleParkInRouteOver(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleParkOutRouteOver(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPACurentViewType(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPAWorkingSts(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPACarport(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPATargetVehicle(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPADrawRoad(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPAWarn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPASmallWarn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPASystemWarn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPASystemWarnType(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDAWarn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPAViewName(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleGlobalViewBtn(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandlePartViewBtn(int32 Value);

	//PDC
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRML_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRMR_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRL_RardarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRR_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFL_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFR_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFML_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFMR_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLFSide_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRFSide_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLRSide_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRRSide_RadarStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmFrontRadarNotise(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmRearRadarNotise(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmFRadarDistance(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmRRadarDistance(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmFRadarNumLocation(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvmRRadarNumLocation(int32 Value);

	//LSDA

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_Forward(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_Backwards(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_Continue(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_Setting(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_FeaturePush(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_WarningA(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_WarningB(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleTBA_WarningA(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleTBA_WarningB(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_ForwardStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_BackwardsStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_ContinueStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_SettingStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_FeaturePushStatus(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_QuitStatus(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_ViewSwitchingStatus(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_ParkingPercent(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleLSDA_SettingPageStatus(int32 Value);


	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDoubleFlashStatus(int32 Value);



	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPA_ViewSwitching_Stat(int32 Value);



	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgMapGeneperctg(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleParkInPercent(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHpaSettingInterface(int32 Value);//ue弹窗
//	HpaSettingInterface
//	FirstParkMapID

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFirstParkMapID(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleFirstParkMapRecentlyused(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleUsgModSts(int32 Value);



	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgMapSyncnReq(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgMapSyncnReqMapList1(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgMapSyncnReqMapList2(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgMapSyncnReqMapList3(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgMapSyncnReqMapList4(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapInfoIsVis(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleCrossNum(int32 Value);


	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleBuildDistanceVisible(int32 Value);


	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleCrossNumVisible(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDecZoneNumVisible(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleDistanceToEndVisible(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvoidPersonsVisible(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleAvoidCarsVisible(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleRampNumVisible(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleParkInDisLeftVisible(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleCruiseTime(int32 Value);

	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgViewDisp(int32 Value);




	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandlecreateHpaRoute(int32 Value);



	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgEstdCrsTi(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgTotCrsTi(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleValtPrkgCrsRemiTi(int32 Value);


	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPA_BeginMoveBtn(int32 Value);


	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleHPA_IsOpenSettingPage(int32 Value);

	//预留
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_A(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_B(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_C(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_D(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_E(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_F(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_G(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_H(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_I(int32 Value);
	UFUNCTION(BlueprintImplementableEvent, Category = "Subscriber")
		void HandleMapIDandSts_J(int32 Value);
};

