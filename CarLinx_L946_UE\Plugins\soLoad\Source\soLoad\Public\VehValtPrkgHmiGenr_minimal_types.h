/**
 * @file VehValtPrkgHmiGenr_minimal_types.h
 * @brief Minimal type definitions for VehValtPrkgHmiGenr
 */

#ifndef VEHVALTPRKGHMIGENR_MINIMAL_TYPES_H_
#define VEHVALTPRKGHMIGENR_MINIMAL_TYPES_H_

#include <cstdint>

namespace gossoa {
namespace ap {
namespace services {
namespace VehValtPrkgHmiGenr {

// #pragma pack(1)  // 确保1字节对齐

enum class VehWave : uint8_t {
  VehWave_NoDisplay = 0,
  VehWave_Display = 1
};

enum class Sts : uint8_t {
  Sts_Default = 0,
  Sts_StraightLineMarker = 1,
  Sts_CurveMarker = 2,
  Sts_SpiralRampMarkingPoint = 3,
  Sts_StraightLineMarkerOutsideTheCar = 4,
  Sts_CurveMarkerOutsideTheCar = 5,
  Sts_SpiralRampMarkingPointOutsideTheCar = 6
};

enum class TrafficSignTyp : uint8_t {
  TrafficSignTyp_NoTrafficSign = 0,
  TrafficSignTyp_TurnLeftSign = 1,
  TrafficSignTyp_TurnRightSign = 2,
  TrafficSignTyp_GoStraightSign = 3,
  TrafficSignTyp_GoStraightTurnLeftSign = 4,
  TrafficSignTyp_GoStraightTurnRightSign = 5,
  TrafficSignTyp_GoStraightTurnLeftSignTurnRightSign = 6,
  TrafficSignTyp_TurnLeftSignTurnRightSign = 7,
  TrafficSignTyp_TurnAroundSign = 8
};

enum class SlotNumber : uint8_t {
  SlotNumber_Default = 0,
  SlotNumber_RecommendedPriority1 = 1,
  SlotNumber_RecommendedPriority2 = 2,
  SlotNumber_RecommendedPriority3 = 3,
  SlotNumber_RecommendedPriority4 = 4,
  SlotNumber_RecommendedPriority5 = 5,
  SlotNumber_RecommendedPriority6 = 6,
  SlotNumber_RecommendedPriority7 = 7,
  SlotNumber_RecommendedPriority8 = 8,
  SlotNumber_RecommendedPriority9 = 9,
  SlotNumber_RecommendedPriority10 = 10,
  SlotNumber_RecommendedPriority11 = 11,
  SlotNumber_RecommendedPriority12 = 12,
  SlotNumber_RecommendedPriority13 = 13,
  SlotNumber_RecommendedPriority14 = 14,
  SlotNumber_RecommendedPriority15 = 15
};

enum class TrafficSignColour : uint8_t {
  TrafficSignColour_NoDisplay = 0,
  TrafficSignColour_White = 1,
  TrafficSignColour_Yellow = 2
};

enum class SlotSts2 : uint8_t {
  SlotSts2_Default = 0,
  SlotSts2_MemoryParkingAvailable = 1,
  SlotSts2_MemoryParkingNotAvailable = 2
};

enum class ObjColor : uint8_t {
  ObjColor_Default = 0,
  ObjColor_Mode1 = 1,
  ObjColor_Mode2 = 2,
  ObjColor_Mode3 = 3,
  ObjColor_Mode4 = 4
};

enum class SlotSts1 : uint8_t {
  SlotSts1_Default = 0,
  SlotSts1_EmptyParkSpace = 1,
  SlotSts1_TargetParkSpace = 2,
  SlotSts1_RecommendeParkSpace = 3,
  SlotSts1_UnusableSpace = 4,
  SlotSts1_MemoryParkingAvailable = 5,
  SlotSts1_MemoryParkingNotAvailable = 6,
  SlotSts1_ObstructionInParkingSpace = 7
};

enum class VehArrowheadDirectiontyp : uint8_t {
  VehArrowheadDirectiontyp_NoDisplay = 0,
  VehArrowheadDirectiontyp_GoAheadDurCruise = 1,
  VehArrowheadDirectiontyp_RegressiveDurCruise = 2,
  VehArrowheadDirectiontyp_GoAheadAndRegressiveDurCruise = 3,
  VehArrowheadDirectiontyp_GoAheadDurParking = 4,
  VehArrowheadDirectiontyp_RegressiveDurParking = 5,
  VehArrowheadDirectiontyp_GoAheadAndRegressiveDurParking = 6,
  VehArrowheadDirectiontyp_DPAFrontLeftTurn = 7,
  VehArrowheadDirectiontyp_DPAFrontRightTurn = 8,
  VehArrowheadDirectiontyp_DPATailLeftTurn = 9,
  VehArrowheadDirectiontyp_DPATailTurnRight = 10
};

struct TtrackPlanning {
  uint16_t ttrackPlanningX__;
  uint16_t ttrackPlanningY__;
  uint8_t sTS__;
};

struct PrkgRoadPoint {
  Sts sts__;
  uint16_t pointX__;
  uint16_t pointY__;
  uint8_t pointZ__;
};

struct VehArrowheadtyp {
  VehArrowheadDirectiontyp vehArrowheadDirection__;
  int16_t vehArrowheadRoll__;
};

struct SlotTyp {
  int8_t slotYawAg__;
  int8_t slotrollAg__;
  int8_t slotpitchAg__;
  int8_t obiqueSlotAg__;
  uint8_t slotTypinfo__;
};

struct SlotSt {
  SlotSts1 slotSts1__;
  SlotNumber slotNumber__;
};

struct HeadingAg {
  uint16_t roll__;
  uint16_t pitch__;
  uint16_t yaw__;
};

struct Posn1 {
  int8_t posnZ__;
  int16_t posnY__;
  int16_t posnX__;
};

struct PrkgObstacle {
  int16_t objSpd__;
  uint8_t objTyp__;
  uint16_t objID__;
  Posn1 objPosn__;
  HeadingAg objHeading__;
  uint8_t objLi__;
  uint8_t objdoor__;
  ObjColor objColor__;
};

struct VehPosntyp {
  int16_t vehPosnZ__; // 2
  int32_t vehPosnX__; // 4
  int32_t vehPosnY__; // 4
};

struct SlotCod {
  uint8_t slotCod1__;
  uint8_t slotCod2__;
  uint8_t slotCod3__;
  uint8_t slotCod4__;
  uint8_t slotCod5__;
};

struct SlotBounding {
  Posn1 slotStart__;
  Posn1 slotEnd__;
  Posn1 slotStartRear__;
  Posn1 slotEndRear__;
};

struct PrkgTrafficsign {
  uint16_t trafficSignID__;
  TrafficSignTyp trafficSignTyp__;
  TrafficSignColour trafficSignColour__;
  Posn1 trafficSignPosn__;
  HeadingAg trafficSignHeadingAg__;
};

struct PrkgMeyPrkgSpe {
  Posn1 slotStart__;
  Posn1 slotEnd__;
  Posn1 slotStartRear__;
  Posn1 slotEndRear__;
  SlotSts2 slotSts2__;
};

struct PrkgSelfvehtyp {
  HeadingAg vehHeadingAg__;
  VehWave vehWave1__;
  VehArrowheadtyp vehArrowhead__; // 2
  VehPosntyp vehPosn__; // 12
};

struct PrkgSlot {
  uint16_t slotID__;
  SlotTyp slotTyp__;
  SlotBounding slotBounding__;
  SlotSt slotSt__;
  SlotCod slotCod__;
};

#pragma pack()  // 恢复默认对齐

}  // namespace VehValtPrkgHmiGenr
}  // namespace services
}  // namespace ap
}  // namespace gossoa

#endif  // VEHVALTPRKGHMIGENR_MINIMAL_TYPES_H_ 