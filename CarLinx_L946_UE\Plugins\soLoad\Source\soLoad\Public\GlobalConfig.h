#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "GlobalConfig.generated.h"

/**
 * 停车系统状态枚举
 */
UENUM(BlueprintType)
enum class EParkingState : uint8
{
    BACKGROUND  = 0 UMETA(DisplayName = "后台"),
    AVM         = 1 UMETA(DisplayName = "AVM"),
    APA         = 2 UMETA(DisplayName = "APA"),
    LSDA        = 3 UMETA(DisplayName = "LSDA"),
    HPA         = 4 UMETA(DisplayName = "HPA")
};

/**
 * 全局配置类
 */
UCLASS(Config=Game)
class SOLOAD_API UGlobalConfig : public UObject
{
    GENERATED_BODY()

public:
    /** 获取单例实例 */
    static UGlobalConfig* GetInstance();
    
    /** 是否启用数据记录 */
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Parking")
    bool bEnableDataRecording{false};

    /** 是否启用性能日志输出 */
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnablePerformanceLog{true};

    /** 当前泊车系统状态 */
    UPROPERTY(Config, EditAnywhere, BlueprintReadWrite, Category = "Parking")
    EParkingState CurrentState;

    /** 重置所有状态为默认值 */
    void Reset();

    /** 获取数据记录状态 */
    UFUNCTION(BlueprintCallable, Category = "Parking")
    bool GetEnableDataRecording() const;

    /** 设置数据记录状态 */
    UFUNCTION(BlueprintCallable, Category = "Parking")
    void SetEnableDataRecording(bool bEnable);

    /** 获取性能日志输出状态 */
    UFUNCTION(BlueprintCallable, Category = "Debug")
    bool GetEnablePerformanceLog() const;

    /** 设置性能日志输出状态 */
    UFUNCTION(BlueprintCallable, Category = "Debug")
    void SetEnablePerformanceLog(bool bEnable);

    /** 获取当前状态 */
    UFUNCTION(BlueprintCallable, Category = "Parking")
    EParkingState GetCurrentState() const;

    /** 设置当前状态 */
    UFUNCTION(BlueprintCallable, Category = "Parking")
    bool SetCurrentState(EParkingState NewState);

    /** 状态判断函数 */
    UFUNCTION(BlueprintCallable, Category = "Parking")
    bool IsInBackground() const { return CurrentState == EParkingState::BACKGROUND; }

    UFUNCTION(BlueprintCallable, Category = "Parking")
    bool IsInAVM() const { return CurrentState == EParkingState::AVM; }

    UFUNCTION(BlueprintCallable, Category = "Parking")
    bool IsInAPA() const { return CurrentState == EParkingState::APA; }

    UFUNCTION(BlueprintCallable, Category = "Parking")
    bool IsInLSDA() const { return CurrentState == EParkingState::LSDA; }

    UFUNCTION(BlueprintCallable, Category = "Parking")
    bool IsInHPA() const { return CurrentState == EParkingState::HPA; }

    bool bHPACreating;

    UFUNCTION(BlueprintCallable, Category = "Parking")
    void EnableHPACreatingFlag() { bHPACreating = true; }

    UFUNCTION(BlueprintCallable, Category = "Parking")
    void DisableHPACreatingFlag() { bHPACreating = false; }

    bool IsInHPACreating() const { return bHPACreating; }

private:
    /** 单例实例 */
    static UGlobalConfig* Instance;
}; 