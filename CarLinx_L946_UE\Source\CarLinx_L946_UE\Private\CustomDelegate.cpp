// Fill out your copyright notice in the Description page of Project Settings.


#include "CustomDelegate.h"

void UCustomDelegate::TriggerIntEvent(FString Message, int32 Value)
{
	
	if (IsInGameThread())
	{
		// 如果已经在游戏线程，直接触发事件
		OnIntEventTriggered.Broadcast(Message, Value);
	}
	else
	{
		// 如果不在游戏线程，使用AsyncTask来确保在游戏线程上执行
		AsyncTask(ENamedThreads::GameThread, [this, Message, Value]()
		{
			OnIntEventTriggered.Broadcast(Message, Value);
		});
	}
}

void UCustomDelegate::TriggerFloatEvent(FString Message, float Value)
{
	if (IsInGameThread())
	{
		// 如果已经在游戏线程，直接触发事件
		OnFloatEventTriggered.Broadcast(Message, Value);
	}
	else
	{
		// 如果不在游戏线程，使用AsyncTask来确保在游戏线程上执行
		AsyncTask(ENamedThreads::GameThread, [this, Message, Value]()
		{
			OnFloatEventTriggered.Broadcast(Message, Value);
		});
	}
}

void UCustomDelegate::TriggerStringEvent(const FString& Message, FString Value)
{
	if (IsInGameThread())
	{
		// 如果已经在游戏线程，直接触发事件
		OnStringEventTriggered.Broadcast(Message, Value);
	}
	else
	{
		// 如果不在游戏线程，使用AsyncTask来确保在游戏线程上执行
		AsyncTask(ENamedThreads::GameThread, [this, Message, Value]()
		{
			OnStringEventTriggered.Broadcast(Message, Value);
		});
	}
}
