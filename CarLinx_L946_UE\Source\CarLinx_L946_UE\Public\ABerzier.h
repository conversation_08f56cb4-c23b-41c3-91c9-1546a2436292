// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "ABerzier.generated.h"

UCLASS()
class CARLINX_L946_UE_API AABerzier : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	AABerzier();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable)
	TArray<FVector2D> BerzierCurve(TArray<FVector2D> src);
};
