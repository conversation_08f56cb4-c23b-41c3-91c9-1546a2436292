# 版本号规则说明

## 版本号格式
`{VERSION_TAG}_{COMMIT_HASH}_{USER_NAME}@{COMPUTER_NAME}_({BUILD_TIME})`

例如：`v1.0.0_(*)1234abcd_wkliu@DESKTOP-PC_(2024-03-21 14:30:00)`

## 各部分说明

### 1. VERSION_TAG
- 从git tag获取的版本标签
- 格式为：`v主版本.次版本.修订号`
- 若无tag则默认为 `v0.0.0`

### 2. COMMIT_HASH
- git commit的哈希值（显示前8位）
- 可能包含以下前缀标记：
  - `(*)` - 有未提交的更改
  - `(#)` - 有未推送的提交
  - `(*#)` - 同时有未提交的更改和未推送的提交
- 无前缀表示工作区清洁且与远程同步

### 3. USER_NAME
- 优先使用git配置的用户名
- 若git未配置用户名则使用系统用户名

### 4. COMPUTER_NAME
- 当前计算机的主机名

### 5. BUILD_TIME
- 构建时间
- 格式为：`YYYY-MM-DD HH:mm:ss`

## 示例
- 清洁状态：`v1.0.0_1234abcd_wkliu@DESKTOP-PC_(2024-03-21 14:30:00)`
- 有未提交更改：`v1.0.0_(*)1234abcd_wkliu@DESKTOP-PC_(2024-03-21 14:30:00)`
- 有未推送提交：`v1.0.0_(#)1234abcd_wkliu@DESKTOP-PC_(2024-03-21 14:30:00)`
- 有未提交更改和未推送提交：`v1.0.0_(*#)1234abcd_wkliu@DESKTOP-PC_(2024-03-21 14:30:00)` 