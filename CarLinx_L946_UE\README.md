# CarLinx L946 UE 数据记录与重放系统

## 功能概述

本系统提供了实时数据记录和回放功能，支持以下特性：
- UDP实时数据接收
- 数据记录
- 数据回放
- 多种泊车模式支持

## 数据源

系统支持两种数据源：
1. UDP实时接收 (`UDP_RECEIVER`)
2. 文件回放 (`FILE_PLAYBACK`)

## 使用方法

### 1. UDP数据接收

```cpp
// 启动UDP接收
StartUDPClient(IP, Port);  // 例如：StartUDPClient("127.0.0.1", 8888)
```

### 2. 数据记录

数据记录功能在以下条件下自动启用：
- 全局配置允许数据记录 (`bEnableDataRecording = true`)
- 当前处于以下泊车模式：
  - APA (自动泊车)
  - LSDA (远程泊车)
  - HPA (记忆泊车)

```cpp
// 手动开始记录
StartRecording();

// 手动停止记录
StopRecording();
```

### 3. 数据回放

支持以下回放控制功能：
- 开始回放
- 暂停/恢复
- 调整播放速度
- 重新开始
- 逐帧播放

```cpp
// 开始回放指定文件
StartPlayback(FilePath);

// 暂停回放
PausePlayback();

// 恢复回放
ResumePlayback();

// 调整播放速度
SetPlaybackSpeed(Speed);  // 例如：SetPlaybackSpeed(2.0f) 为2倍速

// 重新开始回放
RestartPlayback();

// 播放下一帧
PlayNextFrame();
```

### 4. 切换数据源

```cpp
// 切换到UDP接收
SwitchDataSource(EDataSource::UDP_RECEIVER);

// 切换到文件回放
SwitchDataSource(EDataSource::FILE_PLAYBACK, FilePath);
```

### 5. 泊车模式切换

```cpp
// 切换泊车模式
SwitchParkingMode(EParkingState::APA);  // 自动泊车
SwitchParkingMode(EParkingState::LSDA); // 远程泊车
SwitchParkingMode(EParkingState::HPA);  // 记忆泊车
SwitchParkingMode(EParkingState::AVM);  // 全景影像
```

## 数据处理流程

1. UDP数据接收
   - 接收实时数据
   - 数据预处理
   - 转发到数据处理管理器

2. 数据记录
   - 实时数据序列化
   - 写入文件存储

3. 数据回放
   - 文件数据读取
   - 数据反序列化
   - 模拟实时数据流

## 注意事项

1. 数据记录：
   - 仅在特定泊车模式下自动记录
   - 需要确保足够的存储空间
   - 记录文件会自动保存

2. 数据回放：
   - 回放前确保文件完整性
   - 可以随时暂停/恢复
   - 支持调整播放速度

3. 模式切换：
   - 切换模式时会自动处理数据源
   - 切换时会清理旧数据
   - 确保模式切换前保存必要数据

## 错误处理

系统会自动处理以下情况：
- UDP连接断开时自动重连
- 文件读写错误提示
- 数据格式错误处理
- 模式切换异常处理 