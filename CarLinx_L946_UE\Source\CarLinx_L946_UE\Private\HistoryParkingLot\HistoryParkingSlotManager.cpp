﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "HistoryParkingLot/HistoryParkingSlotManager.h"

#include "ToolFunctionLibrary.h"
#include "HistoryParkingLot/HistoryParkingLotBase.h"


void AHistoryParkingSlotManager::AddHistorySpot(int32 InID, AHistoryParkingLotBase* Spot, EParkingOrientation Orientation)
{
	if (!Spot) return;
	
	switch (Orientation)
	{
	case EParkingOrientation::Horizontal:
		HorizontalSpots.Add(InID, Spot);
		break;
	case EParkingOrientation::Vertical:
		VerticalSpots.Add(InID, Spot);
		break;
	}
}

void AHistoryParkingSlotManager::RemoveHistorySpot(int32 InID, EParkingOrientation Orientation)
{
	if (HorizontalSpots.Num() < 0 && VerticalSpots.Num() < 0)
	{
		return;
	}

	switch (Orientation)
	{
	case EParkingOrientation::Horizontal:
		HorizontalSpots.Remove(InID);
		break;
	case EParkingOrientation::Vertical:
		VerticalSpots.Remove(InID);
		break;
	}
}

void AHistoryParkingSlotManager::BeginPlay()
{
	Super::BeginPlay();

	CurrentWorld = GetWorld();
	if (CurrentWorld == nullptr)
	{
		UE_LOG(LogTemp,Warning,TEXT("233"))
	}
}

void AHistoryParkingSlotManager::ResetAllHistoryParkingSlot()
{
	if (HorizontalSpots.Num() < 0 && VerticalSpots.Num() < 0)
	{
		return;
	}
	for (const auto& Spot : HorizontalSpots)
	{
		Spot.Value->Destroy();
	}
	for (const auto& Spot : VerticalSpots)
	{
		Spot.Value->Destroy();
	}
	HorizontalSpots.Empty();
	VerticalSpots.Empty();
	AllParkingSlotData.Empty();
}

void AHistoryParkingSlotManager::CameraBeginEvent()
{
	//确保管理的历史车位为空
	ResetAllHistoryParkingSlot();

	TMap<int32, FParkingSlotData> HistoryParkingSlots;
	UParkingDataProcessor2UE::GetSlotHistory(HistoryParkingSlots);
	if (HistoryParkingSlots.Num() < 0)
	{
		return;
	}
	for (const auto& data : HistoryParkingSlots)
	{
		AllParkingSlotData.Add(data.Value);
	}

	//生成车位
	FVector LocationTemp = FVector::ZeroVector;
	FVector ForwardVector = FVector::ZeroVector;
	for (const auto& Item : AllParkingSlotData)
	{
		GetHpaTouchLocation(LocationTemp, ForwardVector);

		//车位类型
		ECarSlot CurSlotType = UToolFunctionLibrary::SlotType(LocationTemp,ForwardVector,Item.CenterPosition);

		//车位宽度
		float Outlength = 0.f;
		float CurSlotWidth = 0.f;
		UToolFunctionLibrary::CalculateParkingSpaceDimensions(Item.SlotStart,Item.SlotEnd,Item.SlotStartRear,Item.SlotEndRear,Outlength,CurSlotWidth);
		DynamicControlParkingSlot(Item.SlotID,Item.SlotTypInfo,Item.SlotNumber,Item.SlotSts1,Item.CenterPosition,Item.Angle,CurSlotType,CurSlotWidth);
	}
}

void AHistoryParkingSlotManager::DynamicControlParkingSlot(int32 ID, int32 MeshType, int32 Number, int32 Status,
	const FVector& Location, float Rotation, ECarSlot CarSlot, float Width)
{

	switch (MeshType)
	{
		case 1:
		case 4:
		case 7:
		case 10:
		{
				FRotator SpawnRotator(0.f, Rotation,0.f);
				AHistoryParkingLotBase* NewHorizontalSlot = CurrentWorld->SpawnActor<AHistoryParkingLotBase>(HorizontalParkingSlotClass, Location, SpawnRotator);
				if (NewHorizontalSlot != nullptr)
				{
					NewHorizontalSlot->UpdateHistoryParkingLotInfo(Status, Number, Location, Rotation, CarSlot, Width);
					AddHistorySpot(ID, NewHorizontalSlot, EParkingOrientation::Horizontal);
				}
				break;
		}
		case 2:
		case 3:
		case 5:
		case 6:
		case 8:
		case 9:
		case 11:
		case 12:
			{
				FRotator SpawnRotator(0.f, Rotation, 0.f);
				AHistoryParkingLotBase* NewVerticalLot = CurrentWorld->SpawnActor<AHistoryParkingLotBase>(VerticalParkingSlotClass, Location, SpawnRotator);
				if (NewVerticalLot != nullptr)
				{
					NewVerticalLot->UpdateHistoryParkingLotInfo(Status, Number, Location, Rotation, CarSlot, Width);
					AddHistorySpot(ID, NewVerticalLot, EParkingOrientation::Vertical);
				}
				break;
			}
		default:
			break;
	}
}
