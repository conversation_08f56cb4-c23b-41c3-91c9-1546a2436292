// Fill out your copyright notice in the Description page of Project Settings.


#include "SplitView.h"


// Sets default values
ASplitView::ASplitView()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void ASplitView::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void ASplitView::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

void ASplitView::SplitScreen(TArray<FSplitData1> DataArray)
{
	if (GEngine && GEngine->GameViewport)
	{
		if (DataArray.Num() == 2)
		{
			for (int i = 0; i < 2; i++)
			{
				GEngine->GameViewport->SplitscreenInfo[ESplitScreenType::TwoPlayer_Vertical].PlayerData[i].OriginX = DataArray[i].OriginX;
				GEngine->GameViewport->SplitscreenInfo[ESplitScreenType::TwoPlayer_Vertical].PlayerData[i].OriginY = DataArray[i].OriginY;
				GEngine->GameViewport->SplitscreenInfo[ESplitScreenType::TwoPlayer_Vertical].PlayerData[i].SizeX = DataArray[i].SizeX;
				GEngine->GameViewport->SplitscreenInfo[ESplitScreenType::TwoPlayer_Vertical].PlayerData[i].SizeY = DataArray[i].SizeY;
			}
		}
		//log
		//GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Red, TEXT("SplitScreen"));
	}
}

