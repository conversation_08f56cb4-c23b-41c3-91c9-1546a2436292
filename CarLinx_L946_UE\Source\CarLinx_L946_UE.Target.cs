// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;
using System.Collections.Generic;
using System.IO;

public class CarLinx_L946_UETarget : TargetRules
{
	public CarLinx_L946_UETarget( TargetInfo Target) : base(Target)
	{
		Type = TargetType.Game;
		DefaultBuildSettings = BuildSettingsVersion.V2;
		ExtraModuleNames.AddRange( new string[] { "CarLinx_L946_UE" } );

		// 使用 $(ProjectDir) 变量确保正确的路径
		PreBuildSteps.Add("echo *** Running Version Update Script ***");
		PreBuildSteps.Add("cmd.exe /c \"$(ProjectDir)\\Script\\updateVersion.bat\"");
		PreBuildSteps.Add("echo *** Version Update Complete ***");
	}
}
