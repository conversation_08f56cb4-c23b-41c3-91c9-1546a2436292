// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "UObject/UObjectGlobals.h"
#include "CreateMeshActor.generated.h"

UCLASS()
class TRACK_API ACreateMeshActor : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	ACreateMeshActor();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

//圆形拐角函数
public:

	//已知点集，计算两条平行点集
	UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	TArray<FVector> SetTrackPoint(const FVector& Point0,const FVector& Point1);

private:
    FVector Getintersect(float K1, float B1, float K2, float B2, float Z);

public:
    // 轨迹宽度
	UPROPERTY(BlueprintReadWrite)
	float RoundSize;

};
