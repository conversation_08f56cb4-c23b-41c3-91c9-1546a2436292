// Copyright Epic Games, Inc. All Rights Reserved.

#include "soLoad.h"

#define LOCTEXT_NAMESPACE "FsoLoadModule"

void FsoLoadModule::StartupModule()
{
	// This code will execute after your module is loaded into memory
	UE_LOG(LogTemp, Log, TEXT("[carlinx][Debug][soLoad]: FsoLoadModule Started"));
}

void FsoLoadModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module
	UE_LOG(LogTemp, Log, TEXT("[carlinx][Debug][soLoad]: FsoLoadModule Shutdown"));
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FsoLoadModule, soLoad)