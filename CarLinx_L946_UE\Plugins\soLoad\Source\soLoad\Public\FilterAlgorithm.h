#pragma once

#include "VehValtPrkgHmiGenr_minimal_types.h"
#include <vector>
#include <deque>
#include <memory>
#include <map>
#include <chrono>

/**
 * @class FilterAlgorithm
 * @brief 提供三种滤波算法用于处理障碍物数据
 * 
 * 该类提供移动平均滤波、低通滤波和高斯滤波三种算法，用于平滑处理障碍物的位置、朝向和速度数据。
 * 每种滤波算法都有其特定的应用场景和特点。
 * 
 * 使用示例：
 * @code
 *     // 1. 配置滤波参数
 *     FilterAlgorithm::SetMovingAverageWindowSize(7);
 *     FilterAlgorithm::SetLowPassAlpha(0.3f);
 *     FilterAlgorithm::SetGaussianParameters(1.5f, 7);
 * 
 *     // 2. 使用统一接口进行滤波
 *     VehValtPrkgHmiGenr::PrkgObstacle obstacle;  // 假设这是输入数据
 *     // 使用默认滤波类型
 *     auto filtered1 = FilterAlgorithm::Filter(obstacle);
 *     // 或指定滤波类型
 *     auto filtered2 = FilterAlgorithm::Filter(obstacle, FilterAlgorithm::FilterType::MovingAverage);
 * 
 *     // 3. 定期清理过期数据（比如每30秒调用一次）
 *     FilterAlgorithm::ClearStaleData(30.0f);
 * 
 *     // 4. 需要重置所有数据时
 *     FilterAlgorithm::ClearAllData();
 * @endcode
 */
class FilterAlgorithm {
public:
    /**
     * @brief 滤波算法类型枚举
     * @details 定义了支持的滤波算法类型
     */
    enum class FilterType {
        MovingAverage,  // 移动平均滤波：适用于稳定性要求高的场景
        LowPass,        // 低通滤波：适用于需要快速响应的场景
        Gaussian        // 高斯滤波：适用于噪声呈正态分布的场景
    };

    /**
     * @brief 统一的滤波接口
     * @details 根据指定的滤波类型选择对应的滤波算法
     * @param input 输入的障碍物数据
     * @param type 滤波类型，默认使用MovingAverage
     * @return 滤波后的障碍物数据
     */
    static gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle Filter(
        const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle& input,
        FilterType type = FilterType::MovingAverage
    );

    /**
     * @brief 设置移动平均滤波的窗口大小
     * @param windowSize 滑动窗口大小，必须大于1
     */
    static void SetMovingAverageWindowSize(int windowSize);

    /**
     * @brief 设置低通滤波的平滑系数
     * @param alpha 平滑系数(0-1)，越大表示越信任新数据
     */
    static void SetLowPassAlpha(float alpha);

    /**
     * @brief 设置高斯滤波的参数
     * @param sigma 高斯函数的标准差，控制平滑程度
     * @param kernelSize 高斯核大小，必须为奇数
     */
    static void SetGaussianParameters(float sigma, int kernelSize);

    /**
     * @brief 清理长时间未更新的数据缓存
     * @param timeThresholdSeconds 时间阈值(秒)，超过该时间未更新的数据将被清理
     */
    static void ClearStaleData(float timeThresholdSeconds);

    /**
     * @brief 清除所有缓存数据
     * @details 用于重置滤波器状态，比如在障碍物跟踪重启时调用
     */
    static void ClearAllData();

private:
    // 滤波参数
    static int movingAverageWindowSize;
    static float lowPassAlpha;
    static float gaussianSigma;
    static int gaussianKernelSize;

    struct FilterData {
        std::deque<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle> buffer;
        gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle lastValue;
        std::chrono::system_clock::time_point lastUpdateTime;
        // 存储滑动窗口的和
        float sumX = 0;
        float sumY = 0;
        float sumZ = 0;
    };

    // 按objID__分类的数据缓存
    static std::map<uint16_t, FilterData> filterDataMap;
    static std::vector<float> gaussianKernel;

    /**
     * @brief 移动平均滤波
     * @details 对最近N帧数据进行算术平均，可有效抑制随机噪声
     * 优点：
     * - 计算简单，易于实现
     * - 对周期性干扰有良好的抑制作用
     * - 滤波效果稳定
     * 缺点：
     * - 相位滞后，时间延迟较大
     * - 不能很好地跟踪急剧变化的信号
     * 适用场景：
     * - 障碍物静止或匀速运动时
     * - 需要较强噪声抑制能力的场景
     * - 对实时性要求不高的场景
     * 
     * @param input 输入的障碍物数据
     * @param windowSize 滑动窗口大小，默认为5
     * @return 滤波后的障碍物数据
     */
    static gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle MovingAverageFilter(
        const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle& input,
        int windowSize = 5
    );

    /**
     * @brief 低通滤波
     * @details 一阶低通滤波器，可以平滑数据并保持较快响应速度
     * 优点：
     * - 计算量小，实时性好
     * - 对突变信号响应较快
     * - 可通过alpha参数灵活调节滤波强度
     * 缺点：
     * - 滤波效果不如移动平均平滑
     * - 对持续性干扰抑制效果一般
     * 适用场景：
     * - 障碍物运动状态频繁变化时
     * - 需要快速响应的场景
     * - 噪声幅度较小的场景
     * 
     * @param input 输入的障碍物数据
     * @param alpha 平滑系数(0-1)，越大表示越信任新数据，默认0.2
     * @return 滤波后的障碍物数据
     */
    static gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle LowPassFilter(
        const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle& input,
        float alpha = 0.2
    );

    /**
     * @brief 高斯滤波
     * @details 使用高斯分布作为权重进行加权平均
     * 优点：
     * - 滤波效果最平滑
     * - 对高斯噪声有最优抑制效果
     * - 边缘保持性能好
     * 缺点：
     * - 计算量较大
     * - 参数选择较为复杂
     * 适用场景：
     * - 噪声呈正态分布时
     * - 需要最好滤波效果时
     * - 对计算资源要求不敏感时
     * 
     * @param input 输入的障碍物数据
     * @param sigma 高斯函数的标准差，控制平滑程度，默认1.0
     * @param kernelSize 高斯核大小，必须为奇数，默认5
     * @return 滤波后的障碍物数据
     */
    static gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle GaussianFilter(
        const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle& input,
        float sigma = 1.0,
        int kernelSize = 5
    );

    // 辅助函数
    static std::vector<float> CreateGaussianKernel(float sigma, int size);
    static void UpdateFilterData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle& input, uint16_t objId);
}; 