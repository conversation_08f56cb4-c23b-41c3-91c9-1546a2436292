#pragma once

#include "CoreMinimal.h"
#include "HAL/Runnable.h"
#include "HAL/ThreadSafeBool.h"
#include "Containers/Queue.h"
#include "DataTypes.h"
#include <vector>

class SOLOAD_API FFileReadThread : public FRunnable
{
public:
    FFileReadThread(const FString& FilePath);
    virtual ~FFileReadThread();

    // FRunnable接口
    virtual bool Init() override;
    virtual uint32 Run() override;
    virtual void Stop() override;
    virtual void Exit() override;

    // 数据读取接口
    bool ReadFrame(std::vector<uint8_t>& OutData, int64& OutTimestamp);
    bool ReadHeader(FParkingDataFileHeader& OutHeader);
    bool IsFileOpened() const { return bFileOpened; }
    void SeekToBegin();
    bool ReadLastFrame(std::vector<uint8_t>& OutData, int64& OutTimestamp);

private:
    bool OpenFile(const FString& FilePath);
    void CloseFile();

    FThreadSafeBool bRunning{true};
    FString FilePath;
    FILE* FileHandle{nullptr};
    FRunnableThread* Thread{nullptr};

    long currentPos = 0;
    
    FCriticalSection CriticalSection;
    bool bFileOpened{false};

    // 性能监控
    FThreadSafeCounter TotalFramesRead;
    double LastReadTime;
    double ReadRate;
}; 