#include "ParkingDataDeSerializationManager.h"

// UE4 Core headers
#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"
#include "TimerManager.h"
#include "HAL/PlatformTime.h"
#include "Misc/DateTime.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/GameInstance.h"
#include "Async/TaskGraphInterfaces.h"
#include "Containers/Ticker.h"

// Project headers
#include "Logger.h"
#include "GlobalConfig.h"
#include "FileReadThread.h"
#include "GameDelegates.h"
#include "ParkingDataManager.h"
#include "ParkingDataProcessor2UE.h"
#include "ParkingDataProcessManager.h"

UParkingDataDeSerializationManager* UParkingDataDeSerializationManager::Instance = nullptr;

static TAutoConsoleVariable<float> ConsolePlaybackSpeed(
	TEXT("Playback.Speed"),1.0f,
	TEXT("回放速度"));

UParkingDataDeSerializationManager::UParkingDataDeSerializationManager()
	:bIsPaused(false)
	, PlaybackSpeed(1.0f)
{
#if WITH_EDITORONLY_DATA
	FWorldDelegates::OnWorldCleanup.AddLambda([this](UWorld* World, bool bSessionEnded, bool bCleanupResources)
	{
		if (World->IsPlayInEditor())
		{
			StopPlayback();
		}
	});
#endif
}

UParkingDataDeSerializationManager::~UParkingDataDeSerializationManager()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor Begin"), this);
	StopPlayback();
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor End"), this);
}

UParkingDataDeSerializationManager* UParkingDataDeSerializationManager::GetInstance()
{
	if (!Instance)
	{
		// 通过GameInstance创建对象，确保有正确的World上下文
		if (UWorld* World = GEngine ? GEngine->GetWorld() : nullptr)
		{
			if (UGameInstance* GameInstance = World->GetGameInstance())
			{
				Instance = NewObject<UParkingDataDeSerializationManager>(GameInstance);
				Instance->AddToRoot();
				CARLINX_LOG(LogCarLinx, Log,
				            TEXT("Created ParkingDataDeSerializationManager instance with GameInstance context"));
			}
		}

		// 如果通过GameInstance创建失败，则使用默认方式
		if (!Instance)
		{
			Instance = NewObject<UParkingDataDeSerializationManager>();
			Instance->AddToRoot();
			CARLINX_LOG(LogCarLinx, Warning,
			            TEXT("Created ParkingDataDeSerializationManager instance without context"));
		}
	}
	return Instance;
}

bool UParkingDataDeSerializationManager::StartPlayback(bool autoPlay, const FString& FilePath)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StartPlayback Begin"), this);

	if (FileReadThread)
	{
		StopPlayback();
	}

	FString PlaybackFilePath = FilePath;

	// 如果没有指定文件路径，查找当前状态最新的记录文件
	if (PlaybackFilePath.IsEmpty())
	{
		// 获取基础路径和当前状态
		FString BaseFilePath;
#if PLATFORM_ANDROID
        // 检查并选择可用的存储路径
        IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
        TArray<FString> PotentialPaths = {
            TEXT("/storage/emulated/0/Android/data/com.geely.carlinx/files/UE_Log/SR/"),
            TEXT("/storage/emulated/0/UE_Log/SR/"),
            TEXT("/sdcard/Android/data/com.geely.carlinx/files/UE_Log/SR/"),
            TEXT("/sdcard/UE_Log/SR/"),
            TEXT("/data/user/10/cn.carlinx.parking/UE_Log/SR/")
        };

        for (const FString& Path : PotentialPaths)
        {
            if (PlatformFile.DirectoryExists(*Path))
            {
                BaseFilePath = Path;
                break;
            }
        }

        if (BaseFilePath.IsEmpty())
        {
            BaseFilePath = TEXT("/storage/emulated/0/Android/data/com.geely.carlinx/files/UE_Log/SR/");
        }
#else
		BaseFilePath = TEXT("D:/UE_Log/SR/");
#endif

		// 获取当前状态名称
		FString StateName;
		switch (UGlobalConfig::GetInstance()->GetCurrentState())
		{
		case EParkingState::APA:
			StateName = TEXT("APA");
			break;
		case EParkingState::HPA:
			StateName = TEXT("HPA");
			break;
		case EParkingState::LSDA:
			StateName = TEXT("LSDA");
			break;
		case EParkingState::AVM:
			StateName = TEXT("AVM");
			break;
		default:
			StateName = TEXT("BACKGROUND");
			break;
		}

		// 查找所有日期文件夹
		TArray<FString> DateFolders;
		IFileManager::Get().FindFiles(DateFolders, *(BaseFilePath / TEXT("*")), false, true);

		CARLINX_LOG(LogCarLinx, Log, TEXT("Found %d date folders in %s"), DateFolders.Num(), *BaseFilePath);

		// 过滤掉非日期格式的文件夹
		DateFolders.RemoveAll([](const FString& Folder)
		{
			// 检查是否是8位数字格式（YYYYMMDD）
			if (Folder.Len() != 8) return true;
			for (TCHAR c : Folder)
			{
				if (!FChar::IsDigit(c)) return true;
			}
			return false;
		});

		// 按日期排序（文件夹名格式为YYYYMMDD）
		DateFolders.Sort([](const FString& A, const FString& B)
		{
			return A > B; // 降序排序，最新日期在前
		});

		// 从最新的日期文件夹开始查找匹配的文件
		for (const FString& DateFolder : DateFolders)
		{
			FString DateFolderPath = FPaths::Combine(BaseFilePath, DateFolder);
			TArray<FString> FoundFiles;
			IFileManager::Get().FindFiles(FoundFiles,
			                              *FPaths::Combine(DateFolderPath,
			                                               FString::Printf(TEXT("SR_DATA_*_%s.dat"), *StateName)),
			                              true, false);

			if (FoundFiles.Num() > 0)
			{
				// 按文件名排序（因为包含时间戳，所以最后一个就是最新的）
				FoundFiles.Sort();

				// 从最新的文件开始检查，直到找到非空文件
				for (int32 i = FoundFiles.Num() - 1; i >= 0; --i)
				{
					FString TempPath = FPaths::Combine(DateFolderPath, FoundFiles[i]);
					int64 FileSize = IFileManager::Get().FileSize(*TempPath);

					if (FileSize > 0)
					{
						PlaybackFilePath = TempPath;
						CARLINX_LOG(LogCarLinx, Log, TEXT("Found latest valid recording file: %s, size: %lld bytes"),
						            *PlaybackFilePath, FileSize);
						break;
					}
					else
					{
						CARLINX_LOG(LogCarLinx, Warning, TEXT("Skipping empty file: %s"), *TempPath);
					}
				}

				if (!PlaybackFilePath.IsEmpty())
				{
					break; // 找到有效文件后退出文件夹循环
				}
			}
		}

		if (PlaybackFilePath.IsEmpty())
		{
			CARLINX_LOG(LogCarLinx, Error, TEXT("No recording files found for state: %s"), *StateName);
			return false;
		}

		// 检查文件是否存在
		if (!IFileManager::Get().FileExists(*PlaybackFilePath))
		{
			CARLINX_LOG(LogCarLinx, Error, TEXT("File does not exist: %s"), *PlaybackFilePath);
			return false;
		}

		// 检查文件大小
		int64 FileSize = IFileManager::Get().FileSize(*PlaybackFilePath);
		if (FileSize <= 0)
		{
			CARLINX_LOG(LogCarLinx, Error, TEXT("File is empty or invalid: %s (size: %lld)"), *PlaybackFilePath,
			            FileSize);
			return false;
		}

		// 检查文件权限
		if (!IFileManager::Get().IsReadOnly(*PlaybackFilePath))
		{
			CARLINX_LOG(LogCarLinx, Warning, TEXT("File is not read-only: %s"), *PlaybackFilePath);
		}

		CARLINX_LOG(LogCarLinx, Log, TEXT("Attempting to open file: %s (size: %lld bytes)"), *PlaybackFilePath,
		            FileSize);
	}
	StartPlaybackLoop(PlaybackFilePath);
	return true;
}

void UParkingDataDeSerializationManager::ForwardATick(float InDeltaTime)
{
	//这里简单处理了
	float Speed = ConsolePlaybackSpeed->GetFloat();
	InDeltaTime *= 1000 * Speed;
	while (InDeltaTime > (FileCurrentTime - LastFileTime))
	{
		std::vector<uint8_t> CurOutData;
		if (bIsPaused)
		{
			FileReadThread->ReadLastFrame(CurOutData,FileCurrentTime);
			ProcessPlaybackData(CurOutData);
			break;
		}
		if (FileReadThread->ReadFrame(CurOutData, FileCurrentTime))
		{
			// 处理帧数据
			ProcessPlaybackData(CurOutData);
			CARLINX_LOG(LogCarLinx, Log, TEXT("[LoopCounter: %d] [CurTime : %d] [LastFileTime : %d]"),
			            LoopCounter, FileCurrentTime, LastFileTime);
		}
		else
		{
			//读取失败，就从头开始
			FileReadThread->SeekToBegin();
			LastFileTime = FileStartTime;
			FileCurrentTime = FileStartTime;
			LoopCounter++;
			CARLINX_LOG(LogCarLinx, Log, TEXT("从头开始了 [LoopCounter: %d] [CurTime : %d] [LastFileTime : %d]"),
			            LoopCounter, FileCurrentTime, LastFileTime);
			break;
		}
	}
	//更新上次tick的时间
	LastFileTime = FileCurrentTime;
}

void UParkingDataDeSerializationManager::StartPlaybackLoop(const FString& FilePath)
{
	if (!IFileManager::Get().FileExists(*FilePath))
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("File does not exist: %s"), *FilePath);
		return;
	}

	FileReadThread = MakeUnique<FFileReadThread>(FilePath);
	if (!FileReadThread || !FileReadThread->IsFileOpened())
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to open file for playback: %s"), *FilePath);
		FileReadThread.Reset();
		return;
	}

	FileReadThread->SeekToBegin();
	//读取第一帧
	std::vector<uint8_t> OutData;
	FileReadThread->ReadFrame(OutData, FileStartTime);
	LastFileTime = FileStartTime;
	FileCurrentTime = FileStartTime;

	Tickerhandle = FTicker::GetCoreTicker().AddTicker(FTickerDelegate::CreateLambda([this](float InDeltaTime)
	{
		if (FileReadThread)
		{
			ForwardATick(InDeltaTime);
			return true;
		}
		return false;
	}), 0);
}

void UParkingDataDeSerializationManager::StopPlayback()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StopPlayback Begin"), this);
	FTicker::GetCoreTicker().RemoveTicker(Tickerhandle);

	if (FileReadThread)
	{
		// 等待文件读取线程完成
		FileReadThread->Stop();
		FileReadThread.Reset();
	}
	bIsPaused = false;
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StopPlayback End"), this);
}

void UParkingDataDeSerializationManager::PausePlayback()
{
	bIsPaused = true;
	bIsAutoPlaying = false;
}

void UParkingDataDeSerializationManager::ResumePlayback()
{
	if (FileReadThread && bIsPaused)
	{
		bIsPaused = false;
	}
}


void UParkingDataDeSerializationManager::PlayNextFrame()
{
	
}


void UParkingDataDeSerializationManager::ProcessPlaybackData(const std::vector<uint8_t>& Data)
{
	// 使用数据处理管理器处理数据
	UParkingDataProcessManager::GetInstance()->ProcessReceivedData(Data);
}
