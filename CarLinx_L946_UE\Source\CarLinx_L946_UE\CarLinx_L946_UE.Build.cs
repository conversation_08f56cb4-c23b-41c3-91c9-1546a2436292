// Copyright Epic Games, Inc. All Rights Reserved.
using System.IO;
using UnrealBuildTool;

using Tools.DotNETCommon;

public class CarLinx_L946_UE : ModuleRules
{
	public CarLinx_L946_UE(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
	
		PublicDependencyModuleNames.AddRange(new string[] { "Core", "CoreUObject", "Engine", "InputCore", "UMG", "Slate", "SlateCore", "Json", "JsonUtilities" });

		PrivateDependencyModuleNames.AddRange(new string[] { "soLoad", "Track" });

		// Uncomment if you are using Slate UI
		// PrivateDependencyModuleNames.AddRange(new string[] { "Slate", "SlateCore" });
		
		// Uncomment if you are using online features
		// PrivateDependencyModuleNames.Add("OnlineSubsystem");

		// To include OnlineSubsystemSteam, add it to the plugins section in your uproject file with the Enabled attribute set to true
		
		
		if (Target.Platform == UnrealTargetPlatform.Win64)
		{
			Log.TraceInformation("ModuleDirectory!!!!!!!!!!:" +  ModuleDirectory);
			/*string ThirdPartyPath = Path.Combine(ModuleDirectory, "../../Plugins/soLoad/ThirdParty/");

			string DllPath = Path.Combine(ThirdPartyPath, "Lib/Win64/ThirdLib.dll");

			// 复制 DLL 到 Binaries/Win64 目录
			RuntimeDependencies.Add(Path.Combine(ThirdPartyPath, "Lib/Win64/ThirdLib.dll"));

			if (!File.Exists(DllPath))
			{
				Log.TraceInformation("DLL not found: " + DllPath);
			}
			else
			{
				File.Delete(Path.Combine(ModuleDirectory, "../../Binaries/Win64/ThirdLib.dll"));
				File.Copy(DllPath, Path.Combine(ModuleDirectory, "../../Binaries/Win64/ThirdLib.dll"));
				Log.TraceInformation("DLL found: " + DllPath);
			}

			// 添加延迟加载 DLL 的路径
			PublicDelayLoadDLLs.Add("ThirdLib.dll");*/
		}
		else if (Target.Platform == UnrealTargetPlatform.Android)
		{
			Log.TraceInformation("this is Android platform !!!!!");
			// 设置第三方库路径
			//         string ThirdPartyPath = Path.Combine(ModuleDirectory, "../../Plugins/BlueCallMethodPlugin/ThirdParty/LibTest/");
			//Log.TraceInformation("this is Android platform !!!!!" + ThirdPartyPath);

			//         // 如果需要动态链接库，确保库文件在打包时被复制到 APK 中
			//         RuntimeDependencies.Add(Path.Combine(ThirdPartyPath, "Lib/Android/libThirdLib.so"));

			//         // 如果是动态库（.so），可以使用如下方式
			//         PublicDelayLoadDLLs.Add("libThirdLib.so");

			// 设置第三方库的路径
			//string ThirdPartyPath = Path.Combine(ModuleDirectory, "../../Plugins/soLoad/ThirdParty/");

			// 添加导入库文件
			//PublicAdditionalLibraries.Add(Path.Combine(ThirdPartyPath, "Lib/Android/libThirdLib.so"));
			/*PublicAdditionalLibraries.Add(Path.Combine(ThirdPartyPath,"Lib/Android/libVehValtPrkgHmiGenr_cligenerate.so"));
			PublicAdditionalLibraries.Add(Path.Combine(ThirdPartyPath, "Lib/Android/libVehValtPrkgHmiGenr_cli.so"));*/

			// 添加动态库文件，并确保打包到项目中
			//RuntimeDependencies.Add(Path.Combine(ThirdPartyPath, "Lib/Android/libThirdLib.so"));
			/*RuntimeDependencies.Add(Path.Combine(ThirdPartyPath, "Lib/Android/libVehValtPrkgHmiGenr_cligenerate.so"));
			RuntimeDependencies.Add(Path.Combine(ThirdPartyPath, "Lib/Android/libVehValtPrkgHmiGenr_cli.so"));*/
		}
	}
}
