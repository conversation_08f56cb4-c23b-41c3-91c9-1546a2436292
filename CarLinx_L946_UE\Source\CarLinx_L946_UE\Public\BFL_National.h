// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "BFL_National.generated.h"

/**
 * 
 */

USTRUCT(BlueprintType)
struct FSplitData
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(EditAnywhere)
	float SizeX;
	UPROPERTY(EditAnywhere)
	float SizeY;
	UPROPERTY(EditAnywhere)
	float OriginX;
	UPROPERTY(EditAnywhere)
	float OriginY;

	FSplitData()
	{
		SizeX = 0;
		SizeY = 0;
		OriginX = 0;
		OriginY = 0;
	}

	FSplitData(float x, float y, float sizex, float sizey) :
		OriginX(x),
		OriginY(y),
		SizeX(sizex),
		SizeY(sizey)
	{

	}
};

UCLASS()
class CARLINX_L946_UE_API UBFL_National : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
	UFUNCTION(BlueprintCallable)
	static void SplitScreen(TArray<FSplitData> DataArray);

	UFUNCTION(BlueprintCallable, meta = (DisplayName = "LoadTextureFromPath", keywords = "Load"), Category = "LoadFile")    
	static UTexture2D* LoadTextureFromPath(const FString& Path);

	/*
		Convert the local image to Texture2D.

		@ImagePath: Local picture path.
		@InTexture: Converted Texture2D.
		@Width: Picture width.
		@Height: Picture height.
	*/

	UFUNCTION(BlueprintCallable)
	static bool LoadImageToTexture2D(const FString& ImagePath, UTexture2D*& InTexture, int32& Width, int32& Height);

	UFUNCTION(BlueprintCallable, Category = "Position Management")
	static void ProcessNewValue(AActor* Actor, const FVector& NewLocation, const FRotator& NewRotation);

	UFUNCTION(BlueprintPure, Category = "Position Management")
	static float GetCurrentFPS();

	UFUNCTION(BlueprintPure, Category = "Position Management")
	static int GetSwitchView(FString value);

	UFUNCTION(BlueprintCallable, Category = "soLoad|Debug")
	static void PrintAndoridLog(const FString& Message);
	
};
