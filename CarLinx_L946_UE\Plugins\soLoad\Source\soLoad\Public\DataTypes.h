#pragma once

#include "CoreMinimal.h"
#include "DataTypes.generated.h"

// 文件格式常量
static constexpr uint32_t PARKING_FILE_MAGIC_NUMBER = 0x504B4447; // "PKDG"
static constexpr uint32_t PARKING_DATA_MAGIC_NUMBER = 0x44415441; // "DATA"

static constexpr uint32_t PARKING_DATA_VERSION = 1;

// 文件头结构
struct FParkingDataFileHeader
{
    uint32_t magic = PARKING_FILE_MAGIC_NUMBER;
    uint32_t version = PARKING_DATA_VERSION;
    uint64_t frameCount = 0;
    int64_t startTime = 0;
    int64_t endTime = 0;
};

// 数据帧结构体
struct FParkingDataFrame
{
    uint32_t magic = PARKING_DATA_MAGIC_NUMBER;
    int64_t Timestamp;
    uint32_t Size;
    TArray<uint8> Data;
};

// 数据源枚举
UENUM(BlueprintType)
enum class EDataSource : uint8
{
    Null UMETA(DisplayName = "空"),  
    UDP_RECEIVER UMETA(DisplayName = "UDP接收数据"),    
    FILE_PLAYBACK UMETA(DisplayName = "文件回放数据")
}; 


USTRUCT(BlueprintType)
struct FSTCTRACK
{
	GENERATED_USTRUCT_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackPlanningLine")
	FVector PlanningPoint;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackPlanningLine")
	int MoveType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackPlanningLine")
	int PointGroupID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackPlanningLine")
	float PointRotation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackPlanningLine")
	int TraceID;

	FSTCTRACK()
	{
		PlanningPoint = FVector(0, 0, 0);
		MoveType = 0;
		PointGroupID = 0;
		PointRotation = 0.0f;
		TraceID = 0;
	}

	FSTCTRACK(FVector planningPoint, int moveType, int pointGroupID, float pointRotation, int traceID)
	{
		PlanningPoint = planningPoint;
		MoveType = moveType;
		PointGroupID = pointGroupID;
		PointRotation = pointRotation;
		TraceID = traceID;
	}

};

USTRUCT(BlueprintType)
struct FRoadPoint
{
	GENERATED_USTRUCT_BODY()
	//中心点坐标
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RoadPoint")
	FVector PointLocation; 

	//点的类型（直线，曲线，弯道）
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RoadPoint")
	int Type; 

	//点的状态，暂时不需要处理
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RoadPoint")
	int PointSts; 

	//线宽度
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RoadPoint")
	int Width;

	//当前帧ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RoadPoint")
	int FrameID;

	//总帧数
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "RoadPoint")
	int TotalFrameNum; 

	FRoadPoint()
	{
		PointLocation = FVector(0.0f, 0.0f, 0.0f);
		Type = 0;
		PointSts = 0;
		Width = 0;
		FrameID = 1;
		TotalFrameNum = 1;
	}

	FRoadPoint(FVector pointLocation, int type, int pointSts, int width, int frameID, int totalFrameNum)
	{
		PointLocation = pointLocation;
		Type = type;
		PointSts = pointSts;
		Width = width;
		FrameID = frameID;
		TotalFrameNum = totalFrameNum;
	}
};