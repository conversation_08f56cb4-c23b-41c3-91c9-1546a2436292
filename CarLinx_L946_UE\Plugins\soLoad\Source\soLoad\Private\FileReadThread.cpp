#include "FileReadThread.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Logger.h"

FFileReadThread::FFileReadThread(const FString& InFilePath)
    : FilePath(InFilePath)
    , LastReadTime(0)
    , ReadRate(0)
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Constructor Begin"), this);
    Thread = FRunnableThread::Create(this, TEXT("FileReadThread"));
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Constructor End"), this);
}

FFileReadThread::~FFileReadThread()
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor Begin"), this);
    Stop();
    if (Thread)
    {
        Thread->WaitForCompletion();
        delete Thread;
        Thread = nullptr;
    }
    CloseFile();
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor End"), this);
}

bool FFileReadThread::Init()
{
    return OpenFile(FilePath);
}

uint32 FFileReadThread::Run()
{
    while (bRunning)
    {
        // 在这里我们不需要主动读取，因为读取是由外部调用触发的
        FPlatformProcess::Sleep(0.001f);
    }
    return 0;
}

void FFileReadThread::Stop()
{
    bRunning = false;
}

void FFileReadThread::Exit()
{
    CloseFile();
}

bool FFileReadThread::ReadFrame(std::vector<uint8_t>& OutData, int64& OutTimestamp)
{
    FScopeLock Lock(&CriticalSection);
    if (!FileHandle) return false;
    
    // 获取当前文件指针位置
    currentPos = ftell(FileHandle);
    
    // 读取帧结构
    FParkingDataFrame Frame;
    
    // 读取魔数
    if (fread(&Frame.magic, sizeof(Frame.magic), 1, FileHandle) != 1)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to read magic at position: %ld"), this, currentPos);
        return false;
    }

    // 验证魔数
    if (Frame.magic != PARKING_DATA_MAGIC_NUMBER)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Invalid frame magic: 0x%08X at position: %ld"), this, Frame.magic, currentPos);
        return false;
    }
    
    // 读取时间戳
    if (fread(&Frame.Timestamp, sizeof(Frame.Timestamp), 1, FileHandle) != 1)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to read timestamp at position: %ld"), this, ftell(FileHandle));
        return false;
    }
    
    // 读取数据大小
    if (fread(&Frame.Size, sizeof(Frame.Size), 1, FileHandle) != 1)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to read data size at position: %ld"), this, ftell(FileHandle));
        return false;
    }
    
    // 验证数据大小是否合理
    if (Frame.Size == 0 || Frame.Size > 1024 * 1024 * 10) // 最大10MB限制
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Invalid frame size: %u at position: %ld"), this, Frame.Size, ftell(FileHandle));
        return false;
    }
    
    // 读取帧数据
    Frame.Data.SetNumUninitialized(Frame.Size);
    if (fread(Frame.Data.GetData(), 1, Frame.Size, FileHandle) != Frame.Size)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to read frame data at position: %ld"), this, ftell(FileHandle));
        return false;
    }
    
    // 复制数据到输出参数
    OutData.resize(Frame.Size);
    FMemory::Memcpy(OutData.data(), Frame.Data.GetData(), Frame.Size);
    OutTimestamp = Frame.Timestamp;
    
    TotalFramesRead.Increment();

    // 更新性能统计
    double CurrentTime = FPlatformTime::Seconds();
    if (CurrentTime - LastReadTime > 1.0)
    {
        ReadRate = TotalFramesRead.GetValue() / (CurrentTime - LastReadTime);
        LastReadTime = CurrentTime;
    }

    // 获取读取完成后的文件指针位置
    long endPos = ftell(FileHandle);
    CARLINX_LOG(LogCarLinx, Log, TEXT("ReadFrame End - Start Pos: %ld, End Pos: %ld, Frame: Magic=0x%08X, Size=%u"), 
                currentPos, endPos, Frame.magic, Frame.Size);

    return true;
}

bool FFileReadThread::ReadHeader(FParkingDataFileHeader& OutHeader)
{
    FScopeLock Lock(&CriticalSection);
    if (!FileHandle) return false;

    // 移动到文件开始位置
    if (fseek(FileHandle, 0, SEEK_SET) != 0)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to seek to file start"), this);
        return false;
    }

    // 读取文件头
    if (fread(&OutHeader, sizeof(OutHeader), 1, FileHandle) != 1)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to read file header"), this);
        return false;
    }

    // 验证文件头魔数
    if (OutHeader.magic != PARKING_FILE_MAGIC_NUMBER)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Invalid file header magic number: 0x%08X"), this, OutHeader.magic);
        return false;
    }

    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Successfully read file header - Magic: 0x%08X, Version: %u, Frames: %llu"), 
        this, OutHeader.magic, OutHeader.version, OutHeader.frameCount);
    return true;
}

void FFileReadThread::SeekToBegin()
{
    FScopeLock Lock(&CriticalSection);
    if (FileHandle)
    {
        // 移动到文件头之后的位置开始读取数据
        if (fseek(FileHandle, sizeof(FParkingDataFileHeader), SEEK_SET) != 0)
        {
            CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to seek to data start position (offset: %d)"), this, sizeof(FParkingDataFileHeader));
        }
        else
        {
            currentPos = ftell(FileHandle);
            CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Successfully seeked to data start position at %ld"), this, currentPos);
        }
    }
}

bool FFileReadThread::ReadLastFrame(std::vector<uint8_t>& OutData, int64& OutTimestamp)
{
    FScopeLock Lock(&CriticalSection);
    if (!FileHandle) return false;

    //回到上一个位置
    if (fseek(FileHandle, currentPos, SEEK_SET) != 0)
    {
        //我希望这里暴漏出错误来
        check(0);
    }
    
    // 读取帧结构
    FParkingDataFrame Frame;
    
    // 读取魔数
    if (fread(&Frame.magic, sizeof(Frame.magic), 1, FileHandle) != 1)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to read magic at position: %ld"), this, currentPos);
        return false;
    }
    
    // 读取时间戳
    if (fread(&Frame.Timestamp, sizeof(Frame.Timestamp), 1, FileHandle) != 1)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to read timestamp at position: %ld"), this, ftell(FileHandle));
        return false;
    }
    
    // 读取数据大小
    if (fread(&Frame.Size, sizeof(Frame.Size), 1, FileHandle) != 1)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to read data size at position: %ld"), this, ftell(FileHandle));
        return false;
    }
    
    // 读取帧数据
    Frame.Data.SetNumUninitialized(Frame.Size);
    if (fread(Frame.Data.GetData(), 1, Frame.Size, FileHandle) != Frame.Size)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to read frame data at position: %ld"), this, ftell(FileHandle));
        return false;
    }
    
    // 复制数据到输出参数
    OutData.resize(Frame.Size);
    FMemory::Memcpy(OutData.data(), Frame.Data.GetData(), Frame.Size);
    OutTimestamp = Frame.Timestamp;
    
    // 获取读取完成后的文件指针位置
    long endPos = ftell(FileHandle);
    CARLINX_LOG(LogCarLinx, Log, TEXT("ReadFrame End - Start Pos: %ld, End Pos: %ld, Frame: Magic=0x%08X, Size=%u"), 
                currentPos, endPos, Frame.magic, Frame.Size);

    return true;
}


bool FFileReadThread::OpenFile(const FString& InFilePath)
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] OpenFile Begin - Path: %s"), this, *InFilePath);

    // 检查文件路径是否为空
    if (InFilePath.IsEmpty())
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] File path is empty"), this);
        return false;
    }

    // 检查文件是否存在
    if (!IFileManager::Get().FileExists(*InFilePath))
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] File does not exist: %s"), this, *InFilePath);
        return false;
    }

    // 检查文件大小
    int64 FileSize = IFileManager::Get().FileSize(*InFilePath);
    if (FileSize <= 0)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] File is empty or invalid: %s (size: %lld)"), this, *InFilePath, FileSize);
        return false;
    }

    // 检查文件权限
    if (!IFileManager::Get().IsReadOnly(*InFilePath))
    {
        CARLINX_LOG(LogCarLinx, Warning, TEXT("[0x%p] File is not read-only: %s"), this, *InFilePath);
    }

    // 尝试打开文件
    FileHandle = fopen(TCHAR_TO_UTF8(*InFilePath), "rb");
    if (!FileHandle)
    {
        // 获取系统错误信息
        int ErrorCode = errno;
        FString ErrorMsg = UTF8_TO_TCHAR(strerror(ErrorCode));
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to open file: %s (Error: %s)"), this, *InFilePath, *ErrorMsg);
        return false;
    }

    // 检查文件是否可读
    int fd = fileno(FileHandle);
    if (fd == -1)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to get file descriptor"), this);
        fclose(FileHandle);
        FileHandle = nullptr;
        return false;
    }

    // 检查文件是否可以读取
    if (fseek(FileHandle, 0, SEEK_SET) != 0)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to seek file"), this);
        fclose(FileHandle);
        FileHandle = nullptr;
        return false;
    }

    bFileOpened = true;
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Successfully opened file: %s (size: %lld bytes)"), this, *InFilePath, FileSize);
    return true;
}

void FFileReadThread::CloseFile()
{
    if (FileHandle)
    {
        fclose(FileHandle);
        FileHandle = nullptr;
        bFileOpened = false;
    }
} 