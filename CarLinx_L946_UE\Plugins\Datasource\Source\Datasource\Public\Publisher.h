#pragma once
#include <iostream>
#include <string>
#include <map>
#include <unordered_map>
#include <atomic>
#include <functional>

class ASubscriber;
namespace hege {
	class Publisher {
	public:
		static Publisher* GetInstance();
		~Publisher();
		bool RegisterSub(FString& devName, ASubscriber* subObj);
		bool DelSub(FString& devName);
		bool DelAllSubs();

		void PubMsg(std::string topic, int value);
		void PubMsg(std::string topic, float value);
		void PubMsg(std::string topic, std::string value);
		void PubMsg(std::string topic, TArray<float> value);
		void PubMsg(std::string topic, TArray<FVector> value);
		void PubMsg(std::string topic, TMap<int,FVector> value, TArray<float> RotationValue, TArray<int> Type);
	private:
		Publisher();

	private:
		static Publisher* sPubInst;
		std::atomic<int> mThreadRunning;
		std::map<FString, ASubscriber*> mSubMap;
		std::unordered_map<std::string, int> mStoreMapInt;
		std::unordered_map<std::string, float> mStoreMapFloat;
		std::unordered_map<std::string, std::string> mStoreMapStr;
		std::unordered_map<std::string, TArray<float>> mStoreMapTarryFloat;
		std::unordered_map<std::string, TArray<FVector>> mStoreMapTarryVector;
		std::unordered_map<std::string, TMap<int,FVector>> mStoreMapTMapVector;
		std::unordered_map<std::string, TArray<float>> mStoreRotationValue;
		std::unordered_map<std::string, TArray<int>> mStoreTypeValue;
	};
}

