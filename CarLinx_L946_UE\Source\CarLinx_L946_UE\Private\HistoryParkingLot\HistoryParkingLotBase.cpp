﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "HistoryParkingLot/HistoryParkingLotBase.h"

#include "Components/WidgetComponent.h"


// Sets default values
AHistoryParkingLotBase::AHistoryParkingLotBase()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

	Root = CreateDefaultSubobject<USceneComponent>("Root");
	RootComponent = Root;
	SlotWidget = CreateDefaultSubobject<UWidgetComponent>(TEXT("Widget"));
	SlotWidget->SetupAttachment(RootComponent);
}

// Called when the game starts or when spawned
void AHistoryParkingLotBase::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AHistoryParkingLotBase::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void AHistoryParkingLotBase::IniteParkingSize(float sizeX, float sizeY)
{
	if (SlotWidget != nullptr)
	{
		SlotWidget->SetDrawSize(FVector2D(sizeX,sizeY));
	}
}

void AHistoryParkingLotBase::UpdateHistoryParkingLotInfo(int32 Status, int32 Number, FVector Location,float Rotation, ECarSlot CarSlotType, float InWidth)
{
	//实际执行流程固定
	NumberRotation(CarSlotType);

	Width = InWidth;
	NumberCtl(Status,Number);
	UpdateDisMat(Status);
	IniteParkingSize(570.f,Width + 20.f);
	SetNumberVisibility(false);
}

