// Fill out your copyright notice in the Description page of Project Settings.


#include "BFL_National.h"
#include "Logger.h"
#include "IImageWrapper.h"
#include "IImageWrapperModule.h"

void UBFL_National::SplitScreen(TArray<FSplitData> DataArray)
{
	if (GEngine && GEngine->GameViewport)
	{
		if (DataArray.Num() == 2)
		{
			for (int i = 0; i < 2; i++)
			{
				GEngine->GameViewport->SplitscreenInfo[ESplitScreenType::TwoPlayer_Vertical].PlayerData[i].OriginX = DataArray[i].OriginX;
				GEngine->GameViewport->SplitscreenInfo[ESplitScreenType::TwoPlayer_Vertical].PlayerData[i].OriginY = DataArray[i].OriginY;
				GEngine->GameViewport->SplitscreenInfo[ESplitScreenType::TwoPlayer_Vertical].PlayerData[i].SizeX = DataArray[i].SizeX;
				GEngine->GameViewport->SplitscreenInfo[ESplitScreenType::TwoPlayer_Vertical].PlayerData[i].SizeY = DataArray[i].SizeY;
			}
		}
		//log
		GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Red, TEXT("SplitScreen"));
	}
}

UTexture2D* UBFL_National::LoadTextureFromPath(const FString& Path)
{
	if (Path.IsEmpty()) return NULL;
	return Cast<UTexture2D>(StaticLoadObject(UTexture2D::StaticClass(), NULL, *(Path)));
}

FVector originalLocation;

bool UBFL_National::LoadImageToTexture2D(const FString& ImagePath, UTexture2D*& InTexture, int32& Width, int32& Height)
{
	TArray<uint8> SourceImageData;  //存储原始图片的颜色信息

	IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked <IImageWrapperModule>(FName("ImageWrapper"));//启用"ImageWrapper"模型
	TSharedPtr<IImageWrapper> SourceImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::PNG);//实例化ImageWrapper类，构造参数为输入图片的类型,这里写的PNG，也可以是：JPEG，单通道JPEG,BMP,ICO,EXR,ICNS(MAC ICO)

	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*ImagePath))//是否存在该文件
	{
		return false;
	}

	if (!FFileHelper::LoadFileToArray(SourceImageData, *ImagePath))//将文件有压缩的文件信息传入SourceImageData
	{
		return false;
	}

	if (SourceImageWrapper.IsValid() && SourceImageWrapper->SetCompressed(SourceImageData.GetData(), SourceImageData.GetAllocatedSize()))//填充数据进创建的"ImageWrapper"
	{
		TArray <uint8> UncompressedBGRA;                    //存储未压缩的颜色数据，UE4用的颜色格式为BGRA
		if (SourceImageWrapper->GetRaw(ERGBFormat::BGRA, 8, UncompressedBGRA))//获取未压缩的图片颜色信息
		{
			Height = SourceImageWrapper->GetHeight();   //获取高度和宽度
			Width = SourceImageWrapper->GetWidth();

			InTexture = UTexture2D::CreateTransient(Width, Height, PF_B8G8R8A8);    //临时填充——应该是先确定图片长宽->再填充颜色数据

			if (InTexture)
			{
				void* TextureData = InTexture->PlatformData->Mips[0].BulkData.Lock(LOCK_READ_WRITE);    //用自带方法"PlatformData"让 InTexture和指针TextureData绑定(Lock)并且可读可写,因为下一行的复制函数中的参数要求为void*


				FMemory::Memcpy(TextureData, UncompressedBGRA.GetData(), UncompressedBGRA.Num());   //复制，将未压缩的颜色数据和数据大小复制

				InTexture->PlatformData->Mips[0].BulkData.Unlock();//将指针和InTexture解绑(unlock)

				InTexture->UpdateResource();//刷新
				return true;
			}
		}
	}
	return false;
}

void UBFL_National::ProcessNewValue(AActor* Actor, const FVector& NewLocation, const FRotator& NewRotation)
{
	if (!Actor) return;

	static const FName FirstValueTag = TEXT("FirstValueProcessed");

	if (!Actor->Tags.Contains(FirstValueTag))
	{
		// 第一次接收到值，将其设置为世界原点中心
		Actor->SetActorLocationAndRotation(NewLocation, NewRotation);
		originalLocation = Actor->GetActorLocation();
		Actor->Tags.Add(FirstValueTag);
	}
	else
	{
		// 后续接收到的值作为移动值，旋转值直接设置
		FVector NewActorLocation = NewLocation - originalLocation;

		Actor->SetActorLocationAndRotation(NewActorLocation, NewRotation);
	}
}

float UBFL_National::GetCurrentFPS()
{
	extern ENGINE_API float GAverageFPS;
	//GEngine->AddOnScreenDebugMessage(-1, 5.f, FColor::Red, FString::SanitizeFloat(GAverageFPS));
	//UE_LOG(LogTemp, Warning, TEXT("[carlinx][UE-BluePrintFunction]FPS: %f"), GAverageFPS);
	return GAverageFPS;
}

int UBFL_National::GetSwitchView(FString value)
{
	if (value == "0") {
		return 0;
	} else if (value == "10" || value == "20" || value == "30" || value == "33" || 
			   value == "50" || value == "53" || value == "55" || value == "60" || 
			   value == "67" || value == "69" || value == "74" || value == "76" || 
			   value == "80" || value == "81" || value == "101" || value == "111" || 
			   value == "121") {
		return 1;
	} else if (value == "79" || value == "83") {
		return 2;
	} else if (value == "13" || value == "15" || value == "16" || value == "17" || 
			   value == "23" || value == "25" || value == "26" || value == "27" || 
			   value == "35" || value == "37" || value == "38" || value == "39" || 
			   value == "42" || value == "43" || value == "65" || value == "66" || 
			   value == "68" || value == "77" || value == "82" || value == "106" || 
			   value == "126") {
		return 3;
	} else if (value == "14" || value == "18" || value == "24" || value == "28" || 
               value == "36" || value == "40" || value == "44" || value == "57" || 
               value == "58" || value == "102" || value == "114" || value == "122") {
		return 4;
	} else if (value == "11" || value == "12" || value == "21" || value == "22" || 
               value == "31" || value == "32" || value == "34" || value == "41" || 
               value == "51" || value == "52" || value == "54" || value == "56" || 
               value == "62" || value == "63" || value == "71" || value == "72" || 
               value == "75" || value == "64" || value == "103" || value == "112" || 
               value == "123") {
		return 5;
	} else if (value == "104" || value == "113" || value == "124") {
		return 6;
	} else if (value == "61" || value == "70" || value == "73") {
		return 7;
	} else if (value == "98") {
		return 8;
	} else if (value == "99") {
		return 9;
	} else if (value == "78" || value == "84"){
		return 10;	
	} else if (value == "105" || value == "125"){
		return 11;	
	} else {
		return -1;
	}
}

void UBFL_National::PrintAndoridLog(const FString& Message)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[BP] %s"), *Message);
}
