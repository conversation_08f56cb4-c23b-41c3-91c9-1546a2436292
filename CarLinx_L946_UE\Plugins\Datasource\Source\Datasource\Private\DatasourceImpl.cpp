#include "DatasourceImpl.h"
#include "Publisher.h"
#include "Subscriber.h"
#include "AndroidDataFeeder.h"

void UDatasourceImpl::InitPublisher() {
	mPublisher = hege::Publisher::GetInstance();
	
}

void UDatasourceImpl::RegisterSub(FString& name, ASubscriber* sub) {
	hege::Publisher::GetInstance()->RegisterSub(name, sub);
}

void UDatasourceImpl::RemoveSub(FString& name) {
	hege::Publisher::GetInstance()->DelSub(name);
}

void UDatasourceImpl::RemoveAllSubs() {
	hege::Publisher::GetInstance()->DelAllSubs();
}

void UDatasourceImpl::NotifyPlatformInt(FString& topicName, int& value) {
	std::string name = TCHAR_TO_UTF8(*topicName);
#if WITH_EDITOR
	hege::Publisher::GetInstance()->PubMsg(name, value);
#endif
	CallbackPlatformIntValue(name, value);

	
}

void UDatasourceImpl::NotifyPlatformFloat(FString& topicName, float& value) {
	std::string name = TCHAR_TO_UTF8(*topicName);
#if WITH_EDITOR
	hege::Publisher::GetInstance()->PubMsg(name, value);
#endif
	CallbackPlatformFloatValue(name, value);
}

void UDatasourceImpl::NotifyPlatformString(FString& topicName, FString& value) {
	std::string name = TCHAR_TO_UTF8(*topicName);
	std::string vvalue = TCHAR_TO_UTF8(*value);
#if WITH_EDITOR
	hege::Publisher::GetInstance()->PubMsg(name, vvalue);
#endif
	CallbackPlatformStringValue(name, vvalue);
}

void UDatasourceImpl::NotifyPlatformTarryFloat(FString& topicName, TArray<float>& value)
{
	std::string name = TCHAR_TO_UTF8(*topicName);
#if WITH_EDITOR
	hege::Publisher::GetInstance()->PubMsg(name, value);
#endif
	CallbackPlatformTarryFloatValue(name, value);
}

void UDatasourceImpl::NotifyPlatformTarryVector(FString& topicName, TArray<FVector>& value)
{
	std::string name = TCHAR_TO_UTF8(*topicName);
#if WITH_EDITOR
	hege::Publisher::GetInstance()->PubMsg(name, value);
#endif

	TArray<float> XValue;
	TArray<float> YValue;
	TArray<float> ZValue;
	for (int i = 0;i < value.Num();i++)
	{
		XValue.Add(value[i].X);
		YValue.Add(value[i].Y);
		ZValue.Add(value[i].Z);
	}
	CallbackPlatformTarryVectorValue(name, XValue,YValue,ZValue);
}

void UDatasourceImpl::NotifyPlatformTMapVector(FString& topicName, TMap<int, FVector>& value, TArray<float>& RotationValue, TArray<int>& Type)
{
	std::string name = TCHAR_TO_UTF8(*topicName);
#if WITH_EDITOR
	hege::Publisher::GetInstance()->PubMsg(name, value, RotationValue, Type);
#endif

	TArray<int> NameValue;
	TArray<float> XValue;
	TArray<float> YValue;
	TArray<float> ZValue;
	for (const auto& Pair : value)
	{
		NameValue.Add(Pair.Key);
		XValue.Add(Pair.Value.X);
		YValue.Add(Pair.Value.Y);
		ZValue.Add(Pair.Value.Z);
	}
	CallbackPlatformTMapVectorValue(name,NameValue,XValue,YValue,ZValue,RotationValue,Type);
}

void UDatasourceImpl::SetTMapVectorValue(FString& topicName, TMap<int, FVector>& value, TArray<float>& RotationValue, TArray<int>& Type)
{
	std::string name = TCHAR_TO_UTF8(*topicName);
	hege::Publisher::GetInstance()->PubMsg(name, value, RotationValue, Type);
}

void UDatasourceImpl::SetTarryVectorValue(FString& topicName, TArray<FVector>& value)
{
	std::string name = TCHAR_TO_UTF8(*topicName);
	hege::Publisher::GetInstance()->PubMsg(name, value);
}

void UDatasourceImpl::SetTarryFloatValue(FString& topicName, TArray<float>& value)
{
	std::string name = TCHAR_TO_UTF8(*topicName);
	hege::Publisher::GetInstance()->PubMsg(name, value);
}

void UDatasourceImpl::SetIntValue(FString& topicName, int& value) {
	std::string name = TCHAR_TO_UTF8(*topicName);
	hege::Publisher::GetInstance()->PubMsg(name, value);
}

void UDatasourceImpl::SetFloatValue(FString& topicName, float& value) {
	std::string name = TCHAR_TO_UTF8(*topicName);
	hege::Publisher::GetInstance()->PubMsg(name, value);
}

void UDatasourceImpl::SetStringValue(FString& topicName, FString& value) {
	std::string name = TCHAR_TO_UTF8(*topicName);
	std::string vvalue = TCHAR_TO_UTF8(*value);
	hege::Publisher::GetInstance()->PubMsg(name, vvalue);
}