#include "ParkingDataSerializationManager.h"
#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "HAL/FileManager.h"
#include "Misc/DateTime.h"

#include "Logger.h"
#include "GlobalConfig.h"
#include <chrono>

#if PLATFORM_ANDROID
// 定义多个备选路径*(FPaths::ProjectDir() + TEXT("HPAMapID/MapID.json"))
#define ANDROID_PATH_1 TEXT("/storage/emulated/0/Android/data/com.geely.carlinx/files/UE_Log/SR/")
#define ANDROID_PATH_2 TEXT("/storage/emulated/0/UE_Log/SR/")
#define ANDROID_PATH_3 TEXT("/sdcard/Android/data/com.geely.carlinx/files/UE_Log/SR/")
#define ANDROID_PATH_4 TEXT("/sdcard/UE_Log/SR/")
#define ANDROID_PATH_5 TEXT("/data/user/10/cn.carlinx.parking/UE_Log/SR/")
#define ANDROID_PATH_6 (FPaths::ProjectDir() + TEXT("UE_Log/SR/"))

// 默认使用第一个路径
#define DEFAULT_RECORD_PATH ANDROID_PATH_1
#define DEFAULT_FILENAME TEXT("SR_DATA")
#define DEFAULT_FILE_EXT TEXT(".dat")
#elif PLATFORM_WINDOWS
#define DEFAULT_RECORD_PATH TEXT("D:/UE_Log/SR/")
#define DEFAULT_FILENAME TEXT("SR_DATA")
#define DEFAULT_FILE_EXT TEXT(".dat")
#endif

UParkingDataSerializationManager::UParkingDataSerializationManager()
    : BaseFilePath(TEXT(""))
    , CurrentFilePath(TEXT(""))
    , CurrentPlaybackFrame(0)
{
}

UParkingDataSerializationManager::~UParkingDataSerializationManager()
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor Begin"), this);
    StopRecording();
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor End"), this);
}

UParkingDataSerializationManager* UParkingDataSerializationManager::GetInstance()
{
    static UParkingDataSerializationManager* Instance = nullptr;
    if (!Instance)
    {
        CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] GetInstance first time Begin"), Instance);
        Instance = NewObject<UParkingDataSerializationManager>();
        Instance->AddToRoot(); // 防止被垃圾回收
        CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] GetInstance first time End"), Instance);
    }
    return Instance;
}

bool UParkingDataSerializationManager::StartRecording()
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Begin"), this);
    
    // 如果线程已存在且正在运行，检查是否需要创建新文件
    if (FileWriterThread && FileWriterThread->IsRunning())
    {
        // 获取当前文件路径对应的状态
        FString CurrentState;
        if (!CurrentFilePath.IsEmpty())
        {
            // 从文件路径中提取状态名
            FString FileName = FPaths::GetCleanFilename(CurrentFilePath);
            TArray<FString> Parts;
            FileName.ParseIntoArray(Parts, TEXT("_"));
            if (Parts.Num() >= 3)
            {
                CurrentState = Parts.Last().Replace(TEXT(".dat"), TEXT(""));
            }
        }

        // 获取新的状态名
        FString NewState;
        switch (UGlobalConfig::GetInstance()->GetCurrentState())
        {
            case EParkingState::APA:
                NewState = TEXT("APA");
                break;
            case EParkingState::HPA:
                NewState = TEXT("HPA");
                break;
            case EParkingState::LSDA:
                NewState = TEXT("LSDA");
                break;
            case EParkingState::AVM:
                NewState = TEXT("AVM");
                break;
            default:
                NewState = TEXT("BACKGROUND");
                break;
        }

        // 如果状态没有改变，继续使用当前文件
        if (CurrentState == NewState)
        {
            CARLINX_LOG(LogCarLinx, Log, TEXT("Continue using current file for state: %s"), *NewState);
            return true;
        }
    }

    // 重置帧计数
    CurrentPlaybackFrame = 0;

    // 获取开始时间
    int64_t startTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    // 如果线程已存在且正在运行，先停止
    if (FileWriterThread && FileWriterThread->IsRunning())
    {
        FileWriterThread->Stop();
    }

    // 创建新的写入线程
    GenerateFilePathForState();
    FileWriterThread = MakeUnique<FFileWriterThread>(CurrentFilePath);
    if (!FileWriterThread->Start())
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to start file writer thread"));
        return false;
    }

    // 初始化文件头
    FileWriterThread->InitHeader(startTime);
    
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] End"), this);
    return true;
}

void UParkingDataSerializationManager::StopRecording()
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StopRecording Begin"), this);
    if (!FileWriterThread || !UGlobalConfig::GetInstance()->GetEnableDataRecording()) return;
    
    // 更新文件头中的帧计数和结束时间
    int64_t endTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    FileWriterThread->UpdateHeader(CurrentPlaybackFrame, endTime);
    
    // 暂停线程，但不退出
    FileWriterThread->Stop();
    
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StopRecording End"), this);
}

void UParkingDataSerializationManager::RecordFrame(const uint8_t* Data, uint32_t DataSize)
{
    if (!Data || DataSize == 0)
    {
        CARLINX_LOG(LogCarLinx, Warning, TEXT("data or datasize abnormal"));
        return;
    }

    if (!FileWriterThread)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("Initialization is not started before calling, and default parameters are used."));
        return;
    }

    CARLINX_LOG(LogCarLinx, Log, TEXT("[RecordFrame] Begin - DataSize: %u"), DataSize);

    // 构造帧数据结构
    FParkingDataFrame Frame;
    Frame.magic = PARKING_DATA_MAGIC_NUMBER;
    Frame.Timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    Frame.Size = DataSize;
    
    // 复制数据到Frame的Data成员
    Frame.Data.SetNumUninitialized(DataSize);
    FMemory::Memcpy(Frame.Data.GetData(), Data, DataSize);

    CARLINX_LOG(LogCarLinx, Log, TEXT("[RecordFrame] Prepared frame data - Magic: 0x%08X, Total Size: %u"), Frame.magic, Frame.Size);

    // 通过线程写入文件
    FileWriterThread->EnqueueData(Frame);
    
    CurrentPlaybackFrame++;
    
    // 更新文件头中的帧计数和时间戳
    FileWriterThread->UpdateHeader(CurrentPlaybackFrame, Frame.Timestamp);

    CARLINX_LOG(LogCarLinx, Log, TEXT("[RecordFrame] End - Frame: %d"), CurrentPlaybackFrame);
}

void UParkingDataSerializationManager::UpdateRecordingFile()
{
    if (!UGlobalConfig::GetInstance()->GetEnableDataRecording()) {
        return;
    }
    // 每次更新文件如果已经有过则尝试先停止再开始
    if (FileWriterThread)
    {
        StopRecording();
    }

    StartRecording();
}

FString UParkingDataSerializationManager::GenerateFilePathForState()
{

#if PLATFORM_ANDROID
    // 检查并选择可用的存储路径
    FString SelectedPath;
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    
    TArray<FString> PotentialPaths = {
        FString(ANDROID_PATH_1),
        FString(ANDROID_PATH_2),
        FString(ANDROID_PATH_3),
        FString(ANDROID_PATH_4),
        FString(ANDROID_PATH_5),
        FString(ANDROID_PATH_6)
    };

    for (const FString& Path : PotentialPaths)
    {
        // 尝试创建目录
        if (PlatformFile.CreateDirectoryTree(*Path))
        {
            // 检查目录是否可写
            if (PlatformFile.DirectoryExists(*Path))
            {
                SelectedPath = Path;
                CARLINX_LOG(LogCarLinx, Log, TEXT("Selected storage path: %s"), *SelectedPath);
                break;
            }
        }
    }

    // 如果所有路径都不可用，使用默认路径
    if (SelectedPath.IsEmpty())
    {
        SelectedPath = DEFAULT_RECORD_PATH;
        CARLINX_LOG(LogCarLinx, Warning, TEXT("No writable path found, using default path: %s"), *SelectedPath);
    }

    // 保存基础文件路径
    BaseFilePath = SelectedPath;
#else
    // 保存基础文件路径
    BaseFilePath = DEFAULT_RECORD_PATH;
#endif

    FString StateName;
    switch (UGlobalConfig::GetInstance()->GetCurrentState())
    {
        case EParkingState::APA:
            StateName = TEXT("APA");
            break;
        case EParkingState::HPA:
            StateName = TEXT("HPA");
            break;
        case EParkingState::LSDA:
            StateName = TEXT("LSDA");
            break;
        case EParkingState::AVM:
            StateName = TEXT("AVM");
            break;
        default:
            StateName = TEXT("BACKGROUND");
            break;
    }

    // 生成日期和时间戳
    FDateTime Now = FDateTime::Now();
    FString DateFolder = Now.ToString(TEXT("%Y%m%d"));  // 日期文件夹，格式：YYYYMMDD
    FString TimeStamp = Now.ToString(TEXT("%H-%M-%S"));   // 时间戳，格式：HHMMSS
    
    // 检查并删除3天前的数据文件
    FDateTime ThreeDaysAgo = Now - FTimespan::FromDays(3);
    FString OldDateFolder = ThreeDaysAgo.ToString(TEXT("%Y%m%d"));
    FString OldFolderPath = FPaths::Combine(BaseFilePath, OldDateFolder);
    
    if (IFileManager::Get().DirectoryExists(*OldFolderPath))
    {
        CARLINX_LOG(LogCarLinx, Log, TEXT("Found old data folder: %s"), *OldFolderPath);
        
        // 查找并删除所有.dat文件
        TArray<FString> FoundFiles;
        IFileManager::Get().FindFiles(FoundFiles, *FPaths::Combine(OldFolderPath, TEXT("*.dat")), true, false);
        
        int32 DeletedCount = 0;
        for (const FString& File : FoundFiles)
        {
            FString FullPath = FPaths::Combine(OldFolderPath, File);
            if (IFileManager::Get().Delete(*FullPath))
            {
                DeletedCount++;
                CARLINX_LOG(LogCarLinx, Log, TEXT("Deleted old data file: %s"), *FullPath);
            }
        }
        
        if (DeletedCount > 0)
        {
            CARLINX_LOG(LogCarLinx, Log, TEXT("Deleted %d old data files from folder: %s"), DeletedCount, *OldFolderPath);
        }
    }

    // 组合文件名
    FString FileName;
    FileName = BaseFilePath;
    FileName += DEFAULT_FILENAME;
    FileName += TEXT("_");
    FileName += TimeStamp;
    FileName += TEXT("_");
    FileName += StateName;
    FileName += DEFAULT_FILE_EXT;

    // 创建日期文件夹路径
    FString DateFolderPath = FPaths::Combine(BaseFilePath, DateFolder);
    
    // 确保日期文件夹存在
    if (!IFileManager::Get().DirectoryExists(*DateFolderPath))
    {
        IFileManager::Get().MakeDirectory(*DateFolderPath);
    }
    
    // 组合最终文件路径
    FString FinalFilePath = FPaths::Combine(DateFolderPath, FPaths::GetCleanFilename(FileName));
    CARLINX_LOG(LogCarLinx, Warning, TEXT("DateFolderPath: %s, FinalFilePath: %s "), *DateFolderPath, *FinalFilePath);  
    
    CurrentFilePath = FinalFilePath;
    return FinalFilePath;
}