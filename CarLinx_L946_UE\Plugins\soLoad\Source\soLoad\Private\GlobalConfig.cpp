// Project headers
#include "GlobalConfig.h"

// UE4 Core headers
#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "UObject/Object.h"

#include "Logger.h"

UGlobalConfig* UGlobalConfig::Instance = nullptr;

UGlobalConfig* UGlobalConfig::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UGlobalConfig>();
        if (Instance)
        {
            Instance->AddToRoot();
            Instance->Reset();
        }
    }
    return Instance;
}

void UGlobalConfig::Reset()
{
    bEnableDataRecording = false;
    bEnablePerformanceLog = false;
    bHPACreating = false;
    CurrentState = EParkingState::BACKGROUND;
}

bool UGlobalConfig::GetEnableDataRecording() const
{
    return bEnableDataRecording;
}

void UGlobalConfig::SetEnableDataRecording(bool bEnable)
{
    bEnableDataRecording = bEnable;
}

bool UGlobalConfig::GetEnablePerformanceLog() const
{
    return bEnablePerformanceLog;
}

void UGlobalConfig::SetEnablePerformanceLog(bool bEnable)
{
    if (bEnablePerformanceLog != bEnable)
    {
        bEnablePerformanceLog = bEnable;
        CARLINX_LOG(LogCarLinx, Log, TEXT("Performance log %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
    }
}

EParkingState UGlobalConfig::GetCurrentState() const
{
    return CurrentState;
}

bool UGlobalConfig::SetCurrentState(EParkingState NewState)
{
    if (CurrentState != NewState)
    {
        CurrentState = NewState;
        CARLINX_LOG(LogCarLinx, Log, TEXT("CurrentState changed to %s"), *UEnum::GetValueAsString(CurrentState));
        return true;
    }
    else 
    {
        return false;
    }
}