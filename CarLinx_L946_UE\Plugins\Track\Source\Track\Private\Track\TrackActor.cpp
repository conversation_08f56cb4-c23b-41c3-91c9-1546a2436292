// Fill out your copyright notice in the Description page of Project Settings.


#include "Track/TrackActor.h"
#include "UObject/ConstructorHelpers.h"
#include "Kismet/KismetMathLibrary.h"

// Sets default values
ATrackActor::ATrackActor()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	SplineComponent = CreateDefaultSubobject<USplineComponent>("Spline");

	SplineComponent->SetMobility(EComponentMobility::Movable);

	SplineComponent->SetupAttachment(RootComponent);
	SplineComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);

	MeshSpacing = 0.001f;

	bIsColumnar = false;

	//ProceduralMeshComponent
	ProcMeshComponent = CreateDefaultSubobject<UProceduralMeshComponent>(TEXT("ProceduralMeshComponent"));
	ProcMeshComponent->SetupAttachment(RootComponent);
	ProcMeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	CurvePointNum = 3;
}

// Called when the game starts or when spawned
void ATrackActor::BeginPlay()
{
	Super::BeginPlay();
}

void ATrackActor::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);
}

// Called every frame
void ATrackActor::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void ATrackActor::BeginUpdate()
{
	ClearSplineMesh(false);
}

void ATrackActor::AddPoint(const FVector& Point)
{
	SplineComponent->AddSplineWorldPoint(Point);
}

void ATrackActor::EndUpdate()
{
	if (EXTPaths.Num()>0)
	{
		//ProcMeshComponent->SetWorldLocation(EXTPaths[0]);

	//	ProcMeshComponent->UpdateBounds();
	//	ProcMeshComponent->UpdateNavigationBounds();
	}

//	SplineComponent->SetWorldLocation(GetActorLocation());

	ClearSplineMesh(false);

	float SplineLength = SplineComponent->GetSplineLength();

	MeshSpacing = EXTSize * 100 / SplineLength;

	for (float t = 0.0f; t <= 1.0f; t += MeshSpacing)
	{
		float CurrentDistance = UKismetMathLibrary::FInterpTo(0, SplineLength, t, 1.f);

		float NextDistance = UKismetMathLibrary::FInterpTo(0, SplineLength, t + MeshSpacing, 1.0f);

		FVector StartPoint = SplineComponent->GetLocationAtDistanceAlongSpline(CurrentDistance, ESplineCoordinateSpace::Type::World);
		FVector EndPoint = SplineComponent->GetLocationAtDistanceAlongSpline(NextDistance, ESplineCoordinateSpace::Type::World);

		FVector StartTangent = SplineComponent->GetTangentAtDistanceAlongSpline(CurrentDistance, ESplineCoordinateSpace::Type::World);
		FVector EndTangent = SplineComponent->GetTangentAtDistanceAlongSpline(NextDistance, ESplineCoordinateSpace::Type::World);

		FAttachmentTransformRules AttachmentRules(EAttachmentRule::KeepRelative, false);
		USplineMeshComponent* NewSpline = NewObject<USplineMeshComponent>(this);

		NewSpline->SetMobility(EComponentMobility::Movable);
		NewSpline->AttachToComponent(GetRootComponent(), AttachmentRules);
		NewSpline->SetStaticMesh(TrackMesh);
		
		NewSpline->SetStaticMesh(TrackMesh);
		NewSpline->SetStartScale(FVector2D(EXTSize, 1));
		NewSpline->SetEndScale(FVector2D(EXTSize, 1));

		NewSpline->SetStartScale(FVector2D(EXTSize, EXTSize));
		NewSpline->SetEndScale(FVector2D(EXTSize, EXTSize));

		if (TrackMat)
		{
			TrackDynamicMat = UKismetMaterialLibrary::CreateDynamicMaterialInstance(GetWorld(), TrackMat, NAME_None);

			NewSpline->SetMaterial(0, TrackDynamicMat);
		}

		NewSpline->SetStartAndEnd(StartPoint, StartTangent, EndPoint, EndTangent, false);

		//Set the up dir for the spline
		SplineComponent->SetDefaultUpVector(FVector::UpVector, ESplineCoordinateSpace::World);

		FVector SplineUpDir = UKismetMathLibrary::SelectVector(FVector::UpVector, SplineComponent->GetUpVectorAtDistanceAlongSpline(CurrentDistance, ESplineCoordinateSpace::Local), true);
		NewSpline->SetSplineUpDir(SplineUpDir);

		NewSpline->UpdateMesh();
		NewSpline->RegisterComponent();

		NewSpline->SetCollisionObjectType(ECC_Visibility);

		//NewSpline->SetCollisionEnabled(ECollisionEnabled::QueryOnly);

		
		NewSpline->SetCollisionResponseToChannel(ECC_GameTraceChannel1,ECollisionResponse::ECR_Ignore);

		//NewSpline->SetGenerateOverlapEvents(true);
		//NewSpline->CanCharacterStepUpOn = ECanBeCharacterBase::ECB_Yes;
		NewSpline->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
		NewSpline->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

		SplineMeshComponents.Add(NewSpline);
	}

}

void ATrackActor::ClearSplineMesh(bool bProcMeshVisible)
{
	for (UMeshComponent* SplineMeshComponent : SplineMeshComponents)
	{

		if (SplineMeshComponent&& SplineMeshComponent->GetClass() == USplineMeshComponent::StaticClass())
		{
			SplineMeshComponent->DestroyComponent();
		}
	}

	SplineMeshComponents.Empty();

	SplineMeshComponents.Add(ProcMeshComponent);
	if (ProcMeshComponent)
	{
		ProcMeshComponent->SetVisibility(bProcMeshVisible);		

	}

	// Clear all spline mesh

	//
	//SplineComponent->ClearSplinePoints();

	//顶点集
	VertexArr.Empty();
	//顶点Index
	TriangleArr.Empty();
	//法线
	NormalArr.Empty();
	//UVs
	UVArr.Empty();
	//切线
	TangentArr.Empty();
}

void ATrackActor::SetMeshSpacing(float InMeshSpacing)
{
	MeshSpacing = InMeshSpacing;
}

void ATrackActor::SetPoints()
{
	SplineComponent->ClearSplinePoints();
	SplineComponent->SetSplineWorldPoints(EXTPaths);
}

void ATrackActor::SetTrackInterp(FString TrackInterp)
{
	for (int32 i = 0; i < SplineComponent->GetNumberOfSplinePoints(); ++i)
	{
		if (TrackInterp == "line")
		{
			SplineComponent->SetSplinePointType(i, ESplinePointType::Linear);
		}
		else
		{
			SplineComponent->SetSplinePointType(i, ESplinePointType::Curve);
		}
	}
}

//promesh
void ATrackActor::SetTrackPoint()
{
	LineSize = RoundSize * 2;

	TArray<FVector> RightPoint;

	TArray<FVector> LeftPoint;
	/////////计算第一对点
	if (EXTPaths.Num()<2)
	{
	    return;
	}
	//垂直X轴
	if (EXTPaths[0].X - EXTPaths[1].X == 0 && EXTPaths[0].Y - EXTPaths[1].Y <= 0)
	{
		LeftPoint.Add(FVector(EXTPaths[0].X - RoundSize, EXTPaths[0].Y, EXTPaths[0].Z));

		LeftUp = EXTPaths[0].X - RoundSize;

		RightPoint.Add(FVector(EXTPaths[0].X + RoundSize, EXTPaths[0].Y, EXTPaths[0].Z));

		RightUp = EXTPaths[0].X + RoundSize;

		IsUp = true;
	}
	else if (EXTPaths[0].X - EXTPaths[1].X == 0 && EXTPaths[0].Y - EXTPaths[1].Y > 0)
	{
		LeftPoint.Add(FVector(EXTPaths[0].X + RoundSize, EXTPaths[0].Y, EXTPaths[0].Z));

		LeftUp = EXTPaths[0].X + RoundSize;

		RightPoint.Add(FVector(EXTPaths[0].X - RoundSize, EXTPaths[0].Y, EXTPaths[0].Z));

		RightUp = EXTPaths[0].X - RoundSize;

		IsUp = true;
	}
	else if (EXTPaths[0].Y - EXTPaths[1].Y == 0 && EXTPaths[0].X - EXTPaths[1].X <= 0)
	{
		LineK = 0;

		LeftPoint.Add(FVector(EXTPaths[0].X, EXTPaths[0].Y + RoundSize, EXTPaths[0].Z));

		LeftB = EXTPaths[0].Y + RoundSize;

		RightPoint.Add(FVector(EXTPaths[0].X, EXTPaths[0].Y - RoundSize, EXTPaths[0].Z));

		RightB = EXTPaths[0].Y - RoundSize;

		IsUp = false;
	}
	else if (EXTPaths[0].Y - EXTPaths[1].Y == 0 && EXTPaths[0].X - EXTPaths[1].X > 0)
	{
		LineK = 0;

		LeftPoint.Add(FVector(EXTPaths[0].X, EXTPaths[0].Y - RoundSize, EXTPaths[0].Z));

		LeftB = EXTPaths[0].Y - RoundSize;

		RightPoint.Add(FVector(EXTPaths[0].X, EXTPaths[0].Y + RoundSize, EXTPaths[0].Z));

		RightB = EXTPaths[0].Y + RoundSize;

		IsUp = false;
	}
	else
	{
		IsUp = false;

		//不垂直X轴

		//当前两点的KB
		float K = (EXTPaths[0].Y - EXTPaths[1].Y) / (EXTPaths[0].X - EXTPaths[1].X);

		LineK = K;

		float B = EXTPaths[0].Y - K * EXTPaths[0].X;

		//垂直的KB
		float B1 = EXTPaths[0].Y + (1 / K * EXTPaths[0].X);

		if (EXTPaths[0].X < EXTPaths[1].X)
		{
			//左侧的KB
			LeftB = RoundSize * FMath::Sqrt(K * K + 1) + B;

			//右侧的KB
			RightB = B - RoundSize * FMath::Sqrt(K * K + 1);
		}
		else
		{
			//右侧的KB
			RightB = RoundSize * FMath::Sqrt(K * K + 1) + B;

			//左侧的KB
			LeftB = B - RoundSize * FMath::Sqrt(K * K + 1);
		}
		LeftPoint.Add(Getintersect(-1 / K, B1, K, LeftB, EXTPaths[0].Z));

		RightPoint.Add(Getintersect(-1 / K, B1, K, RightB, EXTPaths[0].Z));
	}
	///////计算2到n-1个点
	for (int32 i = 1; i < EXTPaths.Num() - 1; i++)
	{
		//if (FMath::Abs((EXTPaths[i] - EXTPaths[i + 1]).Size()) <= LineSize)
		//{
		//	continue;
		//}

		//都垂直，则不计算该点
		if (IsUp && EXTPaths[i].X - EXTPaths[i + 1].X == 0 && EXTPaths[i].Y - EXTPaths[i + 1].Y <= 0)
		{
			LeftPoint.Add(FVector(EXTPaths[i].X - RoundSize, EXTPaths[i].Y, EXTPaths[i].Z));

			RightPoint.Add(FVector(EXTPaths[i].X + RoundSize, EXTPaths[i].Y, EXTPaths[i].Z));

			continue;
		}
		if (IsUp && EXTPaths[i].X - EXTPaths[i + 1].X == 0 && EXTPaths[i].Y - EXTPaths[i + 1].Y > 0)
		{
			LeftPoint.Add(FVector(EXTPaths[i].X + RoundSize, EXTPaths[i].Y, EXTPaths[i].Z));

			RightPoint.Add(FVector(EXTPaths[i].X - RoundSize, EXTPaths[i].Y, EXTPaths[i].Z));

			continue;
		}
		if (LineK == 0 && EXTPaths[i].Y - EXTPaths[i + 1].Y == 0 && !IsUp && EXTPaths[i].X - EXTPaths[i + 1].X <= 0)
		{
			LeftPoint.Add(FVector(EXTPaths[i].X, EXTPaths[i].Y + RoundSize, EXTPaths[i].Z));

			RightPoint.Add(FVector(EXTPaths[i].X, EXTPaths[i].Y - RoundSize, EXTPaths[i].Z));

			continue;
		}
		if (LineK == 0 && EXTPaths[i].Y - EXTPaths[i + 1].Y == 0 && !IsUp && EXTPaths[i].X - EXTPaths[i + 1].X > 0)
		{
			LeftPoint.Add(FVector(EXTPaths[i].X, EXTPaths[i].Y - RoundSize, EXTPaths[i].Z));

			RightPoint.Add(FVector(EXTPaths[i].X, EXTPaths[i].Y + RoundSize, EXTPaths[i].Z));

			continue;
		}

		//第二条线为垂直X轴
		if (EXTPaths[i].X - EXTPaths[i + 1].X == 0 && EXTPaths[i].Y - EXTPaths[i + 1].Y <= 0)
		{
			LeftUp = EXTPaths[i].X - RoundSize;

			LeftPoint.Add(FVector(LeftUp, LineK * LeftUp + LeftB, EXTPaths[i].Z));

			RightUp = EXTPaths[i].X + RoundSize;

			RightPoint.Add(FVector(RightUp, LineK * RightUp + RightB, EXTPaths[i].Z));

			IsUp = true;

			continue;
		}
		else if (EXTPaths[i].X - EXTPaths[i + 1].X == 0 && EXTPaths[i].Y - EXTPaths[i + 1].Y >= 0)
		{
			LeftUp = EXTPaths[i].X + RoundSize;

			LeftPoint.Add(FVector(LeftUp, LineK * LeftUp + LeftB, EXTPaths[i].Z));

			RightUp = EXTPaths[i].X - RoundSize;

			RightPoint.Add(FVector(RightUp, LineK * RightUp + RightB, EXTPaths[i].Z));

			IsUp = true;

			continue;
		}
		//第二条线的KB
		float K2 = (EXTPaths[i].Y - EXTPaths[i + 1].Y) / (EXTPaths[i].X - EXTPaths[i + 1].X);
		float B2 = EXTPaths[i].Y - K2 * EXTPaths[i].X;

		float LeftB2, RightB2;

		if (EXTPaths[i].X < EXTPaths[i + 1].X)
		{
			//左侧的KB
			LeftB2 = RoundSize * FMath::Sqrt(K2 * K2 + 1) + B2;

			//右侧的KB
			RightB2 = B2 - RoundSize * FMath::Sqrt(K2 * K2 + 1);
		}
		else
		{
			//右侧的KB
			RightB2 = RoundSize * FMath::Sqrt(K2 * K2 + 1) + B2;

			//左侧的KB
			LeftB2 = B2 - RoundSize * FMath::Sqrt(K2 * K2 + 1);
		}
		if (IsUp)
		{
			LeftPoint.Add(FVector(LeftUp, K2 * LeftUp + LeftB2, EXTPaths[i].Z));

			RightPoint.Add(FVector(RightUp, K2 * RightUp + RightB2, EXTPaths[i].Z));

			IsUp = false;
			LineK = K2;
			LeftB = LeftB2;
			RightB = RightB2;

			continue;
		}

		if (FMath::Abs(LineK - K2) < 0.01)
		{
			if (LineK == 0)
			{
				continue;
			}
			else
			{
				float BZ = EXTPaths[i].Y + (1 / LineK) * EXTPaths[i].X;

				LeftPoint.Add(Getintersect(LineK, LeftB, -1 / LineK, BZ, EXTPaths[i].Z));

				RightPoint.Add(Getintersect(LineK, RightB, -1 / LineK, BZ, EXTPaths[i].Z));
			}
		}
		else
		{
			LeftPoint.Add(Getintersect(LineK, LeftB, K2, LeftB2, EXTPaths[i].Z));

			RightPoint.Add(Getintersect(LineK, RightB, K2, RightB2, EXTPaths[i].Z));
		}

		LineK = K2;
		LeftB = LeftB2;
		RightB = RightB2;
	}
	//计算最后一点

	//垂直X轴
	if (EXTPaths[EXTPaths.Num() - 2].X == EXTPaths[EXTPaths.Num() - 1].X && EXTPaths[EXTPaths.Num() - 2].Y - EXTPaths[EXTPaths.Num() - 1].Y <= 0)
	{
		LeftPoint.Add(FVector(EXTPaths[EXTPaths.Num() - 1].X - RoundSize, EXTPaths[EXTPaths.Num() - 1].Y, EXTPaths[EXTPaths.Num() - 1].Z));

		RightPoint.Add(FVector(EXTPaths[EXTPaths.Num() - 1].X + RoundSize, EXTPaths[EXTPaths.Num() - 1].Y, EXTPaths[EXTPaths.Num() - 1].Z));
	}
	else if (EXTPaths[EXTPaths.Num() - 2].X == EXTPaths[EXTPaths.Num() - 1].X && EXTPaths[EXTPaths.Num() - 2].Y - EXTPaths[EXTPaths.Num() - 1].Y > 0)
	{
		LeftPoint.Add(FVector(EXTPaths[EXTPaths.Num() - 1].X + RoundSize, EXTPaths[EXTPaths.Num() - 1].Y, EXTPaths[EXTPaths.Num() - 1].Z));

		RightPoint.Add(FVector(EXTPaths[EXTPaths.Num() - 1].X - RoundSize, EXTPaths[EXTPaths.Num() - 1].Y, EXTPaths[EXTPaths.Num() - 1].Z));
	}
	else if (EXTPaths[EXTPaths.Num() - 2].Y == EXTPaths[EXTPaths.Num() - 1].Y && EXTPaths[EXTPaths.Num() - 2].X - EXTPaths[EXTPaths.Num() - 1].X <= 0)
	{
		LeftPoint.Add(FVector(EXTPaths[EXTPaths.Num() - 1].X, EXTPaths[EXTPaths.Num() - 1].Y + RoundSize, EXTPaths[EXTPaths.Num() - 1].Z));

		RightPoint.Add(FVector(EXTPaths[EXTPaths.Num() - 1].X, EXTPaths[EXTPaths.Num() - 1].Y - RoundSize, EXTPaths[EXTPaths.Num() - 1].Z));
	}
	else if (EXTPaths[EXTPaths.Num() - 2].Y == EXTPaths[EXTPaths.Num() - 1].Y && EXTPaths[EXTPaths.Num() - 2].X - EXTPaths[EXTPaths.Num() - 1].X > 0)
	{
		LeftPoint.Add(FVector(EXTPaths[EXTPaths.Num() - 1].X, EXTPaths[EXTPaths.Num() - 1].Y - RoundSize, EXTPaths[EXTPaths.Num() - 1].Z));

		RightPoint.Add(FVector(EXTPaths[EXTPaths.Num() - 1].X, EXTPaths[EXTPaths.Num() - 1].Y + RoundSize, EXTPaths[EXTPaths.Num() - 1].Z));
	}
	else
	{
		//不垂直.平行3X轴,

		//当前两点的KB
		float KN = (EXTPaths[EXTPaths.Num() - 2].Y - EXTPaths[EXTPaths.Num() - 1].Y) / (EXTPaths[EXTPaths.Num() - 2].X - EXTPaths[EXTPaths.Num() - 1].X);

		float BN = EXTPaths[EXTPaths.Num() - 1].Y - KN * EXTPaths[EXTPaths.Num() - 1].X;

		//垂直的KB
		float BNZ = EXTPaths[EXTPaths.Num() - 1].Y + 1 / KN * EXTPaths[EXTPaths.Num() - 1].X;

		float LeftBN, RightBN;

		if (EXTPaths[EXTPaths.Num() - 2].X < EXTPaths[EXTPaths.Num() - 1].X)
		{
			//左侧的KB
			LeftBN = RoundSize * FMath::Sqrt(KN * KN + 1) + BN;

			//右侧的KB
			RightBN = BN - RoundSize * FMath::Sqrt(KN * KN + 1);
		}
		else
		{
			//右侧的KB
			RightBN = RoundSize * FMath::Sqrt(KN * KN + 1) + BN;

			//左侧的KB
			LeftBN = BN - RoundSize * FMath::Sqrt(KN * KN + 1);
		}
		LeftPoint.Add(Getintersect(-1 / KN, BNZ, KN, LeftBN, EXTPaths[EXTPaths.Num() - 1].Z));

		RightPoint.Add(Getintersect(-1 / KN, BNZ, KN, RightBN, EXTPaths[EXTPaths.Num() - 1].Z));
	}

	if (bCurve)
	{
		RightCurvePoint = CalcaulatePointCurvature(RightPoint, LeftPoint, RoundSize);
		LeftCurvePoint = CalcaulatePointCurvature(LeftPoint, RightPoint, RoundSize);
	}
	else
	{
		//RightCurvePoint = CalcaulatePoint(RightPoint, LeftPoint);
		//LeftCurvePoint = CalcaulatePoint(LeftPoint, RightPoint);
		RightCurvePoint = RightPoint;
		LeftCurvePoint = LeftPoint;


	}
}

void ATrackActor::DrawMesh()
{

//	if (EXTPaths.Num() > 0)
//	{
//		SplineComponent->SetWorldLocation(EXTPaths[0]);
//	}
	
	//ProcMeshComponent->SetRelativeLocation(FVector::ZeroVector);

	ClearSplineMesh(true);

	if (LeftCurvePoint.Num() != RightCurvePoint.Num() || LeftCurvePoint.Num() < 3)
	{
		return;
	}
	//计算周长
	float Primater = this->CalculatePrimater(LeftCurvePoint);
	
	float Primater1 = this->CalculatePrimater(RightCurvePoint);

	PointPrimater = (Primater + Primater1)/2;

	float CurrentUVLength = 0.f;

	float CurrentUVLength1 = 0.f;

	VertexArr.Add(FVector(LeftCurvePoint[0].X, LeftCurvePoint[0].Y, LeftCurvePoint[0].Z));
	VertexArr.Add(FVector(RightCurvePoint[0].X, RightCurvePoint[0].Y, RightCurvePoint[0].Z));

	UVArr.Add(FVector2D(0.f, 1.f));
	UVArr.Add(FVector2D(0.f, 0.f));

	for (int32 i = 1; i < LeftCurvePoint.Num(); i++)
	{
		VertexArr.Add(FVector(LeftCurvePoint[i].X, LeftCurvePoint[i].Y, LeftCurvePoint[i].Z));
		VertexArr.Add(FVector(RightCurvePoint[i].X, RightCurvePoint[i].Y, RightCurvePoint[i].Z));

		float PointDistance = FVector::Distance(LeftCurvePoint[i], RightCurvePoint[i]) - LineSize;

		if (!CurvrPointStart.Find(i))
		{
			CurrentUVLength = CurrentUVLength + FVector::Distance(LeftCurvePoint[i], LeftCurvePoint[i - 1]);
			//CurrentUVLength1 = CurrentUVLength1 + FVector::Distance(RightCurvePoint[i], RightCurvePoint[i - 1]);
			UVArr.Add(FVector2D(CurrentUVLength / Primater, 1.f));
			UVArr.Add(FVector2D(CurrentUVLength / Primater, 0.f));
		}
		else
		{
			float LeftDistance = FVector::Distance(LeftCurvePoint[i], LeftCurvePoint[i - 1]);
			float RightDistance = FVector::Distance(RightCurvePoint[i], RightCurvePoint[i - 1]);

			float Distance = (LeftDistance + RightDistance) / 2;

			CurrentUVLength = CurrentUVLength + Distance;

			Primater = Primater - LeftDistance + Distance;

			UVArr.Add(FVector2D(CurrentUVLength / Primater, 1.f));
			UVArr.Add(FVector2D(CurrentUVLength / Primater, 0.f));
		}
	}

	int32 NumX = LeftCurvePoint.Num();
	//计算三角面数
	UKismetProceduralMeshLibrary::CreateGridMeshTriangles(NumX, 2, true, TriangleArr);
	//计算法线和正切
	UKismetProceduralMeshLibrary::CalculateTangentsForMesh(VertexArr, TriangleArr, UVArr, NormalArr, TangentArr);



	//ProcMeshComponent = NewObject<UProceduralMeshComponent>(RootComponent);

	ProcMeshComponent->CreateMeshSection(0, VertexArr, TriangleArr, NormalArr, UVArr, TArray<FColor>(), TangentArr, true);
	ProcMeshComponent->RegisterComponent();
	ProcMeshComponent->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
	ProcMeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);



	FAttachmentTransformRules AttachmentRules(EAttachmentRule::KeepRelative, false);

	ProcMeshComponent->AttachToComponent(GetRootComponent(), AttachmentRules);

	float Value = (Primater*4) /( 3* LineSize);

	if (TrackMat)
	{
		TrackDynamicMat = UKismetMaterialLibrary::CreateDynamicMaterialInstance(GetWorld(), TrackMat, NAME_None);

		TrackDynamicMat->SetScalarParameterValue(FName("V"), Value);

		ProcMeshComponent->SetMaterial(0, TrackDynamicMat);
	}

}

float ATrackActor::CalculatePrimater(TArray<FVector> Points)
{
	float Primater = 0.f;
	int32 Length = Points.Num();
	for (int i = 0; i < Length; ++i)
	{
		int NextIndex = i + 1;
		if (NextIndex < Length)
		{
			Primater = Primater + FVector::Distance(Points[i], Points[NextIndex]);
		}
	}

	return Primater;
}

FVector ATrackActor::Getintersect(float K1, float B1, float K2, float B2, float Z)
{
	float X = (B1 - B2) / (K2 - K1);

	float Y = K1 * X + B1;

	return FVector(X, Y, Z);
}

FVector ATrackActor::GetDistanceForPoint(FVector PointStart, FVector PointEnd, float Distance)
{
	FVector DirectionVector = PointEnd - PointStart;

	float Dip = Distance / DirectionVector.Size();

	if (Dip > 1)
	{
		return PointStart;
	}

	return PointStart + DirectionVector * Dip;
}

void ATrackActor::GetCercleCenter(FVector Point1, FVector Point2, float K1, float K2, float B1, float B2, bool bNoK1, bool bNoK2, float& R, FVector2D& Center)
{
	float K11;
	float K22;
	float B11;
	float B22;

	if (bNoK1 && K2 == 0)
	{
		Center.X = Point2.X;
		Center.Y = Point1.Y;
		R = FVector::Distance(Point1, FVector(Center, Point2.Z));
	}
	else if (bNoK2 && K1 == 0)
	{
		Center.X = Point1.X;
		Center.Y = Point2.Y;
		R = FVector::Distance(Point2, FVector(Center, Point2.Z));
	}
	else if (bNoK1)
	{
		K11 = 0.0;
		B11 = Point1.Y;
		K22 = -1 / K2;
		B22 = Point2.Y - K22 * Point2.X;

		Center.X = (Point1.Y - B22) / K22;
		Center.Y = Point1.Y;
		R = FVector::Distance(Point1, FVector(Center, Point2.Z));
	}
	else if (bNoK2)
	{
		K22 = 0.0;
		B22 = Point2.Y;
		K11 = -1 / K1;
		B11 = Point1.Y - K11 * Point1.X;

		Center.X = (Point2.Y - B11) / K11;
		Center.Y = Point2.Y;
		R = FVector::Distance(Point1, FVector(Center, Point2.Z));
	}
	else if (K1 == 0)
	{
		K22 = -1 / K2;
		B22 = Point2.Y - K22 * Point2.X;

		Center.X = Point1.X;
		Center.Y = K22 * Point1.X + B22;
		R = FVector::Distance(Point1, FVector(Center, Point2.Z));
	}
	else if (K2 == 0)
	{
		K11 = -1 / K1;
		B11 = Point1.Y - K11 * Point1.X;

		Center.X = Point2.X;
		Center.Y = K11 * Point2.X + B11;
		R = FVector::Distance(Point1, FVector(Center, Point2.Z));
	}
	else
	{
		K11 = -1 / K1;
		B11 = Point1.Y - K11 * Point1.X;
		K22 = -1 / K2;
		B22 = Point2.Y - K22 * Point2.X;

		FVector Center1;
		Center1 = Getintersect(K11, B11, K22, B22, Point2.Z);
		Center = FVector2D(Center1.X, Center1.Y);
		R = FVector::Distance(Point1, Center1);
	}
}

FVector ATrackActor::GetAngle(FVector Point1, FVector Point2, float Radius, FVector CircleCenter)
{
	FVector Angle = FVector::ZeroVector;

	if ((Point1.X - CircleCenter.X == 0 && Point1.Y - CircleCenter.Y > 0) || Radius == 0)
	{
		Angle.X = PI / 2;
	}
	else if ((Point1.X - CircleCenter.X == 0 && Point1.Y - CircleCenter.Y < 0) || Radius == 0)
	{
		Angle.X = (-1) * PI / 2;
	}
	else if ((Point1.Y - CircleCenter.Y == 0 && Point1.X - CircleCenter.X > 0) || Radius == 0)
	{
		Angle.X = 0;
	}
	else if ((Point1.Y - CircleCenter.Y == 0 && Point1.X - CircleCenter.X < 0) || Radius == 0)
	{
		Angle.X = PI;
	}
	else if (Point1.X - CircleCenter.X < 0)
	{
		Angle.X = FMath::Atan((Point1.Y - CircleCenter.Y) / (Point1.X - CircleCenter.X)) + PI;
	}
	else
	{
		Angle.X = FMath::Atan((Point1.Y - CircleCenter.Y) / (Point1.X - CircleCenter.X));
	}
	if ((Point2.X - CircleCenter.X == 0 && Point2.Y - CircleCenter.Y > 0) || Radius == 0)
	{
		Angle.Y = PI / 2;
	}
	else if ((Point2.X - CircleCenter.X == 0 && Point2.Y - CircleCenter.Y < 0) || Radius == 0)
	{
		Angle.Y = (-1) * PI / 2;
	}
	else if ((Point2.Y - CircleCenter.Y == 0 && Point2.X - CircleCenter.X > 0) || Radius == 0)
	{
		Angle.Y = 0;
	}
	else if ((Point2.Y - CircleCenter.Y == 0 && Point2.X - CircleCenter.X < 0) || Radius == 0)
	{
		Angle.Y = PI;
	}
	else if (Point2.X - CircleCenter.X < 0)
	{
		Angle.Y = FMath::Atan((Point2.Y - CircleCenter.Y) / (Point2.X - CircleCenter.X)) + PI;
	}
	else
	{
		Angle.Y = FMath::Atan((Point2.Y - CircleCenter.Y) / (Point2.X - CircleCenter.X));
	}

	return Angle;
}

TArray<FVector> ATrackActor::CalcaulatePointCurvature(const TArray<FVector>& InPoints, const TArray<FVector>& InPoints1, float Size)
{
	float NewSize = Size;

	TArray<FVector> CurvePoints;

	if (InPoints.Num() < 3)
	{
		return TArray<FVector>();
	}

	CurvePoints.Add(InPoints[0]);

	FVector MidPoint = InPoints[0];

	FVector MidPoint1 = InPoints1[0];

	for (int32 i = 1; i < InPoints.Num() - 1; i++)
	{
		for (int k = 0; k <= 3; k++)
		{
			CurvrPointStart.Add(i + 1 + (i - 1) * CurvePointNum + k, true);
		}

		float Distance1 = FVector::Distance(MidPoint, InPoints[i]);
		float Distance2 = FVector::Distance(MidPoint1, InPoints1[i]);
		if (Distance1 > Distance2)
		{
			Size = (Distance1 - Distance2) + NewSize;
		}
		else
		{
			Size = NewSize;
		}

		FVector Point1 = GetDistanceForPoint(InPoints[i], InPoints[i - 1], Size);
		if (FVector::Distance(Point1, InPoints[i]) < 1)
		{
		//	break;
		}

		FVector Point2 = GetDistanceForPoint(InPoints[i], InPoints[i + 1], Size);
		if (FVector::Distance(Point2, InPoints[i]) < 1)
		{
		//	break;
		}
		//Point1,Point2分别为圆上两点

		////另外一种计算方式
		FVector2D CircleCenter;

		float R = Size;
		float K1=0.f;
		float K2 = 0.f;
		float B1 = 0.f;
		float B2 = 0.f;
		bool bNoK1 = false;
		bool bNoK2 = false;

		if(InPoints[i - 1].X - InPoints[i].X == 0)
		{
			bNoK1 = true;
		}
		else if (FMath::Abs(InPoints[i - 1].Y - InPoints[i].Y) <= 0.1)
		{
			K1 = 0;
			B1 = InPoints[i].Y;
		}
		else
		{
			K1 = (InPoints[i - 1].Y - InPoints[i].Y) / (InPoints[i - 1].X - InPoints[i].X);

			B1 = InPoints[i].Y - K1 * InPoints[i].X;
		}

		if(InPoints[i].X - InPoints[i + 1].X == 0)
		{
			bNoK2 = true;
		}
		else if (InPoints[i].Y - InPoints[i + 1].Y == 0)
		{
			K2 = 0;
			B2 = InPoints[i].X;
		}
		else
		{
			K2 = (InPoints[i].Y - InPoints[i + 1].Y) / (InPoints[i].X - InPoints[i + 1].X);

			B2 = InPoints[i].Y - K2 * InPoints[i].X;
		}

		GetCercleCenter(Point1, Point2, K1, K2, B1, B2, bNoK1, bNoK2, R, CircleCenter);

		FVector Angle = GetAngle(Point1, Point2, R, FVector(CircleCenter, InPoints[i].Z));
		if (Angle.X >= Angle.Y && Angle.X - Angle.Y <= PI)
		{
			for (double j = 0; j <= CurvePointNum; j++)
			{
				CurvePoints.Add(FVector(CircleCenter.X + R * FMath::Cos(Angle.X - (Angle.X - Angle.Y) * j * 0.1), CircleCenter.Y + R * FMath::Sin(Angle.X - (Angle.X - Angle.Y) * j * 0.1), InPoints[i].Z));
			}
		}
		else if (Angle.X >= Angle.Y && Angle.X - Angle.Y >= PI)
		{
			Angle.X = Angle.X - 2 * PI;

			for (double j = 0; j <= CurvePointNum; j++)
			{
				CurvePoints.Add(FVector(CircleCenter.X + R * FMath::Cos(Angle.X - (Angle.X - Angle.Y) * j * 0.1), CircleCenter.Y + R * FMath::Sin(Angle.X - (Angle.X - Angle.Y) * j * 0.1), InPoints[i].Z));
			}
		}
		else if (Angle.X <= Angle.Y && Angle.X - Angle.Y <= -PI)
		{
			Angle.X = Angle.X + 2 * PI;

			for (double j = 0; j <= CurvePointNum; j++)
			{
				CurvePoints.Add(FVector(CircleCenter.X + R * FMath::Cos(Angle.X - (Angle.X - Angle.Y) * j * 0.1), CircleCenter.Y + R * FMath::Sin(Angle.X - (Angle.X - Angle.Y) * j * 0.1), InPoints[i].Z));
			}
		}
		else if (Angle.X <= Angle.Y && Angle.X - Angle.Y >= -PI)
		{
			for (double j = 0; j <= CurvePointNum; j++)
			{
				CurvePoints.Add(FVector(CircleCenter.X + R * FMath::Cos(Angle.X - (Angle.X - Angle.Y) * j * 0.1), CircleCenter.Y + R * FMath::Sin(Angle.X - (Angle.X - Angle.Y) * j * 0.1), InPoints[i].Z));
			}
		}

		if (Distance1 > Distance2)
		{
			MidPoint = Point2;

			MidPoint1 = GetDistanceForPoint(InPoints1[i], InPoints1[i + 1], NewSize);;
		}
		else
		{
			MidPoint = Point2;

			MidPoint1 = GetDistanceForPoint(InPoints1[i], InPoints1[i + 1], NewSize + Distance2 - Distance1);
		}
	}
	CurvePoints.Add(InPoints[InPoints.Num() - 1]);

	return CurvePoints;
}

TArray<FVector> ATrackActor::CalcaulatePoint(const TArray<FVector>& InPoints, const TArray<FVector>& InPoints1 )
{
	TArray<FVector> CurvePoints;

	float Size;

	if (InPoints.Num() < 3)
	{
		return TArray<FVector>();
	}

	CurvePoints.Add(InPoints[0]);

	FVector MidPoint = InPoints[0];

	FVector MidPoint1 = InPoints1[0];

	for (int32 i = 1; i < InPoints.Num() - 1; i++)
	{
		for (int k = 0; k <= 8; k++)
		{
			CurvrPointStart.Add(i + 1 + (i - 1) * CurvePointNum + k, true);
		}
		float Distance1 = FVector::Distance(MidPoint, InPoints[i]);
		float Distance2 = FVector::Distance(MidPoint1, InPoints1[i]);
		if (Distance1 > Distance2)
		{
			Size = (Distance1 - Distance2);

			FVector Point1 = GetDistanceForPoint(InPoints[i], InPoints[i - 1], Size);
			if (FVector::Distance(Point1, InPoints[i]) < 1)
			{
				break;
			}

			FVector Point2 = GetDistanceForPoint(InPoints[i], InPoints[i + 1], Size);
			if (FVector::Distance(Point2, InPoints[i]) < 1)
			{
				break;
			}

			CurvePoints.Add(Point1);
			CurvePoints.Add(InPoints[i]);
			CurvePoints.Add(Point2);
			MidPoint = Point2;
			MidPoint1 = InPoints1[i];
		}
		else
		{
			Size = (Distance2 - Distance1);

			FVector Point2 = GetDistanceForPoint(InPoints1[i], InPoints[i + 1], Size);
			if (FVector::Distance(Point2, InPoints[i]) < 1)
			{
				break;
			}

			CurvePoints.Add(InPoints[i]);
			CurvePoints.Add(InPoints[i]);
			CurvePoints.Add(InPoints[i]);

			MidPoint = InPoints[i];
			MidPoint1 = Point2;
		}
	}
	CurvePoints.Add(InPoints[InPoints.Num() - 1]);

	return CurvePoints;
}

void ATrackActor::SetbCurve(bool bIsCurve)
{
	bCurve = bIsCurve;
}

void ATrackActor::SetPointsEXT(const TArray<FVector>& Paths)
{
	EXTPaths = Paths;
}

void ATrackActor::SetTypeEXT(const FString& NewTrackType)
{
    EXTTrackType = NewTrackType;
}

void ATrackActor::SetInterpEXT(const FString& NewInterp)
{
	EXTInterp = NewInterp;
}

void ATrackActor::SetSizeEXT(const float& NewSize)
{
	EXTSize = NewSize;

	RoundSize = NewSize*50;

	LineSize = RoundSize*2;
}

void ATrackActor::SetHighlightEXT(const bool& NewbHighLight)
{
	EXTbHighLight = NewbHighLight;
}

void ATrackActor::SetHighLightColorEXT(const FString& NewHighLightColor)
{
	EXTHighLightColor = NewHighLightColor;
}

