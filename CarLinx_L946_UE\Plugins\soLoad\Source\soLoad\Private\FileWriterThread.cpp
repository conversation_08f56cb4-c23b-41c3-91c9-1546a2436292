#include "FileWriterThread.h"

// UE4 Core headers
#include "CoreMinimal.h"
#include "HAL/PlatformFilemanager.h"
#include "HAL/PlatformProcess.h"
#include "HAL/FileManager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"

// Standard headers
#include <chrono>

// Project headers
#include "Logger.h"
#include "DataTypes.h"

FFileWriterThread::FFileWriterThread(const FString& FilePath)
    : ThreadFilePath(FilePath)
    , FileHandle(nullptr)
    , bFileOpened(false)
    , Thread(nullptr)
    , bRunning(false)
    , LastQueueSize(0)
    , LastCheckTime(0)
    , QueueGrowthRate(0)
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Constructor Begin"), this);
    Start();
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Constructor End"), this);
}

FFileWriterThread::~FFileWriterThread()
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Begin"), this);
    
    // 先停止线程
    Stop();
    
    // 等待线程完全停止
    if (Thread)
    {
        Thread->Kill(true);
        delete Thread;
        Thread = nullptr;
    }
    
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] End"), this);
}

bool FFileWriterThread::Init()
{
    return OpenFile(ThreadFilePath);
}

uint32 FFileWriterThread::Run()
{
    while (bRunning)
    {
        ProcessQueuedData();
        // 减少休眠时间到500ms，提高处理频率
        FPlatformProcess::Sleep(0.5f);
    }
    return 0;
}

void FFileWriterThread::Stop()
{
    FScopeLock Lock(&CriticalSection);
    
    if (!IsRunning())
    {
        return;
    }

    CloseFile();
    bRunning = false;
}

void FFileWriterThread::Exit()
{
    CloseFile();
}

void FFileWriterThread::MonitorQueuePerformance()
{
    int32 CurrentQueueSize = PendingDataCount.GetValue();
    double CurrentTime = FPlatformTime::Seconds();
    
    // 每秒更新一次性能统计
    if (CurrentTime - LastCheckTime >= 1.0)
    {
        // 计算写入帧率
        int32 CurrentWrittenFrames = TotalFramesWritten.GetValue();
        double WriteRate = static_cast<double>(TotalFramesWritten.GetValue() - LastQueueSize);  // 直接用差值作为每秒写入帧数
        
        // 记录当前状态用于下次计算
        LastQueueSize = TotalFramesWritten.GetValue();
        LastCheckTime = CurrentTime;

        // 队列大小超过警告阈值时输出日志
        if (CurrentQueueSize > WARNING_QUEUE_SIZE)
        {
            CARLINX_LOG(LogCarLinx, Warning, TEXT("Queue size warning - Pending frames: %d"), CurrentQueueSize);
        }
        
        // 输出详细的性能信息
        CARLINX_LOG(LogCarLinx, Log, TEXT("Queue performance stats - Pending frames: %d, Write rate: %.2f frames/sec, Total written: %d, Total received: %d"), 
            CurrentQueueSize,
            WriteRate,
            CurrentWrittenFrames,
            TotalFramesReceived.GetValue());
    }
}

void FFileWriterThread::EnqueueData(const FParkingDataFrame& Frame)
{
    FScopeLock Lock(&CriticalSection);
    
    // 调用性能监控函数
    MonitorQueuePerformance();

    if (PendingDataCount.GetValue() >= MAX_QUEUE_SIZE)
    {
        CARLINX_LOG(LogCarLinx, Warning, TEXT("Queue full, dropping frame"));
        return;
    }

    // 将帧数据加入队列
    DataQueue.Enqueue(Frame);
    TotalFramesReceived.Increment();
    PendingDataCount.Increment();

    // 输出调试信息
    CARLINX_LOG(LogCarLinx, Verbose, TEXT("Enqueued frame - Size: %u, Queue Size: %d"), 
        Frame.Size, PendingDataCount.GetValue());
}

void FFileWriterThread::UpdateHeader(uint64_t frameCount, int64_t endTime, int64_t startTime)
{
    FScopeLock Lock(&CriticalSection);
    
    // 只更新提供的值
    if (frameCount > 0) CurrentHeader.frameCount = frameCount;
    if (endTime > 0) CurrentHeader.endTime = endTime;
    if (startTime > 0) CurrentHeader.startTime = startTime;
    
    bHeaderNeedsUpdate = true;
}

bool FFileWriterThread::OpenFile(const FString& InFilePath)
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("Opening file: %s"), *InFilePath);

    // 检查文件路径是否为空
    if (InFilePath.IsEmpty())
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("File path is empty"));
        return false;
    }

    // 获取完整的目录路径
    FString DirectoryPath = FPaths::GetPath(InFilePath);
    
    // 分解目录路径为各个层级
    TArray<FString> PathParts;
    DirectoryPath.ParseIntoArray(PathParts, TEXT("/"));
    
    // 从根目录开始逐级检查
    FString CurrentPath;
    for (int32 i = 0; i < PathParts.Num(); ++i)
    {
        if (i == 0 && PathParts[i].IsEmpty())
        {
            CurrentPath = TEXT("/");
            continue;
        }
        
        CurrentPath = FPaths::Combine(CurrentPath, PathParts[i]);
        
        // 检查目录是否存在
        if (!IFileManager::Get().DirectoryExists(*CurrentPath))
        {
            CARLINX_LOG(LogCarLinx, Warning, TEXT("Directory does not exist: %s"), *CurrentPath);
            
            // 尝试创建目录
            bool bCreated = IFileManager::Get().MakeDirectory(*CurrentPath, true);
            if (!bCreated)
            {
                CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to create directory: %s"), *CurrentPath);
                return false;
            }
            CARLINX_LOG(LogCarLinx, Log, TEXT("Successfully created directory: %s"), *CurrentPath);
        }
        
        // 检查目录权限
        if (!FPaths::DirectoryExists(CurrentPath))
        {
            CARLINX_LOG(LogCarLinx, Error, TEXT("Directory is not accessible: %s"), *CurrentPath);
            return false;
        }
    }

    // 尝试打开文件
#if PLATFORM_WINDOWS
    errno_t err = fopen_s(&FileHandle, TCHAR_TO_UTF8(*InFilePath), "wb");
    if (err != 0 || !FileHandle)
#else
    FileHandle = fopen(TCHAR_TO_UTF8(*InFilePath), "wb");
    if (!FileHandle)
#endif
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to create file: %s"), *InFilePath);
        return false;
    }

    // 初始化文件头
    CurrentHeader = FParkingDataFileHeader();
    CurrentHeader.magic = PARKING_FILE_MAGIC_NUMBER;
    CurrentHeader.version = PARKING_DATA_VERSION;
    CurrentHeader.frameCount = 0;
    bHeaderNeedsUpdate = true;

    bFileOpened = true;
    CARLINX_LOG(LogCarLinx, Log, TEXT("Successfully opened file: %s"), *InFilePath);
    return true;
}

void FFileWriterThread::CloseFile()
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Begin"), this);
    if (FileHandle)
    {
        // 确保最后一次更新文件头
        if (bHeaderNeedsUpdate)
        {
            WriteHeader();
        }
        fflush(FileHandle);
        fclose(FileHandle);
        FileHandle = nullptr;
        bFileOpened = false;

        // 清空性能监控数据
        LastQueueSize = 0;
        LastCheckTime = 0;
        QueueGrowthRate = 0;
        TotalFramesReceived.Set(0);
        TotalFramesWritten.Set(0);
        PendingDataCount.Set(0);
    }
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] End"), this);
}

bool FFileWriterThread::WriteHeader()
{
    if (!FileHandle)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[WriteHeader] File handle is invalid"));
        return false;
    }

    CARLINX_LOG(LogCarLinx, Log, TEXT("[WriteHeader] Begin - Current Header: Magic=0x%08X, Version=%u, Frames=%llu"), 
        CurrentHeader.magic, CurrentHeader.version, CurrentHeader.frameCount);

    long CurrentPos = ftell(FileHandle);
    if (fseek(FileHandle, 0, SEEK_SET) != 0)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[WriteHeader] Failed to seek to file start from position %ld"), CurrentPos);
        return false;
    }

    size_t bytesWritten = fwrite(&CurrentHeader, sizeof(CurrentHeader), 1, FileHandle);
    if (bytesWritten != 1)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[WriteHeader] Failed to write header - Expected: 1, Written: %u"), bytesWritten);
        return false;
    }

    if (fseek(FileHandle, CurrentPos, SEEK_SET) != 0)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[WriteHeader] Failed to restore file position to %ld"), CurrentPos);
        return false;
    }

    bHeaderNeedsUpdate = false;
    CARLINX_LOG(LogCarLinx, Log, TEXT("[WriteHeader] Successfully updated header"));
    return true;
}

void FFileWriterThread::ProcessQueuedData()
{
    if (!FileHandle || !bFileOpened)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("File handle is invalid or file not opened"));
        return;
    }

    if (PendingDataCount.GetValue() <= 0)
    {
        return;
    }

    CARLINX_LOG(LogCarLinx, Log, TEXT("[ProcessQueuedData] Begin - Queue Size: %d"), PendingDataCount.GetValue());

    const int32 MAX_BATCH_SIZE = 30; // 每次处理最多30帧数据
    int32 ProcessedCount = 0;
    bool bNeedsFlush = false;

    while (ProcessedCount < MAX_BATCH_SIZE && FileHandle && bFileOpened)
    {
        FParkingDataFrame Frame;
        {
            FScopeLock Lock(&CriticalSection);
            if (!DataQueue.Dequeue(Frame))
            {
                break;
            }
        }

        // 检查Frame数据有效性
        if (Frame.Size == 0 || !Frame.Data.GetData())
        {
            CARLINX_LOG(LogCarLinx, Warning, TEXT("[ProcessQueuedData] Invalid frame data"));
            PendingDataCount.Decrement();
            continue;
        }

        // 移动到文件尾部
        if (fseek(FileHandle, 0, SEEK_END) != 0)
        {
            CARLINX_LOG(LogCarLinx, Error, TEXT("[ProcessQueuedData] Failed to seek to file end"));
            break;
        }

        bool bWriteSuccess = true;
        
        // 写入魔数
        bWriteSuccess &= (fwrite(&Frame.magic, sizeof(Frame.magic), 1, FileHandle) == 1);
        
        // 写入时间戳
        bWriteSuccess &= (fwrite(&Frame.Timestamp, sizeof(Frame.Timestamp), 1, FileHandle) == 1);
        
        // 写入数据大小
        bWriteSuccess &= (fwrite(&Frame.Size, sizeof(Frame.Size), 1, FileHandle) == 1);
        
        // 写入实际数据
        bWriteSuccess &= (fwrite(Frame.Data.GetData(), 1, Frame.Size, FileHandle) == Frame.Size);

        if (!bWriteSuccess)
        {
            CARLINX_LOG(LogCarLinx, Error, TEXT("[ProcessQueuedData] Failed to write frame data"));
            break;
        }

        TotalFramesWritten.Increment();
        PendingDataCount.Decrement();
        ProcessedCount++;
        bNeedsFlush = true;

        CARLINX_LOG(LogCarLinx, Verbose, TEXT("[ProcessQueuedData] Written frame - Magic: 0x%08X, Size: %u"), 
            Frame.magic, Frame.Size);
    }

    // 如有需要则更新文件头
    if (bHeaderNeedsUpdate && FileHandle && bFileOpened)
    {
        if (!WriteHeader())
        {
            CARLINX_LOG(LogCarLinx, Error, TEXT("[ProcessQueuedData] Failed to update header"));
        }
    }

    // 批量处理完成后刷新缓冲区
    if (bNeedsFlush && FileHandle)
    {
        fflush(FileHandle);

        // 输出写入状态日志
        CARLINX_LOG(LogCarLinx, Log, TEXT("[ProcessQueuedData] End - Processed: %d, Total Written: %d, Pending: %d"), 
            ProcessedCount,
            TotalFramesWritten.GetValue(), 
            PendingDataCount.GetValue());
    }
}

bool FFileWriterThread::UpdateFilePath(const FString& NewFilePath)
{
    FScopeLock Lock(&CriticalSection);
    
    // 关闭当前文件
    CloseFile();
    
    // 更新文件路径
    ThreadFilePath = NewFilePath;
    CurrentFilePath = NewFilePath;

    // 打开新文件
    return OpenFile(NewFilePath);
}

bool FFileWriterThread::Start()
{
    FScopeLock Lock(&CriticalSection);
    
    if (IsRunning())
    {
        return true;
    }

    bRunning = true;
    Thread = FRunnableThread::Create(this, TEXT("FileWriterThread"));
    
    return Thread != nullptr;
}

bool FFileWriterThread::Restart()
{
    FScopeLock Lock(&CriticalSection);
    
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Restart() Begin"), this);
    
    // 先停止当前线程
    Stop();
    
    // 等待线程完全停止
    while (Thread != nullptr)
    {
        FPlatformProcess::Sleep(0.01f);
    }
    
    // 清空队列
    DataQueue.Empty();
    
    // 重新打开文件
    if (!OpenFile(ThreadFilePath))
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to reopen file"));
        return false;
    }
    
    // 重新启动线程
    return Start();
}

void FFileWriterThread::InitHeader(int64_t startTime)
{
    FScopeLock Lock(&CriticalSection);
    
    // 初始化文件头
    CurrentHeader = FParkingDataFileHeader();
    CurrentHeader.magic = PARKING_FILE_MAGIC_NUMBER;
    CurrentHeader.version = PARKING_DATA_VERSION;
    CurrentHeader.frameCount = 0;
    CurrentHeader.startTime = startTime;
    CurrentHeader.endTime = startTime;
    
    bHeaderNeedsUpdate = true;
    
    // 立即写入文件头
    WriteHeader();
}