#include "Subscriber.h"






const TMap<FString, EVehicleEventType> ASubscriber::NameToEnumMap = {
	{TEXT("System_Change"), EVehicleEventType::System_Change},
	{TEXT("GearType"), EVehicleEventType::GearType},
	{TEXT("DayNightMode"), EVehicleEventType::DayNightMode},
	{TEXT("PDCVolume"), EVehicleEventType::PDCVolume},
	{TEXT("RCTAWarning"), EVehicleEventType::RCTAWarning},
	{TEXT("Widget_Finish"), EVehicleEventType::Widget_Finish},
	{TEXT("PDCVolume_Sts"), EVehicleEventType::PDCVolume_Sts},
	{TEXT("DebugShow"), EVehicleEventType::DebugShow},
	{TEXT("CameraSts"), EVehicleEventType::CameraSts},
	{TEXT("RPAWarn"), EVehicleEventType::RPAWarn},

	// ���ֺͳ���״̬
	{TEXT("RWheelRotateDir"), EVehicleEventType::RWheelRotateDir},
	{TEXT("FWheelRotateDir"), EVehicleEventType::FWheelRotateDir},
	{TEXT("FWheelRotateSpeed"), EVehicleEventType::FWheelRotateSpeed},
	{TEXT("RWheelRotateSpeed"), EVehicleEventType::RWheelRotateSpeed},
	{TEXT("WhlRotToothCntr"), EVehicleEventType::WhlRotToothCntr},
	{TEXT("DrvDoorSts"), EVehicleEventType::DrvDoorSts},
	{TEXT("PassDoorSts"), EVehicleEventType::PassDoorSts},
	{TEXT("LeReDoorSts"), EVehicleEventType::LeReDoorSts},
	{TEXT("RiReDoorSts"), EVehicleEventType::RiReDoorSts},
	{TEXT("HoodSts"), EVehicleEventType::HoodSts},
	{TEXT("TrunkSts"), EVehicleEventType::TrunkSts},

	// �������촰
	{TEXT("DrvWinPer"), EVehicleEventType::DrvWinPer},
	{TEXT("PassWinPer"), EVehicleEventType::PassWinPer},
	{TEXT("LeReWinPer"), EVehicleEventType::LeReWinPer},
	{TEXT("RiReWinPer"), EVehicleEventType::RiReWinPer},
	{TEXT("SunroofPer"), EVehicleEventType::SunroofPer},

	// ���Ӿ�״̬
	{TEXT("DrvMirrSts"), EVehicleEventType::DrvMirrSts},
	{TEXT("PassMirrSts"), EVehicleEventType::PassMirrSts},

	// �ƹ������״�?
	{TEXT("LowBeam"), EVehicleEventType::LowBeam},
	{TEXT("HighBeam"), EVehicleEventType::HighBeam},
	{TEXT("TurnLeftLightSts"), EVehicleEventType::TurnLeftLightSts},
	{TEXT("TurnRightLightSts"), EVehicleEventType::TurnRightLightSts},
	{TEXT("DoubleFlash"), EVehicleEventType::DoubleFlash},
	{TEXT("BreakLightSts"), EVehicleEventType::BreakLightSts},
	{TEXT("DrvSeatfoldSts"), EVehicleEventType::DrvSeatfoldSts},

	// �����˶�״̬
	{TEXT("CarRoll"), EVehicleEventType::CarRoll},
	{TEXT("CarPitch"), EVehicleEventType::CarPitch},
	{TEXT("CarYaw"), EVehicleEventType::CarYaw},
	{TEXT("DrivePosition"), EVehicleEventType::DrivePosition},
	{TEXT("CarSpeed"), EVehicleEventType::CarSpeed},
	{TEXT("SteeringAngle"), EVehicleEventType::SteeringAngle},
	{TEXT("APA_ParkInSurplusDistance"), EVehicleEventType::APA_ParkInSurplusDistance},
	
	

	// AVM���?
	{TEXT("Avm5sTimer"), EVehicleEventType::Avm5sTimer},
	{TEXT("AvmWanringA0"), EVehicleEventType::AvmWanringA0},
	{TEXT("AvmWanringA1"), EVehicleEventType::AvmWanringA1},
	{TEXT("AvmWanringA2"), EVehicleEventType::AvmWanringA2},
	{TEXT("AvmWanringA3"), EVehicleEventType::AvmWanringA3},
	{TEXT("AvmWanringB1"), EVehicleEventType::AvmWanringB1},
	{TEXT("AvmWanringB2"), EVehicleEventType::AvmWanringB2},
	{TEXT("AvmWanringB3"), EVehicleEventType::AvmWanringB3},
	{TEXT("AvmWanringB4"), EVehicleEventType::AvmWanringB4},
	{TEXT("AvmTrunkWarning"), EVehicleEventType::AvmTrunkWarning},
	{TEXT("AvmCurrViewType"), EVehicleEventType::AvmCurrViewType},
	{TEXT("AvmViewTwoD"), EVehicleEventType::AvmViewTwoD},
	{TEXT("AvmViewThreeD"), EVehicleEventType::AvmViewThreeD},
	{TEXT("AvmSettingPage"), EVehicleEventType::AvmSettingPage},
	{TEXT("AvmTransparentCar"), EVehicleEventType::AvmTransparentCar},
	{TEXT("AvmSteeringlinkage"), EVehicleEventType::AvmSteeringlinkage},
	{TEXT("AvmNarrowActive"), EVehicleEventType::AvmNarrowActive},
	{TEXT("AvmFactoryStatus"), EVehicleEventType::AvmFactoryStatus},
	{TEXT("AvmVersionNum"), EVehicleEventType::AvmVersionNum},
	{TEXT("AvmCalib"), EVehicleEventType::AvmCalib},
	{TEXT("AvmCalibProgress"), EVehicleEventType::AvmCalibProgress},
	{TEXT("AvmCamFaultFront"), EVehicleEventType::AvmCamFaultFront},
	{TEXT("AvmCamFaultRear"), EVehicleEventType::AvmCamFaultRear},
	{TEXT("AvmCamFaultLeft"), EVehicleEventType::AvmCamFaultLeft},
	{TEXT("AvmCamFaultRight"), EVehicleEventType::AvmCamFaultRight},

	// APA���?
	{TEXT("APABtnStatus"), EVehicleEventType::APABtnStatus},
	{TEXT("APA_Quit"), EVehicleEventType::APA_Quit},
	{TEXT("APA_RadarMute"), EVehicleEventType::APA_RadarMute},
	{TEXT("APA_To360"), EVehicleEventType::APA_To360},
	{TEXT("APA_ViewSwitching"), EVehicleEventType::APA_ViewSwitching},
	{TEXT("APA_ParkinReq"), EVehicleEventType::APA_ParkinReq},
	{TEXT("APA_ParkoutReq"), EVehicleEventType::APA_ParkoutReq},
	{TEXT("APA_Setting"), EVehicleEventType::APA_Setting},
	{TEXT("APA_ContPark"), EVehicleEventType::APA_ContPark},
	{TEXT("APA_ParkingWithoutFeeling"), EVehicleEventType::APA_ParkingWithoutFeeling},
	{TEXT("APA_LanguageBroadcast"), EVehicleEventType::APA_LanguageBroadcast},
	{TEXT("APA_BroadcastMode"), EVehicleEventType::APA_BroadcastMode},
	{TEXT("APA_SRViewChange"), EVehicleEventType::APA_SRViewChange},
	{TEXT("APA_CenterIn"), EVehicleEventType::APA_CenterIn},
	{TEXT("APA_LeftIn"), EVehicleEventType::APA_LeftIn},
	{TEXT("APA_RightIn"), EVehicleEventType::APA_RightIn},
	{TEXT("APA_LeftOut"), EVehicleEventType::APA_LeftOut},
	{TEXT("APA_RightOut"), EVehicleEventType::APA_RightOut},
	{TEXT("APA_StartParkOut"), EVehicleEventType::APA_StartParkOut},
	{TEXT("APA_WorkSts"), EVehicleEventType::APA_WorkSts},
	{TEXT("APA_TouchX"), EVehicleEventType::APA_TouchX},
	{TEXT("APA_TouchY"), EVehicleEventType::APA_TouchY},
	{TEXT("APA_FingerIndex"), EVehicleEventType::APA_FingerIndex},
	{ TEXT("APA_TouchEnvTyp"), EVehicleEventType::APA_TouchEnvTyp },
	{ TEXT("APA_Quit_Stat"), EVehicleEventType::APA_Quit_Stat },
	{ TEXT("APA_RadarMute_Stat"), EVehicleEventType::APA_RadarMute_Stat },
	{ TEXT("APA_To360_Stat"), EVehicleEventType::APA_To360_Stat },
	{ TEXT("APA_ViewSwitching_Stat"), EVehicleEventType::APA_ViewSwitching_Stat },

	{ TEXT("APA_ParkinReq_Stat"), EVehicleEventType::APA_ParkinReq_Stat },
	{ TEXT("APA_ParkoutReq_Stat"), EVehicleEventType::APA_ParkoutReq_Stat },
	{ TEXT("APA_Setting_Stat"), EVehicleEventType::APA_Setting_Stat },
	{ TEXT("APA_SettingBtnSts_Stat"), EVehicleEventType::APA_SettingBtnSts_Stat },
	{ TEXT("APA_ContPark_Stat"), EVehicleEventType::APA_ContPark_Stat },
	{ TEXT("APA_ViewID_Stat"), EVehicleEventType::APA_ViewID_Stat },
	{ TEXT("APA_ParkingWithoutFeeling_Stat"), EVehicleEventType::APA_ParkingWithoutFeeling_Stat },
	{ TEXT("APA_LanguageBroadcast_Stat"), EVehicleEventType::APA_LanguageBroadcast_Stat },
	{ TEXT("APA_BroadcastMode_Stat"), EVehicleEventType::APA_BroadcastMode_Stat },
	{ TEXT("APA_SRViewChange_Stat"), EVehicleEventType::APA_SRViewChange_Stat },
	{ TEXT("APA_CenterIn_Stat"), EVehicleEventType::APA_CenterIn_Stat },
	{ TEXT("APA_LeftIn_Stat"), EVehicleEventType::APA_LeftIn_Stat },
	{ TEXT("APA_RightIn_Stat"), EVehicleEventType::APA_RightIn_Stat },
	{ TEXT("APA_LeftOut_Stat"), EVehicleEventType::APA_LeftOut_Stat },
	{ TEXT("APA_RightOut_Stat"), EVehicleEventType::APA_RightOut_Stat },
	{ TEXT("APA_StartParkOut_Stat"), EVehicleEventType::APA_StartParkOut_Stat },

	{ TEXT("APA_StrtPrkg_Stat"), EVehicleEventType::APA_StrtPrkg_Stat },

	{ TEXT("APA_PerFrontLeftOut_Stat"), EVehicleEventType::APA_PerFrontLeftOut_Stat },
	{ TEXT("APA_PerFrontRightOut_Stat"), EVehicleEventType::APA_PerFrontRightOut_Stat },
	{ TEXT("APA_PerBackLeftOut_Stat"), EVehicleEventType::APA_PerBackLeftOut_Stat },
	{ TEXT("APA_PerBackRightOut_Stat"), EVehicleEventType::APA_PerBackRightOut_Stat },

	{ TEXT("APA_ParkingPercent"), EVehicleEventType::APA_ParkingPercent },
	{ TEXT("APA_ParkOutInterface_Stat"), EVehicleEventType::APA_ParkOutInterface_Stat },
	{ TEXT("APA_WarningID"), EVehicleEventType::APA_WarningID },
	{ TEXT("APA_WarningAID"), EVehicleEventType::APA_WarningAID },
	{ TEXT("APA_ParkInSurplusDistance_Stat"), EVehicleEventType::APA_ParkInSurplusDistance_Stat },
	{ TEXT("DrvrAsscSysSts"), EVehicleEventType::DrvrAsscSysSts },

	{ TEXT("APA_FrntAndReSwt_Stat"), EVehicleEventType::APA_FrntAndReSwt_Stat },
	{ TEXT("APA_ViewSwitching_BtnStat"), EVehicleEventType::APA_ViewSwitching_BtnStat },
	{ TEXT("APA_FrntAndReSwt"), EVehicleEventType::APA_FrntAndReSwt },

	//RPA
	{ TEXT("RPA_CarInPark_Stat"), EVehicleEventType::RPA_CarInPark_Stat },
	{ TEXT("RPA_RemCtrlPrkg_Stat"), EVehicleEventType::RPA_RemCtrlPrkg_Stat },
	{ TEXT("PrkgAssiSysRemPrkgSts"), EVehicleEventType::PrkgAssiSysRemPrkgSts },
	

	{ TEXT("HPASettingPage"), EVehicleEventType::HPASettingPage },
	{ TEXT("SmarkGeneration"), EVehicleEventType::SmarkGeneration },
	{ TEXT("FuncPush"), EVehicleEventType::FuncPush },
	{ TEXT("HPAVoiceSts"), EVehicleEventType::HPAVoiceSts },
	{ TEXT("MyRouteSts"), EVehicleEventType::MyRouteSts },
	{ TEXT("ParkInRouteNum"), EVehicleEventType::ParkInRouteNum },
	{ TEXT("ParkOutRouteNum"), EVehicleEventType::ParkOutRouteNum },
	{ TEXT("BuildDistance"), EVehicleEventType::BuildDistance },
	{ TEXT("RampNum"), EVehicleEventType::RampNum },
	{ TEXT("DecZoneNum"), EVehicleEventType::DecZoneNum },

	{ TEXT("ParkInDisLeft"), EVehicleEventType::ParkInDisLeft },

	{ TEXT("CuriseTimeLeft"), EVehicleEventType::CuriseTimeLeft },
	{ TEXT("AvoidPersons"), EVehicleEventType::AvoidPersons },
	{ TEXT("AvoidCars"), EVehicleEventType::AvoidCars },
	//{ TEXT("DistanceToEnd"), EVehicleEventType::DistanceToEnd },
	{ TEXT("TotalCurisingTime"), EVehicleEventType::TotalCurisingTime },
	{ TEXT("FirstParkMapTime"), EVehicleEventType::FirstParkMapTime },
	{ TEXT("FirstParkMapDistance"), EVehicleEventType::FirstParkMapDistance },
	{ TEXT("FirstParkMapSts"), EVehicleEventType::FirstParkMapSts },
	{ TEXT("Recentlyused"), EVehicleEventType::Recentlyused },
	{ TEXT("FirstParkPhoneDis"), EVehicleEventType::FirstParkPhoneDis },
	{ TEXT("SecParkMapTime"), EVehicleEventType::SecParkMapTime },
	{ TEXT("SecParkMapDistance"), EVehicleEventType::SecParkMapDistance },
	{ TEXT("SecParkMapSts"), EVehicleEventType::SecParkMapSts },
	{ TEXT("SecParkPhoneDis"), EVehicleEventType::SecParkPhoneDis },
	{ TEXT("HPASts"), EVehicleEventType::HPASts },
	{ TEXT("PhoneCtrlSts"), EVehicleEventType::PhoneCtrlSts },
	{ TEXT("HPASumPage"), EVehicleEventType::HPASumPage },
	{ TEXT("HPARecoverBtn"), EVehicleEventType::HPARecoverBtn },
	{ TEXT("HPAFinishBtn"), EVehicleEventType::HPAFinishBtn },
	{ TEXT("BtnSettingSts"), EVehicleEventType::BtnSettingSts },
	{ TEXT("BtnExitSts"), EVehicleEventType::BtnExitSts },
	{ TEXT("BtnParkInSts"), EVehicleEventType::BtnParkInSts },
	{ TEXT("BtnParkOutSts"), EVehicleEventType::BtnParkOutSts },
	{ TEXT("HPABtnMuteSts"), EVehicleEventType::HPABtnMuteSts },
	{ TEXT("HPAPDCVolume"), EVehicleEventType::HPAPDCVolume },
	{ TEXT("ParkInRouteCover"), EVehicleEventType::ParkInRouteCover },
	{ TEXT("ParkOutRouteCover"), EVehicleEventType::ParkOutRouteCover },
	{ TEXT("RouteSelectionVisibility"), EVehicleEventType::RouteSelectionVisibility },
	{ TEXT("StartMemoParking"), EVehicleEventType::StartMemoParking },
	{ TEXT("BuildRouteCard"), EVehicleEventType::BuildRouteCard },
	{ TEXT("ForwardRouteCard"), EVehicleEventType::ForwardRouteCard },
	{ TEXT("ParkInRouteOver"), EVehicleEventType::ParkInRouteOver },
	{ TEXT("ParkOutRouteOver"), EVehicleEventType::ParkOutRouteOver },
	{ TEXT("HPACurentViewType"), EVehicleEventType::HPACurentViewType },
	{ TEXT("HPAWorkingSts"), EVehicleEventType::HPAWorkingSts },
	{ TEXT("HPACarport"), EVehicleEventType::HPACarport },
	{ TEXT("HPATargetVehicle"), EVehicleEventType::HPATargetVehicle },
	{ TEXT("HPADrawRoad"), EVehicleEventType::HPADrawRoad },
	{ TEXT("HPAWarn"), EVehicleEventType::HPAWarn },
	{ TEXT("HPASmallWarn"), EVehicleEventType::HPASmallWarn },
	{ TEXT("HPASystemWarn"), EVehicleEventType::HPASystemWarn },
	{ TEXT("HPASystemWarnType"), EVehicleEventType::HPASystemWarnType },
	{ TEXT("LSDAWarn"), EVehicleEventType::LSDAWarn },
	{ TEXT("HPAViewName"), EVehicleEventType::HPAViewName },
	{ TEXT("GlobalViewBtn"), EVehicleEventType::GlobalViewBtn },
	{ TEXT("PartViewBtn"), EVehicleEventType::PartViewBtn },
	{ TEXT("ValtPrkgViewDisp"), EVehicleEventType::ValtPrkgViewDisp },
	//PDC
	{ TEXT("RML_RadarStatus"), EVehicleEventType::RML_RadarStatus },
	{ TEXT("RMR_RadarStatus"), EVehicleEventType::RMR_RadarStatus },
	{ TEXT("RL_RardarStatus"), EVehicleEventType::RL_RardarStatus },
	{ TEXT("RR_RadarStatus"), EVehicleEventType::RR_RadarStatus },
	{ TEXT("FL_RadarStatus"), EVehicleEventType::FL_RadarStatus },
	{ TEXT("FR_RadarStatus"), EVehicleEventType::FR_RadarStatus },
	{ TEXT("FML_RadarStatus"), EVehicleEventType::FML_RadarStatus },
	{ TEXT("FMR_RadarStatus"), EVehicleEventType::FMR_RadarStatus },
	{ TEXT("LFSide_RadarStatus"), EVehicleEventType::LFSide_RadarStatus },
	{ TEXT("RFSide_RadarStatus"), EVehicleEventType::RFSide_RadarStatus },
	{ TEXT("LRSide_RadarStatus"), EVehicleEventType::LRSide_RadarStatus },
	{ TEXT("RRSide_RadarStatus"), EVehicleEventType::RRSide_RadarStatus },
	{ TEXT("AvmFrontRadarNotise"), EVehicleEventType::AvmFrontRadarNotise },
	{ TEXT("AvmRearRadarNotise"), EVehicleEventType::AvmRearRadarNotise },
	{ TEXT("AvmFRadarDistance"), EVehicleEventType::AvmFRadarDistance },
	{ TEXT("AvmRRadarDistance"), EVehicleEventType::AvmRRadarDistance },
	{ TEXT("AvmFRadarNumLocation"), EVehicleEventType::AvmFRadarNumLocation },
	{ TEXT("AvmRRadarNumLocation"), EVehicleEventType::AvmRRadarNumLocation },

	//LSDA
	{ TEXT("LSDA_Forward"), EVehicleEventType::LSDA_Forward },
	{ TEXT("LSDA_Backwards"), EVehicleEventType::LSDA_Backwards },
	{ TEXT("LSDA_Continue"), EVehicleEventType::LSDA_Continue },
	{ TEXT("LSDA_Setting"), EVehicleEventType::LSDA_Setting },
	{ TEXT("LSDA_FeaturePush"), EVehicleEventType::LSDA_FeaturePush },
	{ TEXT("LSDA_WarningA"), EVehicleEventType::LSDA_WarningA },
	{ TEXT("LSDA_WarningB"), EVehicleEventType::LSDA_WarningB },
	{ TEXT("TBA_WarningA"), EVehicleEventType::TBA_WarningA },
	{ TEXT("TBA_WarningB"), EVehicleEventType::TBA_WarningB },
	{ TEXT("LSDA_ForwardStatus"), EVehicleEventType::LSDA_ForwardStatus },
	{ TEXT("LSDA_BackwardsStatus"), EVehicleEventType::LSDA_BackwardsStatus },
	{ TEXT("LSDA_ContinueStatus"), EVehicleEventType::LSDA_ContinueStatus },
	{ TEXT("LSDA_SettingStatus"), EVehicleEventType::LSDA_SettingStatus },
	{ TEXT("LSDA_FeaturePushStatus"), EVehicleEventType::LSDA_FeaturePushStatus },
	{ TEXT("LSDA_ViewSwitchingStatus"), EVehicleEventType::LSDA_ViewSwitchingStatus },
	{ TEXT("LSDA_ParkingPercent"), EVehicleEventType::LSDA_ParkingPercent },
	{ TEXT("LSDA_QuitStatus"), EVehicleEventType::LSDA_QuitStatus },
	{ TEXT("LSDA_SettingPageStatus"), EVehicleEventType::LSDA_SettingPageStatus },

	// DoubleFlashStatus ˫����״̬  date:1127
	{ TEXT("DoubleFlashStatus"), EVehicleEventType::DoubleFlashStatus },
	{ TEXT("ParkInPercent"), EVehicleEventType::ParkInPercent },

	{ TEXT("ValtPrkgMapGeneperctg"), EVehicleEventType::ValtPrkgMapGeneperctg },
	{ TEXT("HPA_ViewSwitching_Stat"), EVehicleEventType::HPA_ViewSwitching_Stat },

	{ TEXT("FirstParkMapID"), EVehicleEventType::FirstParkMapID },
	{ TEXT("FirstParkMapRecentlyused"), EVehicleEventType::FirstParkMapRecentlyused },
	{ TEXT("UsgModSts"), EVehicleEventType::UsgModSts },


	{ TEXT("ValtPrkgMapSyncnReq"), EVehicleEventType::ValtPrkgMapSyncnReq },
	{ TEXT("ValtPrkgMapSyncnReqMapList1"), EVehicleEventType::ValtPrkgMapSyncnReqMapList1 },
	{ TEXT("ValtPrkgMapSyncnReqMapList2"), EVehicleEventType::ValtPrkgMapSyncnReqMapList2 },
	{ TEXT("ValtPrkgMapSyncnReqMapList3"), EVehicleEventType::ValtPrkgMapSyncnReqMapList3 },
	{ TEXT("ValtPrkgMapSyncnReqMapList4"), EVehicleEventType::ValtPrkgMapSyncnReqMapList4 },


	
	{ TEXT("createHpaRoute"), EVehicleEventType::createHpaRoute },


	//��ͼ��Ϣ��ʾ����

	{ TEXT("MapInfoIsVis"), EVehicleEventType::MapInfoIsVis },

	{ TEXT("BuildDistanceVisible"), EVehicleEventType::BuildDistanceVisible },

	{ TEXT("CrossNumVisible"), EVehicleEventType::CrossNumVisible },

	{ TEXT("DecZoneNumVisible"), EVehicleEventType::DecZoneNumVisible },

	{ TEXT("DistanceToEndVisible"), EVehicleEventType::DistanceToEndVisible },

	{ TEXT("AvoidPersonsVisible"), EVehicleEventType::AvoidPersonsVisible },

	{ TEXT("AvoidCarsVisible"), EVehicleEventType::AvoidCarsVisible },

	{ TEXT("RampNumVisible"), EVehicleEventType::RampNumVisible },

	{ TEXT("ParkInDisLeftVisible"), EVehicleEventType::ParkInDisLeftVisible },


	//***********************************************��ͼ��Ϣ��ʵ����
	{ TEXT("CruiseTime"), EVehicleEventType::CruiseTime },

	
	{ TEXT("ValtPrkgEstdCrsTi"), EVehicleEventType::ValtPrkgEstdCrsTi },
	{ TEXT("ValtPrkgTotCrsTi"), EVehicleEventType::ValtPrkgTotCrsTi },
	{ TEXT("ValtPrkgCrsRemiTi"), EVehicleEventType::ValtPrkgCrsRemiTi },


	
	{ TEXT("HPA_BeginMoveBtn"), EVehicleEventType::HPA_BeginMoveBtn },
		
	{ TEXT("HPA_IsOpenSettingPage"), EVehicleEventType::HPA_IsOpenSettingPage },
	//HpaSettingInterface
	{ TEXT("HpaSettingInterface"), EVehicleEventType::HpaSettingInterface }


};

const TMap<FString, EReserved> ASubscriber::reservedToEnumMap =
{
	//预留
	{ TEXT("MapIDandSts_A"), EReserved::MapIDandSts_A },
	{ TEXT("MapIDandSts_B"), EReserved::MapIDandSts_B },
	{ TEXT("MapIDandSts_C"), EReserved::MapIDandSts_C },
	{ TEXT("MapIDandSts_D"), EReserved::MapIDandSts_D },
	{ TEXT("MapIDandSts_E"), EReserved::MapIDandSts_E },
	{ TEXT("MapIDandSts_F"), EReserved::MapIDandSts_F },
	{ TEXT("MapIDandSts_G"), EReserved::MapIDandSts_G },
	{ TEXT("MapIDandSts_H"), EReserved::MapIDandSts_H },
	{ TEXT("MapIDandSts_I"), EReserved::MapIDandSts_I },
	{ TEXT("MapIDandSts_J"), EReserved::MapIDandSts_J }
};





void ASubscriber::AddTopic(FString topic) {
	mTopicVec.push_back(topic);
}

void ASubscriber::AddTopics(TArray<FString> topics) {
	for (auto item : topics) {
		mTopicVec.push_back(item);
	}
}

void ASubscriber::RemoveTopic(FString topic) {
	bool rst = false;
	auto it = mTopicVec.begin();
	for (; it != mTopicVec.end(); it++) {
		if (!it->Compare(topic)) {
			rst = true;
			break;
		}
	}
	if (rst) {
		mTopicVec.erase(it);
	}
}

void ASubscriber::RemoveAllTopic() {
	mTopicVec.clear();
}

bool ASubscriber::AddTopicWithFile(FString filePath)
{
	TArray<FString> results;
	FFileHelper::LoadFileToStringArray(results, *filePath);
	for (auto item : results) {
		mTopicVec.push_back(item);
	}
	return results.Num() > 0 ? true : false;
}


EVehicleEventType ASubscriber::StringToVehicleEnum(const FString& Name)
{
	if (const EVehicleEventType* FoundEnum = NameToEnumMap.Find(Name))
	{
		return *FoundEnum;
	}

	return EVehicleEventType::Unknown;
}

EReserved ASubscriber::StringToReservedEnum(const FString& Name)
{
	if (const EReserved* FoundEnum = reservedToEnumMap.Find(Name))
	{
		return *FoundEnum;
	}

	return EReserved::Unknown;
}




void ASubscriber::IntValueUpdateEvent(const FString& name, int32 Value)
{
	if (!IsInGameThread()) {
		AsyncTask(ENamedThreads::GameThread, [this, name, Value]() {
			IntValueUpdateEvent(name, Value);
			});
		return;
	}
	if (!name.IsEmpty())
	{
		EVehicleEventType type;
		type = StringToVehicleEnum(name);
		if (type != EVehicleEventType::Unknown)
		{
			switch (type)
			{
			case EVehicleEventType::System_Change:
				HandleSystemChange(Value);
				break;
			case EVehicleEventType::GearType:
				HandleGearType(Value);
				break;
			case EVehicleEventType::DayNightMode:
				HandleDayNightMode(Value);
				break;
			case EVehicleEventType::PDCVolume:
				HandlePDCVolume(Value);
				break;
			case EVehicleEventType::RCTAWarning:
				HandleRCTAWarning(Value);
				break;
			case EVehicleEventType::Widget_Finish:
				HandleWidgetFinish(Value);
				break;
			case EVehicleEventType::PDCVolume_Sts:
				HandlePDCVolumeSts(Value);
				break;
			case EVehicleEventType::DebugShow:
				HandleDebugShow(Value);
				break;
			case EVehicleEventType::CameraSts:
				HandleCameraSts(Value);
				break;
			case EVehicleEventType::RPAWarn:
				HandleRPAWarn(Value);
				break;

				// ���ֺͳ���״̬
			case EVehicleEventType::FWheelRotateDir:
				HandleFWheelRotateDir(Value);
				break;
			case EVehicleEventType::RWheelRotateDir:
				HandleRWheelRotateDir(Value);
				break;
			case EVehicleEventType::FWheelRotateSpeed:
				HandleFWheelRotateSpeed(Value);
				break;
			case EVehicleEventType::RWheelRotateSpeed:
				HandleRWheelRotateSpeed(Value);
				break;
			case EVehicleEventType::WhlRotToothCntr:
				HandleWhlRotToothCntr(Value);
				break;
			case EVehicleEventType::DrvDoorSts:
				HandleDrvDoorSts(Value);
				break;
			case EVehicleEventType::PassDoorSts:
				HandlePassDoorSts(Value);
				break;
			case EVehicleEventType::LeReDoorSts:
				HandleLeReDoorSts(Value);
				break;
			case EVehicleEventType::RiReDoorSts:
				HandleRiReDoorSts(Value);
				break;
			case EVehicleEventType::HoodSts:
				HandleHoodSts(Value);
				break;
			case EVehicleEventType::TrunkSts:
				HandleTrunkSts(Value);
				break;

				// �������촰
			case EVehicleEventType::DrvWinPer:
				HandleDrvWinPer(Value);
				break;
			case EVehicleEventType::PassWinPer:
				HandlePassWinPer(Value);
				break;
			case EVehicleEventType::LeReWinPer:
				HandleLeReWinPer(Value);
				break;
			case EVehicleEventType::RiReWinPer:
				HandleRiReWinPer(Value);
				break;
			case EVehicleEventType::SunroofPer:
				HandleSunroofPer(Value);
				break;

				// ���Ӿ�״̬
			case EVehicleEventType::DrvMirrSts:
				HandleDrvMirrSts(Value);
				break;
			case EVehicleEventType::PassMirrSts:
				HandlePassMirrSts(Value);
				break;

				// �ƹ������״�?
			case EVehicleEventType::LowBeam:
				HandleLowBeam(Value);
				break;
			case EVehicleEventType::HighBeam:
				HandleHighBeam(Value);
				break;
			case EVehicleEventType::TurnLeftLightSts:
				HandleTurnLeftLightSts(Value);
				break;
			case EVehicleEventType::TurnRightLightSts:
				HandleTurnRightLightSts(Value);
				break;
			case EVehicleEventType::DoubleFlash:
				HandleDoubleFlash(Value);
				break;
			case EVehicleEventType::BreakLightSts:
				HandleBreakLightSts(Value);
				break;
			case EVehicleEventType::DrvSeatfoldSts:
				HandleDrvSeatfoldSts(Value);
				break;

				// �����˶�״̬
			case EVehicleEventType::CarRoll:
				HandleCarRoll(Value);
				break;
			case EVehicleEventType::CarPitch:
				HandleCarPitch(Value);
				break;
			case EVehicleEventType::CarYaw:
				HandleCarYaw(Value);
				break;
			case EVehicleEventType::DrivePosition:
				HandleDrivePosition(Value);
				break;
			case EVehicleEventType::CarSpeed:
				HandleCarSpeed(Value);
				break;
			case EVehicleEventType::SteeringAngle:
				HandleSteeringAngle(Value);
				break;

				// AVM���?
			case EVehicleEventType::Avm5sTimer:
				HandleAvm5sTimer(Value);
				break;
			case EVehicleEventType::AvmWanringA0:
				HandleAvmWanringA0(Value);
				break;
			case EVehicleEventType::AvmWanringA1:
				HandleAvmWanringA1(Value);
				break;
			case EVehicleEventType::AvmWanringA2:
				HandleAvmWanringA2(Value);
				break;
			case EVehicleEventType::AvmWanringA3:
				HandleAvmWanringA3(Value);
				break;
			case EVehicleEventType::AvmWanringB1:
				HandleAvmWanringB1(Value);
				break;
			case EVehicleEventType::AvmWanringB2:
				HandleAvmWanringB2(Value);
				break;
			case EVehicleEventType::AvmWanringB3:
				HandleAvmWanringB3(Value);
				break;
			case EVehicleEventType::AvmWanringB4:
				HandleAvmWanringB4(Value);
				break;
			case EVehicleEventType::AvmTrunkWarning:
				HandleAvmTrunkWarning(Value);
				break;

			case EVehicleEventType::AvmCurrViewType:
				HandleAvmCurrViewType(Value);
				break;
			case EVehicleEventType::AvmViewTwoD:
				HandleAvmViewTwoD(Value);
				break;
			case EVehicleEventType::AvmViewThreeD:
				HandleAvmViewThreeD(Value);
				break;
			case EVehicleEventType::AvmSettingPage:
				HandleAvmSettingPage(Value);
				break;
			case EVehicleEventType::AvmTransparentCar:
				HandleAvmTransparentCar(Value);
				break;
			case EVehicleEventType::AvmSteeringlinkage:
				HandleAvmSteeringlinkage(Value);
				break;
			case EVehicleEventType::AvmNarrowActive:
				HandleAvmNarrowActive(Value);
				break;
			case EVehicleEventType::AvmFactoryStatus:
				HandleAvmFactoryStatus(Value);
				break;
			case EVehicleEventType::AvmVersionNum:
				HandleAvmVersionNum(Value);
				break;
			case EVehicleEventType::AvmCalib:
				HandleAvmCalib(Value);
				break;
			case EVehicleEventType::AvmCalibProgress:
				HandleAvmCalibProgress(Value);
				break;
			case EVehicleEventType::AvmCamFaultFront:
				HandleAvmCamFaultFront(Value);
				break;
			case EVehicleEventType::AvmCamFaultRear:
				HandleAvmCamFaultRear(Value);
				break;
			case EVehicleEventType::AvmCamFaultLeft:
				HandleAvmCamFaultLeft(Value);
				break;
			case EVehicleEventType::AvmCamFaultRight:
				HandleAvmCamFaultRight(Value);
				break;

				// APA���?
			case EVehicleEventType::APABtnStatus:
				HandleAPABtnStatus(Value);
				break;
				
			case EVehicleEventType::APA_ParkInSurplusDistance:
				HandleAPAParkInSurplusDistance(Value);
				break;
			case EVehicleEventType::APA_Quit:
				HandleAPA_Quit(Value);
				break;
			case EVehicleEventType::APA_RadarMute:
				HandleAPA_RadarMute(Value);
				break;
			case EVehicleEventType::APA_To360:
				HandleAPA_To360(Value);
				break;
			case EVehicleEventType::APA_ViewSwitching:
				HandleAPA_ViewSwitching(Value);
				break;
			case EVehicleEventType::APA_ParkinReq:
				HandleAPA_ParkinReq(Value);
				break;
			case EVehicleEventType::APA_ParkoutReq:
				HandleAPA_ParkoutReq(Value);
				break;
			case EVehicleEventType::APA_Setting:
				HandleAPA_Setting(Value);
				break;
			case EVehicleEventType::APA_ContPark:
				HandleAPA_ContPark(Value);
				break;
			case EVehicleEventType::APA_ParkingWithoutFeeling:
				HandleAPA_ParkingWithoutFeeling(Value);
				break;
			case EVehicleEventType::APA_LanguageBroadcast:
				HandleAPA_LanguageBroadcast(Value);
				break;
			case EVehicleEventType::APA_BroadcastMode:
				HandleAPA_BroadcastMode(Value);
				break;
			case EVehicleEventType::APA_SRViewChange:
				HandleAPA_SRViewChange(Value);
				break;
			case EVehicleEventType::APA_CenterIn:
				HandleAPA_CenterIn(Value);
				break;
			case EVehicleEventType::APA_LeftIn:
				HandleAPA_LeftIn(Value);
				break;
			case EVehicleEventType::APA_RightIn:
				HandleAPA_RightIn(Value);
				break;
			case EVehicleEventType::APA_LeftOut:
				HandleAPA_LeftOut(Value);
				break;
			case EVehicleEventType::APA_RightOut:
				HandleAPA_RightOut(Value);
				break;
			case EVehicleEventType::APA_StartParkOut:
				HandleAPA_StartParkOut(Value);
				break;
			case EVehicleEventType::APA_WorkSts:
				HandleAPA_WorkSts(Value);
				break;
			case EVehicleEventType::APA_TouchX:
				HandleAPA_TouchX(Value);
				break;
			case EVehicleEventType::APA_TouchY:
				HandleAPA_TouchY(Value);
				break;
			case EVehicleEventType::APA_FingerIndex:
				HandleAPA_FingerIndex(Value);
				break;
			case EVehicleEventType::APA_TouchEnvTyp:
				HandleAPA_TouchEnvTyp(Value);
				break;
			case EVehicleEventType::APA_Quit_Stat:
				HandleAPA_Quit_Stat(Value);
				break;
			case EVehicleEventType::APA_RadarMute_Stat:
				HandleAPA_RadarMute_Stat(Value);
				break;
			case EVehicleEventType::APA_To360_Stat:
				HandleAPA_To360_Stat(Value);
				break;
			case EVehicleEventType::APA_ViewSwitching_Stat:
				HandleAPA_ViewSwitching_Stat(Value);
				break;
			case EVehicleEventType::APA_ParkinReq_Stat:
				HandleAPA_ParkinReq_Stat(Value);
				break;
			case EVehicleEventType::APA_ParkoutReq_Stat:
				HandleAPA_ParkoutReq_Stat(Value);
				break;
			case EVehicleEventType::APA_Setting_Stat:
				HandleAPA_Setting_Stat(Value);
				break;
			case EVehicleEventType::APA_SettingBtnSts_Stat:
				HandleAPA_SettingBtnSts_Stat(Value);
				break;
			case EVehicleEventType::APA_ContPark_Stat:
				HandleAPA_ContPark_Stat(Value);
				break;
			case EVehicleEventType::APA_ViewID_Stat:
				HandleAPA_ViewID_Stat(Value);
				break;
			case EVehicleEventType::APA_ParkingWithoutFeeling_Stat:
				HandleAPA_ParkingWithoutFeeling_Stat(Value);
				break;
			case EVehicleEventType::APA_LanguageBroadcast_Stat:
				HandleAPA_LanguageBroadcast_Stat(Value);
				break;
			case EVehicleEventType::APA_BroadcastMode_Stat:
				HandleAPA_BroadcastMode_Stat(Value);
				break;
			case EVehicleEventType::APA_SRViewChange_Stat:
				HandleAPA_SRViewChange_Stat(Value);
				break;
			case EVehicleEventType::APA_CenterIn_Stat:
				HandleAPA_CenterIn_Stat(Value);
				break;
			case EVehicleEventType::APA_LeftIn_Stat:
				HandleAPA_LeftIn_Stat(Value);
				break;

			case EVehicleEventType::APA_RightIn_Stat:
				HandleAPA_RightIn_Stat(Value);
				break;
			case EVehicleEventType::APA_LeftOut_Stat:
				HandleAPA_LeftOut_Stat(Value);
				break;
			case EVehicleEventType::APA_RightOut_Stat:
				HandleAPA_RightOut_Stat(Value);
				break;
			case EVehicleEventType::APA_PerFrontLeftOut_Stat:
				HandleAPA_PerFrontLeftOut_Stat(Value);
				break;
			case EVehicleEventType::APA_PerFrontRightOut_Stat:
				HandleAPA_PerFrontRightOut_Stat(Value);
				break;
			case EVehicleEventType::APA_PerBackLeftOut_Stat:
				HandleAPA_PerBackLeftOut_Stat(Value);
				break;
			case EVehicleEventType::APA_PerBackRightOut_Stat:
				HandleAPA_PerBackRightOut_Stat(Value);
				break;
			case EVehicleEventType::APA_StartParkOut_Stat:
				HandleAPA_StartParkOut_Stat(Value);
				break;
			case EVehicleEventType::APA_ParkingPercent:
				HandleAPA_ParkingPercent(Value);
				break;
			case EVehicleEventType::APA_ParkOutInterface_Stat:
				HandleAPA_ParkOutInterface_Stat(Value);
				break;
			case EVehicleEventType::APA_WarningID:
				HandleAPA_WarningID(Value);
				break;
			case EVehicleEventType::APA_WarningAID:
				HandleAPA_WarningAID(Value);
				break;
			case EVehicleEventType::APA_ParkInSurplusDistance_Stat:
				HandleAPA_ParkInSurplusDistance_Stat(Value);
				break;
			case EVehicleEventType::DrvrAsscSysSts:
				HandleDrvrAsscSysSts(Value);
				break;

			case EVehicleEventType::APA_ViewSwitching_BtnStat:
				HandleAPA_ViewSwitching_BtnStat(Value);
				break;
			case EVehicleEventType::APA_FrntAndReSwt:
				HandleAPA_FrntAndReSwt(Value);
				break;
			case EVehicleEventType::APA_FrntAndReSwt_Stat:
				HandleAPA_FrntAndReSwt_Stat(Value);
				break;

			case EVehicleEventType::APA_StrtPrkg_Stat:
				HandleAPA_StrtPrkg_Stat(Value);
				break;

				//RPA
			case EVehicleEventType::RPA_CarInPark_Stat:
				HandleRPA_CarInPark_Stat(Value);
				break;
			case EVehicleEventType::RPA_RemCtrlPrkg_Stat:
				HandleRPA_RemCtrlPrkg_Stat(Value);
				break;
			case EVehicleEventType::PrkgAssiSysRemPrkgSts:
				HandlePrkgAssiSysRemPrkgSts(Value);
				break;
				

				//HPA
			case EVehicleEventType::HPASettingPage:
				HandleHPASettingPage(Value);
				break;
			case EVehicleEventType::SmarkGeneration:
				HandleSmarkGeneration(Value);
				break;
			case EVehicleEventType::FuncPush:
				HandleFuncPush(Value);
				break;
			case EVehicleEventType::HPAVoiceSts:
				HandleHPAVoiceSts(Value);
				break;
			case EVehicleEventType::MyRouteSts:
				HandleMyRouteSts(Value);
				break;
			case EVehicleEventType::ParkInRouteNum:
				HandleParkInRouteNum(Value);
				break;
			case EVehicleEventType::ParkOutRouteNum:
				HandleParkOutRouteNum(Value);
				break;
			case EVehicleEventType::BuildDistance:
				HandleBuildDistance(Value);
				break;
			case EVehicleEventType::RampNum:
				HandleRampNum(Value);
				break;
			case EVehicleEventType::DecZoneNum:
				HandleDecZoneNum(Value);
				break;
			case EVehicleEventType::ParkInDisLeft:
				HandleParkInDisLeft(Value);
				break;
			case EVehicleEventType::CuriseTimeLeft:
				HandleCuriseTimeLeft(Value);
				break;
			case EVehicleEventType::AvoidPersons:
				HandleAvoidPersons(Value);
				break;
			case EVehicleEventType::AvoidCars:
				HandleAvoidCars(Value);
				break;
				/*case EVehicleEventType::DistanceToEnd:
					HandleDistanceToEnd(Value);
					break;*/
			case EVehicleEventType::TotalCurisingTime:
				HandleTotalCurisingTime(Value);
				break;

			case EVehicleEventType::FirstParkMapTime:
				HandleFirstParkMapTime(Value);
				break;
			case EVehicleEventType::FirstParkMapDistance:
				HandleFirstParkMapDistance(Value);
				break;
			case EVehicleEventType::FirstParkMapSts:
				HandleFirstParkMapSts(Value);
				break;
			case EVehicleEventType::Recentlyused:
				HandleRecentlyused(Value);
				break;
			case EVehicleEventType::FirstParkPhoneDis:
				HandleFirstParkPhoneDis(Value);
				break;
			case EVehicleEventType::SecParkMapTime:
				HandleSecParkMapTime(Value);
				break;
			case EVehicleEventType::SecParkMapDistance:
				HandleSecParkMapDistance(Value);
				break;
			case EVehicleEventType::SecParkMapSts:
				HandleSecParkMapSts(Value);
				break;
			case EVehicleEventType::SecParkPhoneDis:
				HandleSecParkPhoneDis(Value);
				break;
			case EVehicleEventType::HPASts:
				HandleHPASts(Value);
				break;
			case EVehicleEventType::PhoneCtrlSts:
				HandlePhoneCtrlSts(Value);
				break;
			case EVehicleEventType::HPASumPage:
				HandleHPASumPage(Value);
				break;
			case EVehicleEventType::HPARecoverBtn:
				HandleHPARecoverBtn(Value);
				break;

			case EVehicleEventType::HPAFinishBtn:
				HandleHPAFinishBtn(Value);
				break;
			case EVehicleEventType::BtnSettingSts:
				HandleBtnSettingSts(Value);
				break;
			case EVehicleEventType::BtnExitSts:
				HandleBtnExitSts(Value);
				break;
			case EVehicleEventType::BtnParkInSts:
				HandleBtnParkInSts(Value);
				break;
			case EVehicleEventType::BtnParkOutSts:
				HandleBtnParkOutSts(Value);
				break;
			case EVehicleEventType::HPABtnMuteSts:
				HandleHPABtnMuteSts(Value);
				break;
			case EVehicleEventType::HPAPDCVolume:
				HandleHPAPDCVolume(Value);
				break;
			case EVehicleEventType::ParkInRouteCover:
				HandleParkInRouteCover(Value);
				break;
			case EVehicleEventType::ParkOutRouteCover:
				HandleParkOutRouteCover(Value);
				break;
			case EVehicleEventType::RouteSelectionVisibility:
				HandleRouteSelectionVisibility(Value);
				break;
			case EVehicleEventType::StartMemoParking:
				HandleStartMemoParking(Value);
				break;
			case EVehicleEventType::BuildRouteCard:
				HandleBuildRouteCard(Value);
				break;
			case EVehicleEventType::ForwardRouteCard:
				HandleForwardRouteCard(Value);
				break;
			case EVehicleEventType::ParkInRouteOver:
				HandleParkInRouteOver(Value);
				break;
			case EVehicleEventType::ParkOutRouteOver:
				HandleParkOutRouteOver(Value);
				break;
			case EVehicleEventType::HPACurentViewType:
				HandleHPACurentViewType(Value);
				break;
			case EVehicleEventType::HPAWorkingSts:
				HandleHPAWorkingSts(Value);
				break;
			case EVehicleEventType::HPACarport:
				HandleHPACarport(Value);
				break;
			case EVehicleEventType::HPATargetVehicle:
				HandleHPATargetVehicle(Value);
				break;
			case EVehicleEventType::HPADrawRoad:
				HandleHPADrawRoad(Value);
				break;
			case EVehicleEventType::HPAWarn:
				HandleHPAWarn(Value);
				break;
			case EVehicleEventType::HPASmallWarn:
				HandleHPASmallWarn(Value);
				break;
			case EVehicleEventType::HPASystemWarn:
				HandleHPASystemWarn(Value);
				break;
			case EVehicleEventType::HPASystemWarnType:
				HandleHPASystemWarnType(Value);
				break;
			case EVehicleEventType::LSDAWarn:
				HandleLSDAWarn(Value);
				break;
			case EVehicleEventType::HPAViewName:
				HandleHPAViewName(Value);
				break;
			case EVehicleEventType::GlobalViewBtn:
				HandleGlobalViewBtn(Value);
				break;
			case EVehicleEventType::PartViewBtn:
				HandlePartViewBtn(Value);
				break;

				//PDC
			case EVehicleEventType::RML_RadarStatus:
				HandleRML_RadarStatus(Value);
				break;
			case EVehicleEventType::RMR_RadarStatus:
				HandleRMR_RadarStatus(Value);
				break;
			case EVehicleEventType::RL_RardarStatus:
				HandleRL_RardarStatus(Value);
				break;
			case EVehicleEventType::RR_RadarStatus:
				HandleRR_RadarStatus(Value);
				break;
			case EVehicleEventType::FL_RadarStatus:
				HandleFL_RadarStatus(Value);
				break;

			case EVehicleEventType::FR_RadarStatus:
				HandleFR_RadarStatus(Value);
				break;

			case EVehicleEventType::FML_RadarStatus:
				HandleFML_RadarStatus(Value);
				break;
			case EVehicleEventType::FMR_RadarStatus:
				HandleFMR_RadarStatus(Value);
				break;
			case EVehicleEventType::LFSide_RadarStatus:
				HandleLFSide_RadarStatus(Value);
				break;
			case EVehicleEventType::RFSide_RadarStatus:
				HandleRFSide_RadarStatus(Value);
				break;
			case EVehicleEventType::LRSide_RadarStatus:
				HandleLRSide_RadarStatus(Value);
				break;
			case EVehicleEventType::RRSide_RadarStatus:
				HandleRRSide_RadarStatus(Value);
				break;
			case EVehicleEventType::AvmFrontRadarNotise:
				HandleAvmFrontRadarNotise(Value);
				break;
			case EVehicleEventType::AvmRearRadarNotise:
				HandleAvmRearRadarNotise(Value);
				break;
			case EVehicleEventType::AvmFRadarDistance:
				HandleAvmFRadarDistance(Value);
				break;
			case EVehicleEventType::AvmRRadarDistance:
				HandleAvmRRadarDistance(Value);
				break;
			case EVehicleEventType::AvmFRadarNumLocation:
				HandleAvmFRadarNumLocation(Value);
				break;
			case EVehicleEventType::AvmRRadarNumLocation:
				HandleAvmRRadarNumLocation(Value);
				break;

				//LSDA

			case EVehicleEventType::LSDA_Forward:
				HandleLSDA_Forward(Value);
				break;
			case EVehicleEventType::LSDA_Backwards:
				HandleLSDA_Backwards(Value);
				break;
			case EVehicleEventType::LSDA_Continue:
				HandleLSDA_Continue(Value);
				break;
			case EVehicleEventType::LSDA_Setting:
				HandleLSDA_Setting(Value);
				break;
			case EVehicleEventType::LSDA_FeaturePush:
				HandleLSDA_FeaturePush(Value);
				break;
			case EVehicleEventType::LSDA_WarningA:
				HandleLSDA_WarningA(Value);
				break;
			case EVehicleEventType::LSDA_WarningB:
				HandleLSDA_WarningB(Value);
				break;
			case EVehicleEventType::TBA_WarningA:
				HandleTBA_WarningA(Value);
				break;
			case EVehicleEventType::TBA_WarningB:
				HandleTBA_WarningB(Value);
				break;
			case EVehicleEventType::LSDA_ForwardStatus:
				HandleLSDA_ForwardStatus(Value);
				break;
			case EVehicleEventType::LSDA_BackwardsStatus:
				HandleLSDA_BackwardsStatus(Value);
				break;
			case EVehicleEventType::LSDA_ContinueStatus:
				HandleLSDA_ContinueStatus(Value);
				break;
			case EVehicleEventType::LSDA_SettingStatus:
				HandleLSDA_SettingStatus(Value);
				break;
			case EVehicleEventType::LSDA_FeaturePushStatus:
				HandleLSDA_FeaturePushStatus(Value);
				break;
			case EVehicleEventType::LSDA_ViewSwitchingStatus:
				HandleLSDA_ViewSwitchingStatus(Value);
				break;
			case EVehicleEventType::LSDA_ParkingPercent:
				HandleLSDA_ParkingPercent(Value);
				break;
			case EVehicleEventType::LSDA_QuitStatus:
				HandleLSDA_QuitStatus(Value);
				break;
			case EVehicleEventType::LSDA_SettingPageStatus:
				HandleLSDA_SettingPageStatus(Value);
				break;

			case EVehicleEventType::DoubleFlashStatus:
				HandleDoubleFlashStatus(Value);
				break;
			case EVehicleEventType::ValtPrkgMapGeneperctg:
				HandleValtPrkgMapGeneperctg(Value);
				break;
			case EVehicleEventType::ParkInPercent:
				HandleParkInPercent(Value);
				break;
			case EVehicleEventType::HPA_ViewSwitching_Stat:
				HandleHPA_ViewSwitching_Stat(Value);
				break;
			case EVehicleEventType::HpaSettingInterface:
				HandleHpaSettingInterface(Value);
				break;
			case EVehicleEventType::FirstParkMapID:
				HandleFirstParkMapID(Value);
				break;
			case EVehicleEventType::FirstParkMapRecentlyused:
				HandleFirstParkMapRecentlyused(Value);
				break;
			case EVehicleEventType::UsgModSts:
				HandleUsgModSts(Value);
				break;
			case EVehicleEventType::ValtPrkgMapSyncnReq:
				HandleValtPrkgMapSyncnReq(Value);
				break;
			case EVehicleEventType::ValtPrkgMapSyncnReqMapList1:
				HandleValtPrkgMapSyncnReqMapList1(Value);
				break;
			case EVehicleEventType::ValtPrkgMapSyncnReqMapList2:
				HandleValtPrkgMapSyncnReqMapList2(Value);
				break;
			case EVehicleEventType::ValtPrkgMapSyncnReqMapList3:
				HandleValtPrkgMapSyncnReqMapList3(Value);
				break;
			case EVehicleEventType::ValtPrkgMapSyncnReqMapList4:
				HandleValtPrkgMapSyncnReqMapList4(Value);
				break;
			case EVehicleEventType::MapInfoIsVis:
				HandleMapInfoIsVis(Value);
				break;
			case EVehicleEventType::BuildDistanceVisible:
				HandleBuildDistanceVisible(Value);
				break;

			case EVehicleEventType::CrossNumVisible:
				HandleCrossNumVisible(Value);
				break;

			case EVehicleEventType::DecZoneNumVisible:
				HandleDecZoneNumVisible(Value);
				break;

			case EVehicleEventType::DistanceToEndVisible:
				HandleDistanceToEndVisible(Value);
				break;

			case EVehicleEventType::AvoidPersonsVisible:
				HandleAvoidPersonsVisible(Value);
				break;

			case EVehicleEventType::AvoidCarsVisible:
				HandleAvoidCarsVisible(Value);
				break;

			case EVehicleEventType::RampNumVisible:
				HandleRampNumVisible(Value);
				break;

			case EVehicleEventType::ParkInDisLeftVisible:
				HandleParkInDisLeftVisible(Value);
				break;
				
			case EVehicleEventType::CruiseTime:
				HandleCruiseTime(Value);
				break;

			case EVehicleEventType::ValtPrkgViewDisp:
				HandleValtPrkgViewDisp(Value);
				break;
				
				
			case EVehicleEventType::HPA_BeginMoveBtn:
				HandleHPA_BeginMoveBtn(Value);
				break;

			case EVehicleEventType::createHpaRoute:
				HandlecreateHpaRoute(Value);
				break;
				
			case EVehicleEventType::HPA_IsOpenSettingPage:
				HandleHPA_IsOpenSettingPage(Value);
				break;

			}
		}

		EReserved types;
		types = StringToReservedEnum(name);
		if (types != EReserved::Unknown)
		{
			switch (types)
			{
				//预留
			case EReserved::MapIDandSts_A:
				HandleMapIDandSts_A(Value);
				break;
			case EReserved::MapIDandSts_B:
				HandleMapIDandSts_B(Value);
				break;
			case EReserved::MapIDandSts_C:
				HandleMapIDandSts_C(Value);
				break;
			case EReserved::MapIDandSts_D:
				HandleMapIDandSts_D(Value);
				break;
			case EReserved::MapIDandSts_E:
				HandleMapIDandSts_E(Value);
				break;
			case EReserved::MapIDandSts_F:
				HandleMapIDandSts_F(Value);
				break;
			case EReserved::MapIDandSts_G:
				HandleMapIDandSts_G(Value);
				break;
			case EReserved::MapIDandSts_H:
				HandleMapIDandSts_H(Value);
				break;
			case EReserved::MapIDandSts_I:
				HandleMapIDandSts_I(Value);
				break;
			case EReserved::MapIDandSts_J:
				HandleMapIDandSts_J(Value);
				break;
			}
		}
		//UE_LOG(LogTemp, Warning, TEXT("Unhandled vehicle event type: %s"), *name);

		//(LogCarLinx,Warning, TEXT("[DataSouce]Unhandled vehicle event type: %s"), *name);
	}



	return;


}

void ASubscriber::FStringValueUpdate(const FString& name, const FString& value)
{


	if (!IsInGameThread()) {
		AsyncTask(ENamedThreads::GameThread, [this, name, value]() {
			FStringValueUpdate(name, value);
			CARLINX_LOG(LogCarLinx,Warning, TEXT("[DataSouce]FStringValueUpdate11111 event type: %s"), *name);
			});
		return;
	}
	FStringValueUpdateEvent(name, value);
//	CARLINX_LOG(LogCarLinx,Warning, TEXT("[DataSouce]FStringValueUpdate event type: %s"), *name);
}

