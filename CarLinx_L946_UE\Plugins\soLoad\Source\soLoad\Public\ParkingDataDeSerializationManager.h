#pragma once

// UE4 Core headers
#include "CoreMinimal.h"
#include "UObject/Object.h"

// Project headers
#include "DataTypes.h"
#include "FileReadThread.h"
#include "ParkingDataManager.h"

// Generated header must be last
#include "ParkingDataDeSerializationManager.generated.h"

UCLASS(Blueprintable)
class SOLOAD_API UParkingDataDeSerializationManager : public UObject
{
    GENERATED_BODY()

public:
    UParkingDataDeSerializationManager();
    virtual ~UParkingDataDeSerializationManager();
    
    static UParkingDataDeSerializationManager* GetInstance();
    
    // 开始回放数据
    // @param FilePath 要回放的文件路径。如果为空，将自动使用当前状态下最新的记录文件
    bool StartPlayback(bool autoPlay = false, const FString& FilePath = "");
    
    // 暂停回放
    void PausePlayback();
    
    // 恢复回放
    void ResumePlayback();
    
    
    
    // 暂未实现
    void PlayNextFrame();
    
    FDelegateHandle  Tickerhandle;
    void ForwardATick(float InDeltaTime);
    
    void StartPlaybackLoop(const FString& FilePath);
    
    void StopPlayback();

private:
    static constexpr uint32_t MAGIC_NUMBER = 0x504B4447; // "PKDG"
    static constexpr uint32_t CURRENT_VERSION = 1;

    UPROPERTY()
    FString CurrentFilePath;
    
    FParkingDataFileHeader PlaybackHeader;
    
    void ProcessPlaybackData(const std::vector<uint8_t>& Data);
    
    TUniquePtr<FFileReadThread> FileReadThread;
    bool bIsPaused;
    bool bIsAutoPlaying;
    
    static UParkingDataDeSerializationManager* Instance;
    
    // 播放速度控制
    float PlaybackSpeed = 1.f;

    int LoopCounter = 0;

    //这里用无符号的值更合理
    int64 FileStartTime = 0;
    int64 LastFileTime = 0;
    int64 FileCurrentTime = 0;
}; 