#include "ANetworkManager.h"
#include "FUDPClientThread.h" // 包含 FUDPClientThreadManager 的头文件
#include "Logging/LogMacros.h"
#include "../../../Source/CarLinx_L946_UE/Public/Version.h"
#include "Logger.h"
#include "CoreMinimal.h"

// JSON 相关头文件
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"

// UE4 Core headers
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"
#include "TimerManager.h"
#include "Async/AsyncWork.h"

// Project headers
#include "FUDPClientThread.h"
#include "FUDPClientThreadToAndroid.h"
#include "ParkingDataSerializationManager.h"
#include "ParkingDataDeSerializationManager.h"
#include "ParkingDataProcessManager.h"
#include "FilterAlgorithm.h"

ANetworkManager::ANetworkManager()
{
	CARLINX_LOG(LogCarLinx, Warning, TEXT("[0x%p] ANetworkManager() Begin"), this);
    PrimaryActorTick.bCanEverTick = false;
    bIsClientRunning = false;
	bIsRecording = false;
	bIsPlaying = false;

	// 默认使用UDP接收
	//CurrentDataSource = EDataSource::UDP_RECEIVER;

    // 读取 Version.txt 的内容
    FString VersionFilePath = FPaths::ProjectContentDir() / TEXT("Version/Version.txt");
    FString VersionFileContent;
    if (FFileHelper::LoadFileToString(VersionFileContent, *VersionFilePath))
    {
        // 提取 Version.txt 中的 Version String
        FString VersionStringFromTxt;
        const FString SearchString = TEXT("Version String: ");
        int32 FoundIndex = VersionFileContent.Find(SearchString);
        if (FoundIndex != INDEX_NONE)
        {
            int32 StartIndex = FoundIndex + SearchString.Len();
            int32 EndIndex = VersionFileContent.Find(TEXT("\n"), ESearchCase::IgnoreCase, ESearchDir::FromStart, StartIndex);
            VersionStringFromTxt = VersionFileContent.Mid(StartIndex, EndIndex - StartIndex).TrimStartAndEnd();

            if (VersionStringFromTxt == TEXT(VERSION_STRING))
            {
                CARLINX_LOG(LogCarLinx, Warning, TEXT(" Version Strings are identical."));
                CARLINX_LOG(LogCarLinx, Warning, TEXT(" Version String from UE4.so File: %s"), TEXT(VERSION_STRING));
            }
            else
            {
                CARLINX_LOG(LogCarLinx, Warning, TEXT(" Version Strings differ."));
                // 打印从 Version.txt 中读取到的 Version String
                CARLINX_LOG(LogCarLinx, Warning, TEXT(" Version String from OBB File: %s"), *VersionStringFromTxt);
                // 打印和比较 Version String
                CARLINX_LOG(LogCarLinx, Warning, TEXT(" Version String from UE4.so File: %s"), TEXT(VERSION_STRING));
            }
        }
        else
        {
            CARLINX_LOG(LogCarLinx, Error, TEXT(" Version String not found in OBB File"));
        }
        
    }
    else
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT(" Failed to read OBB File"));
    }

	CARLINX_LOG(LogCarLinx, Warning, TEXT("[0x%p] ANetworkManager() End"), this);
}

ANetworkManager::~ANetworkManager()
{
	CARLINX_LOG(LogCarLinx, Warning, TEXT("[0x%p] ~ANetworkManager() Begin"), this);
	if (bIsClientRunning) {
		StopUDPClient();
	}
	CARLINX_LOG(LogCarLinx, Warning, TEXT("[0x%p] ~ANetworkManager() End"), this);
}

void ANetworkManager::BeginPlay()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] BeginPlay() Begin"), this);

	// 订阅数据处理管理器的事件
	auto DataProcessManager = UParkingDataProcessManager::GetInstance();
	DataProcessManager->OnSelfCarDataProcessed.AddUObject(this, &ANetworkManager::OnSelfCarDataUpdated);
	DataProcessManager->OnSlotDataProcessed.AddUObject(this, &ANetworkManager::OnSlotDataUpdated);
	DataProcessManager->OnObstacleDataProcessed.AddUObject(this, &ANetworkManager::OnObstacleDataUpdated);
	DataProcessManager->OnTrafficSignDataProcessed.AddUObject(this, &ANetworkManager::OnTrafficSignDataUpdated);
	DataProcessManager->OnRoadRightPointDataProcessed.AddUObject(this, &ANetworkManager::OnRoadRightPointDataUpdated);
	DataProcessManager->OnRoadLeftPointDataProcessed.AddUObject(this, &ANetworkManager::OnRoadLeftPointDataUpdated);
	DataProcessManager->OnPerspectiveModeProcessed.AddUObject(this, &ANetworkManager::OnPerspectiveModeUpdated);
	DataProcessManager->OnRoadPointDataUpdated.AddUObject(this, &ANetworkManager::OnRoadPointUpdated);
	
	// 订阅FUDPClientThreadToAndroidManager的事件
	FUDPClientThreadToAndroidManager::Get().OnSyncMapDelegate.AddUObject(this, &ANetworkManager::OnSyncMapUpdated);

	DataProcessManager->OnTrackPlanningProcessed.AddUObject(this, &ANetworkManager::OnTrackPlanningPointsUpdated);

	SwitchDataSource(EDataSource::UDP_RECEIVER);

	Super::BeginPlay();
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] BeginPlay() End"), this);
}

void ANetworkManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	Super::EndPlay(EndPlayReason);
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] EndPlay() Begin"), this);

	// 取消订阅数据处理管理器的事件
	auto DataProcessManager = UParkingDataProcessManager::GetInstance();
	DataProcessManager->OnSelfCarDataProcessed.RemoveAll(this);
	DataProcessManager->OnSlotDataProcessed.RemoveAll(this);
	DataProcessManager->OnObstacleDataProcessed.RemoveAll(this);
	DataProcessManager->OnTrafficSignDataProcessed.RemoveAll(this);
	DataProcessManager->OnRoadRightPointDataProcessed.RemoveAll(this);
	DataProcessManager->OnRoadLeftPointDataProcessed.RemoveAll(this);
	DataProcessManager->OnPerspectiveModeProcessed.RemoveAll(this);
	DataProcessManager->OnTrackPlanningProcessed.RemoveAll(this);
	DataProcessManager->OnLeftPointsProcessed.RemoveAll(this);

	// 取消订阅FUDPClientThreadToAndroidManager的事件
	FUDPClientThreadToAndroidManager::Get().OnSyncMapDelegate.RemoveAll(this);

	// 停止记录和回放
	if (bIsRecording)
	{
		StopRecording();
	}
	StopPlayback();
	

	// 停止所有数据源
	StopCurrentDataSource();

	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] EndPlay() End"), this);
}

void ANetworkManager::StartUDPClient(const FString& IP, int32 Port)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StartUDPClient() Begin - IP: %s, Port: %d"), this, *IP, Port);
	
	if (CurrentDataSource != EDataSource::UDP_RECEIVER)
	{
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Cannot start UDP client: Current data source is not UDP_RECEIVER"));
		return;
	}

	if (bIsClientRunning)
	{
		CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] UDP Client is already running"), this);
	
		if (bIsClientRunning && ServerIP == IP && ServerPort == Port)
		{
			CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] UDP Client is already running with same IP and Port"), this);
			return;
		}

		CARLINX_LOG(LogCarLinx, Warning, TEXT("[0x%p] UDP Client is already running with new UDP IP and Port, stop old first"), this);

		StopUDPClient();
		while (bIsClientRunning)
		{
			FPlatformProcess::Sleep(0.01f);
		}
	}

	// 保存IP和端口信息
	ServerIP = IP;
	ServerPort = Port;

	bIsClientRunning = true;
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Starting new UDP Client with IP: %s and Port: %d"), this, *IP, Port);
	FUDPClientThreadManager::Get().StartThread(IP, Port);
	FUDPClientThreadToAndroidManager::Get().StartThread(IP, 10005);
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StartUDPClient() End"), this);
}

void ANetworkManager::StopUDPClient()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StopUDPClient() Begin"), this);
	if (bIsClientRunning)
	{
		// 停止UDP客户端
		FUDPClientThreadManager::Get().StopThread();
		FUDPClientThreadToAndroidManager::Get().StopThread();
		// 等待线程完全停止
		FPlatformProcess::Sleep(0.1f);
		
		bIsClientRunning = false;
		CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] UDP Client stopped"), this);
	}
	else
	{
		CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] UDP Client is not running"), this);
	}
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StopUDPClient() End"), this);
}

void ANetworkManager::ResetHPARoadPointsData()
{
	RoadPoints.Empty();
	UParkingDataProcessManager::GetInstance()->ResetHPARoadPoints();
}

void ANetworkManager::ResetHPAFlag()
{
    //修改标记位，表示HPA建图过程中

    //FUDPClientThreadManager::Get().ResetHPAFlag();
	UGlobalConfig::GetInstance()->EnableHPACreatingFlag();

}


void ANetworkManager::OnDataReceived(const TMap<FString, FString>& ReceivedData)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] OnDataReceived() Begin"), this);
	OnDataReceivedBP(ReceivedData);
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] OnDataReceived() End"), this);
}

FVector ANetworkManager::GetMapValue(const FString& Ponintx, const FString& Poninty, const FString& Ponintz)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] GetMapValue() Begin"), this);
	float _x = FCString::Atof(*Ponintx);//         float _y = float _z =
	float _y = FCString::Atof(*Poninty);
	float _z = FCString::Atof(*Ponintz);
	FVector _P(_x, _y, _z);
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] GetMapValue() End"), this);
	return _P;
}

void ANetworkManager::OnSelfCarDataUpdated()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] OnSelfCarDataUpdated() Begin"), this);
	SelfCarMap = UParkingDataProcessManager::GetInstance()->GetSelfCarData();
	OnSelfCarDataUpdatedBP(SelfCarMap);
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] OnSelfCarDataUpdated() End"), this);
}

void ANetworkManager::OnSlotDataUpdated(int32 SlotNumber)
{
	// CARLINX_LOG(LogCarLinx, Log, TEXT("Slot: %d"), SlotNumber);
	SlotParkingMap[SlotNumber] = UParkingDataProcessManager::GetInstance()->GetSlotData(SlotNumber);
	OnSlotDataUpdatedBP(SlotNumber, SlotParkingMap[SlotNumber]);
}

void ANetworkManager::OnObstacleDataUpdated(int32 ObstacleNumber)
{
	// CARLINX_LOG(LogCarLinx, Log, TEXT("Obstacle: %d"), ObstacleNumber);
	ObstacleMap[ObstacleNumber] = UParkingDataProcessManager::GetInstance()->GetObstacleData(ObstacleNumber);
	OnObstacleDataUpdatedBP(ObstacleNumber, ObstacleMap[ObstacleNumber]);
}

void ANetworkManager::OnTrafficSignDataUpdated(int32 TrafficSignNumber)
{
	// CARLINX_LOG(LogCarLinx, Log, TEXT("TrafficSign: %d"), TrafficSignNumber);
	TrafficSignMap[TrafficSignNumber] = UParkingDataProcessManager::GetInstance()->GetTrafficSignData(TrafficSignNumber);
	OnTrafficSignDataUpdatedBP(TrafficSignNumber, TrafficSignMap[TrafficSignNumber]);
}

void ANetworkManager::OnRoadRightPointDataUpdated(int32 PointNumber)
{
	// CARLINX_LOG(LogCarLinx, Log, TEXT("RoadRightPoint: %d"), PointNumber);
	RoadRightPointMap[PointNumber] = UParkingDataProcessManager::GetInstance()->GetRoadRightPointData(PointNumber);
	OnRoadRightPointDataUpdatedBP(PointNumber, RoadRightPointMap[PointNumber]);
}

void ANetworkManager::OnRoadLeftPointDataUpdated(int32 PointNumber)
{
	// CARLINX_LOG(LogCarLinx, Log, TEXT("RoadLeftPoint: %d"), PointNumber);
	RoadLeftPointMap[PointNumber] = UParkingDataProcessManager::GetInstance()->GetRoadLeftPointData(PointNumber);
	OnRoadLeftPointDataUpdatedBP(PointNumber, RoadLeftPointMap[PointNumber]);
}

void ANetworkManager::OnPerspectiveModeUpdated()
{
	// CARLINX_LOG(LogCarLinx, Log, TEXT("PerspectiveMode"));
	PerspectiveMode = UParkingDataProcessManager::GetInstance()->GetPerspectiveModeData();
	OnPerspectiveModeUpdatedBP(PerspectiveMode);
}


void ANetworkManager::StartRecording()
{
	if (!bIsRecording && UGlobalConfig::GetInstance()->bEnableDataRecording)
	{
		if (UParkingDataSerializationManager::GetInstance()->StartRecording())
		{
			bIsRecording = true;
			CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Recording started successfully"), this);
		}
	}
}

void ANetworkManager::StopRecording()
{
	if (bIsRecording)
	{
		UParkingDataSerializationManager::GetInstance()->StopRecording();
		bIsRecording = false;
		CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Recording stopped"), this);
	}
}


void ANetworkManager::SwitchDataSource(EDataSource NewSource, const FString& FilePath)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("Current datasource: %s, New datasource: %s, current parking mode: %s"), 
		*UEnum::GetValueAsString(CurrentDataSource),
		*UEnum::GetValueAsString(NewSource),
		*UEnum::GetValueAsString(UGlobalConfig::GetInstance()->GetCurrentState()));
		
	if (CurrentDataSource == NewSource)
	{
		CARLINX_LOG(LogCarLinx, Warning, TEXT("[0x%p] Already using this data source"), this);
		return;
	}

	// 停止当前数据源
	StopCurrentDataSource();

	// 等待所有数据源完全停止
	while (bIsClientRunning )
	{
		FPlatformProcess::Sleep(0.01f);
	}

	// 清理所有数据
	CleanupOldData();

	// 切换到新数据源
	EDataSource OldSource = CurrentDataSource;
	CurrentDataSource = NewSource;

	// 启动新数据源
	bool startSuccess = false;
	if (NewSource == EDataSource::UDP_RECEIVER)
	{
		StartUDPReceiver();
		StartRecording();
		startSuccess = bIsClientRunning;
	}
	else if (NewSource == EDataSource::FILE_PLAYBACK)
	{
		// 此处不启动，等待蓝图调用“开始重放”
		// StartFilePlayback(FilePath.IsEmpty() ? PlaybackFilePath : FilePath);
		//startSuccess = bIsPlaying;
	}

	// if (!startSuccess)
	// {
	// 	CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to start new data source, reverting to previous source"), this);
	// }

	CARLINX_LOG(LogCarLinx, Log, TEXT("Current datasource: %s, New datasource: %s, current parking mode: %s"), 
		*UEnum::GetValueAsString(CurrentDataSource),
		*UEnum::GetValueAsString(NewSource),
		*UEnum::GetValueAsString(UGlobalConfig::GetInstance()->GetCurrentState()));

	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] SwitchDataSource() End - Current source: %s"), this, 
		*UEnum::GetValueAsString(CurrentDataSource));
}

void ANetworkManager::StartUDPReceiver()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StartUDPReceiver() Begin"), this);
	
	// 确保之前的UDP客户端已完全停止
	if (bIsClientRunning)
	{
		StopUDPClient();
		// 等待UDP客户端完全停止
		while (bIsClientRunning)
		{
			FPlatformProcess::Sleep(0.01f);
		}
	}

	// 使用默认IP和端口如果未设置
	if (ServerIP.IsEmpty())
	{
		ServerIP = TEXT("127.0.0.1");
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Using default IP: %s"), *ServerIP);
	}
	if (ServerPort == 0)
	{
		ServerPort = 10000;
		CARLINX_LOG(LogCarLinx, Warning, TEXT("Using default Port: %d"), ServerPort);
	}

	StartUDPClient(ServerIP, ServerPort);
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StartUDPReceiver() End"), this);
}

void ANetworkManager::StartFilePlayback(const FString& FilePath)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StartFilePlayback() Begin"), this);
	if (!bIsPlaying)
	{
		// 确保UDP客户端已停止
		if (bIsClientRunning)
		{
			StopUDPClient();
		}

		// 等待UDP客户端完全停止
		while (bIsClientRunning)
		{
			FPlatformProcess::Sleep(0.01f);
		}

		StartPlayback(FilePath);
		CARLINX_LOG(LogCarLinx, Log, TEXT("Started file playback from: %s"), *FilePath);
	}
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StartFilePlayback() End"), this);
}

void ANetworkManager::StopCurrentDataSource()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StopCurrentDataSource() Begin - Current source: %d"), this, 
		static_cast<int32>(CurrentDataSource));

	if(CurrentDataSource == EDataSource::FILE_PLAYBACK) 
	{
		if (bIsPlaying)
		{
			StopPlayback();
			// 等待播放完全停止
			while (bIsPlaying)
			{
				FPlatformProcess::Sleep(0.01f);
			}
		}
	} 
	else if(CurrentDataSource == EDataSource::UDP_RECEIVER) 
	{
		if (bIsClientRunning)
		{
			StopUDPClient();
			// 等待UDP客户端完全停止
			while (bIsClientRunning)
			{
				FPlatformProcess::Sleep(0.01f);
			}
		}
		// 停止录制
		StopRecording();
	}

	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] StopCurrentDataSource() End"), this);
}

void ANetworkManager::StartPlayback(const FString& FilePath)
{
	
	if (UParkingDataDeSerializationManager::GetInstance()->StartPlayback(true, FilePath))
	{
		// 启动成功，开始自动播放
	}
	else
	{
		check(0);
	}
}

void ANetworkManager::StartLoopPlayback()
{
	UParkingDataDeSerializationManager::GetInstance()->StartPlaybackLoop(TEXT(R"(D:\UE_Log\SR\20250421\SR_DATA_18-33-49_APA.dat)"));
}

void ANetworkManager::StartAutoPlayback()
{
	UParkingDataDeSerializationManager::GetInstance()->StartPlayback(true);
}

void ANetworkManager::StopPlayback()
{
	UParkingDataDeSerializationManager::GetInstance()->StopPlayback();
}

void ANetworkManager::PausePlayback()
{
	UParkingDataDeSerializationManager::GetInstance()->PausePlayback();
}

void ANetworkManager::ResumePlayback()
{
	UParkingDataDeSerializationManager::GetInstance()->ResumePlayback();
}

void ANetworkManager::RestartPlayback()
{
	// if (CurrentDataSource == EDataSource::FILE_PLAYBACK)
	// {
	// 	bIsPlaying = true;
	// 	UParkingDataDeSerializationManager::GetInstance()->RestartPlayback();
	// }
}

void ANetworkManager::PlayNextFrame()
{
	if (CurrentDataSource == EDataSource::FILE_PLAYBACK)
	{
		UParkingDataDeSerializationManager* DeSerializationManager = UParkingDataDeSerializationManager::GetInstance();
		
		DeSerializationManager->PlayNextFrame();
	}
}

void ANetworkManager::CleanupOldData()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] CleanupOldData() Begin"), this);

	// 清理自车数据
	SelfCarMap.Empty();
	OnSelfCarDataUpdatedBP(SelfCarMap);

	// 清理车位数据
	for (int32 i = 0; i < 32; ++i)
	{
		SlotParkingMap[i].Empty();
		OnSlotDataUpdatedBP(i, SlotParkingMap[i]);
	}

	// 清理障碍物数据
	for (int32 i = 0; i < 60; ++i)
	{
		ObstacleMap[i].Empty();
		OnObstacleDataUpdatedBP(i, ObstacleMap[i]);
	}

	// 清理交通标志数据
	for (int32 i = 0; i < 10; ++i)
	{
		TrafficSignMap[i].Empty();
		OnTrafficSignDataUpdatedBP(i, TrafficSignMap[i]);
	}

	// 清理道路点数据
	for (int32 i = 0; i < 100; ++i)
	{
		RoadRightPointMap[i].Empty();
		OnRoadRightPointDataUpdatedBP(i, RoadRightPointMap[i]);
		
		RoadLeftPointMap[i].Empty();
		OnRoadLeftPointDataUpdatedBP(i, RoadLeftPointMap[i]);
	}

	// 清理视角模式数据
	PerspectiveMode.Empty();
	OnPerspectiveModeUpdatedBP(PerspectiveMode);

	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] CleanupOldData() End"), this);
}

void ANetworkManager::SwitchParkingMode(EParkingState NewState)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("Current ParkingMode: %s, New ParkingMode: %s, Current DataSource: %s"), 
		*UEnum::GetValueAsString(UGlobalConfig::GetInstance()->GetCurrentState()),
		*UEnum::GetValueAsString(NewState),
		*UEnum::GetValueAsString(CurrentDataSource));
	// 切换泊车模式
	bool changed = UGlobalConfig::GetInstance()->SetCurrentState(NewState);
	if (changed)
	{
		// 状态切换时清空滤波器数据
		FilterAlgorithm::ClearAllData();
		
		// apa hpa lsda
		if (NewState >= EParkingState::APA)
		{
			// 根据当前数据源执行不同操作
			if (CurrentDataSource == EDataSource::UDP_RECEIVER)
			{
				StartRecording();
			}
			else if (CurrentDataSource == EDataSource::FILE_PLAYBACK)
			{
				// 在文件重放模式下，需要重新选择对应状态的文件
				// 如果当前正在播放，先停止
				if (bIsPlaying)
				{
					StopPlayback();
					// 等待播放完全停止
					while (bIsPlaying)
					{
						FPlatformProcess::Sleep(0.01f);
					}
				}

				// 此处不启动，等待蓝图调用“开始重放”
				// StartPlayback("");
			}
		}
		else if (NewState == EParkingState::AVM)
		{
			// 如果AVM，则停止录制，函数内会确认配置
			if (CurrentDataSource == EDataSource::UDP_RECEIVER)
			{
				StopRecording();
			}
		}
		else
		{
			// BACKGROUND模式，清理过期数据
			CleanupOldData();
			if (CurrentDataSource == EDataSource::UDP_RECEIVER)
			{
				StopRecording();
			}
		}
	}
}



void ANetworkManager::OnRoadPointUpdated()
{
	CARLINX_LOG(LogCarLinx,Log, TEXT("[0x%p] HPA Road Point OnRoadPointUpdated() Begin"), this);
	RoadPoints = UParkingDataProcessManager::GetInstance()->GetHPARoadPoints();
	OnRoadPointDataUpdateBP(RoadPoints);
	CARLINX_LOG(LogCarLinx,Log, TEXT("[0x%p] HPA Road Point OnRoadPointUpdated() End"), this);
}

void ANetworkManager::OnTrackPlanningPointsUpdated()
{
	CARLINX_LOG(LogCarLinx,Log, TEXT("[0x%p] OnTrackPlanningPointsUpdated() Begin"), this);
	TrackPlanningPoints = UParkingDataProcessManager::GetInstance()->GetTrackPlanningPoints();
	LineType = UParkingDataProcessManager::GetInstance()->GetLineType();
	// 调用蓝图事件
	OnTtrackPlanningLeftArray(TrackPlanningPoints, LineType);
	CARLINX_LOG(LogCarLinx,Log, TEXT("[0x%p] OnTrackPlanningPointsUpdated() End"), this);
}
void ANetworkManager::OnSyncMapUpdated(int32 Value)
{
	CARLINX_LOG(LogCarLinx,Log, TEXT("[0x%p] OnSyncMapUpdated() Begin"), this);
	OnSyncMapBP(Value);
	CARLINX_LOG(LogCarLinx,Log, TEXT("[0x%p] OnSyncMapUpdated() End"), this);
}

void ANetworkManager::OnSyncMapResponse(int32 CheckouMapList1, int32 CheckouMapList2, int32 CheckouMapList3, int32 CheckouMapList4)
{
	CARLINX_LOG(LogCarLinx,Log, TEXT("[0x%p] OnSyncMapResponse() Begin"), this);

	// 构建JSON对象
	TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
	JsonObject->SetNumberField("CheckouMapList1", CheckouMapList1);
	JsonObject->SetNumberField("CheckouMapList2", CheckouMapList2);
	JsonObject->SetNumberField("CheckouMapList3", CheckouMapList3);
	JsonObject->SetNumberField("CheckouMapList4", CheckouMapList4);

	// 将JSON对象转换为字符串
	FString JsonString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
	FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

	// 发送JSON字符串给Android
	FUDPClientThreadToAndroidManager::Get().SetMessage(JsonString);

}
