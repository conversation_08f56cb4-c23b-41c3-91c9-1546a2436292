// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "DrawLine.generated.h"

UCLASS()
class CARLINX_L946_UE_API ADrawLine : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	ADrawLine();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;
	
	UFUNCTION(BlueprintCallable)
	void SetLineValue(TArray<FVector> Inpoints, float Breadth);

	UPROPERTY(BlueprintReadOnly)
	TArray<FVector> LinePoints;

	UPROPERTY(BlueprintReadOnly)
	TArray<int> LineTriangles;

private:
	TArray<FVector> L ;
	float CathetusK = 0.0f;
};
