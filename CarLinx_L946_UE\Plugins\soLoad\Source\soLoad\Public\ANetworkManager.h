﻿#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "DataTypes.h"
#include "GlobalConfig.h"
#include "ANetworkManager.generated.h"

UCLASS()
class SOLOAD_API ANetworkManager : public AActor
{
	GENERATED_BODY()

public:
	ANetworkManager();
	virtual ~ANetworkManager();

	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

	UFUNCTION(BlueprintCallable, Category = "Networking")
		void StartUDPClient(const FString& IP, int32 Port);

	UFUNCTION(BlueprintCallable, Category = "Networking")
		void StopUDPClient();

	UFUNCTION(BlueprintImplementableEvent, Category = "Networking")
		void OnDataReceivedBP(const TMap<FString, FString>& ReceivedData);

	UPROPERTY(EditAnywhere, Config, BlueprintReadWrite, Category = "Networking")
		FString ServerIP;

	UPROPERTY(EditAnywhere, Config, BlueprintReadWrite, Category = "Networking")
		int32 ServerPort;

	TMap<FString, FString> ReceivedDataMap;

	UFUNCTION()
		void OnSelfCarDataUpdated();

	UFUNCTION()
		void OnSlotDataUpdated(int32 SlotNumber);

	UFUNCTION()
		void OnObstacleDataUpdated(int32 ObstacleNumber);

	UFUNCTION()
		void OnTrafficSignDataUpdated(int32 TrafficSignNumber);

	UFUNCTION()
		void OnRoadRightPointDataUpdated(int32 RoadRightPointNumber);

	UFUNCTION()
		void OnRoadLeftPointDataUpdated(int32 RoadLeftPointNumber);

	UFUNCTION()
		void OnPerspectiveModeUpdated();

	UFUNCTION()
		void OnRoadPointUpdated();

	UFUNCTION()
		void OnTrackPlanningPointsUpdated();

	UFUNCTION()
		void OnSyncMapUpdated(int32 Value);

	// 同步地图响应
	UFUNCTION(BlueprintCallable)
		void OnSyncMapResponse(int32 CheckouMapList1, int32 CheckouMapList2, int32 CheckouMapList3, int32 CheckouMapList4);


	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnSelfCarDataUpdatedBP(const TMap<FString, FString>& SelfCarData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnSlotDataUpdatedBP(int32 SlotNumber, const TMap<FString, FString>& SlotData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnObstacleDataUpdatedBP(int32 ObstacleNumber, const TMap<FString, FString>& ObstacleData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnTrafficSignDataUpdatedBP(int32 TrafficSignNumber, const TMap<FString, FString>& TrafficSignData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnRoadRightPointDataUpdatedBP(int32 RoadRightPointNumber, const TMap<FString, FString>& RoadRightPointData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnRoadLeftPointDataUpdatedBP(int32 RoadLeftPointNumber, const TMap<FString, FString>& RoadLeftPointData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnPerspectiveModeUpdatedBP(const TMap<FString, FString>& PerspectiveModeData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnTtrackPlanningLeftArray(const TArray<FSTCTRACK>& Points, int Type);

	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnSyncMapBP(int32 Value);

	// 鏁版嵁璁板綍鍜屽洖鏀炬帶鍒?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Recording")
		bool bEnableDataRecording;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Recording")
		bool bEnableDataPlayback;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Recording")
		FString PlaybackFilePath;

	// 褰撳墠鏁版嵁婧?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data Source")
		EDataSource CurrentDataSource{EDataSource::Null};

	// 鍒囨崲鏁版嵁婧?
	UFUNCTION(BlueprintCallable, Category = "Data Source")
		void SwitchDataSource(EDataSource NewSource, const FString& FilePath = "");

	// 鍒囨彌娉婅粖妯″紡
	UFUNCTION(BlueprintCallable, Category = "Data Source")
		void SwitchParkingMode(EParkingState NewMode);
	
	
	// 鏆傚仠閲嶆斁
	UFUNCTION(BlueprintCallable, Category = "Playback Control")
		void PausePlayback();
	
	// 鎭㈠閲嶆斁
	UFUNCTION(BlueprintCallable, Category = "Playback Control")
		void ResumePlayback();
	
	// 閲嶆柊寮€濮嬮噸鏀?
	
	UFUNCTION(BlueprintCallable, Category = "Playback Control",meta=(DeprecatedFunction, DeprecationMessage="已经弃用"))
		void RestartPlayback();

	// 寮€濮嬮噸鏀?
	UFUNCTION(BlueprintCallable, Category = "Playback Control")
		void StartPlayback(const FString& FilePath);
	
	UFUNCTION(BlueprintCallable, Category = "Playback Control")
	void StartLoopPlayback();

	// 寮€濮嬭嚜鍔ㄩ噸鏀?
	UFUNCTION(BlueprintCallable, Category = "Playback Control")
		void StartAutoPlayback();

	// 鍋滄閲嶆斁
	UFUNCTION(BlueprintCallable, Category = "Playback Control")
		void StopPlayback();

	// 鎾斁涓嬩竴甯?
	UFUNCTION(BlueprintCallable, Category = "Playback Control")
		void PlayNextFrame();

	// 娓呯悊鏁版嵁
	UFUNCTION(BlueprintCallable, Category = "Data Control")
		void CleanupOldData();

	// 寮€濮嬪綍鍒?
	UFUNCTION(BlueprintCallable, Category = "Data Recording")
		void StartRecording();

	// 鍋滄褰曞埗
	UFUNCTION(BlueprintCallable, Category = "Data Recording")
		void StopRecording();

	UFUNCTION(BlueprintImplementableEvent, Category = "Network")
		void OnRoadPointDataUpdateBP(const TArray<FRoadPoint>& Points);

	UFUNCTION(BlueprintCallable, Category = "Network")
		void ResetHPARoadPointsData();

	//每次点建图按键调用一次
	UFUNCTION(BlueprintCallable, Category = "Network")
		void ResetHPAFlag();

protected:
	bool bIsRecording;
	bool bIsPlaying;
	bool bIsClientRunning;
	

private:
	float LastTestButtonTime = 0.0f;

	void OnDataReceived(const TMap<FString, FString>& ReceivedData);
	TMap<FString, FString> SelfCarMap;
	TMap<FString, FString> SlotParkingMap[32];
	TMap<FString, FString> ObstacleMap[60];
	TMap<FString, FString> TrafficSignMap[10];
	TMap<FString, FString> RoadRightPointMap[100];
	TMap<FString, FString> RoadLeftPointMap[100];
	TMap<FString, FString> PerspectiveMode;
	TArray<FSTCTRACK> TrackPlanningPoints;
	FVector CarLocation;
	FRotator CarRotation;
	FVector ForwardLocation;
	FVector BackwardLocation;
	TArray<FRoadPoint> RoadPoints;
	int LineType;

	

private:

	FVector GetMapValue(const FString& Ponintx, const FString& Poninty, const FString& Ponintz);

	// 鍚姩UDP鎺ユ敹
	void StartUDPReceiver();
	
	// 鍚姩鏂囦欢鍥炴斁
	void StartFilePlayback(const FString& FilePath);
	
	// 鍋滄褰撳墠鏁版嵁婧?
	void StopCurrentDataSource();

};
