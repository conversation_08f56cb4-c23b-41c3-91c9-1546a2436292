#pragma once

#include "HAL/Runnable.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/ThreadSafeCounter.h"
#include "HAL/CriticalSection.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "IPAddress.h"
#include "Sockets.h"
#include "SocketSubsystem.h"
#include "Common/UdpSocketBuilder.h"
#include "Async/AsyncWork.h"
#include "DataTypes.h"

#include <vector>
#include "CoreMinimal.h"
#include "ParkingDataManager.h"
#include <array>
#include <memory>
#include "VehValtPrkgHmiGenr_minimal_types.h"
#include "./GlobalConfig.h"

class FRunnableThread;
class FSocket;
struct FIPv4Endpoint;
class UGlobalConfig;

// UDP客户端线程类
class SOLOAD_API FUDPClientThread : public FRunnable
{
public:
	FUDPClientThread(const FString& IP, int32 Port);
	virtual ~FUDPClientThread();

	// FRunnable接口
	virtual bool Init() override;
	virtual uint32 Run() override;
	virtual void Stop() override;
	virtual void Exit() override;

protected:
	// 数据处理函数
	void ProcessReceivedData(const std::vector<uint8_t>& Data);
	
	
	// 辅助函数
	void Cleanup();
	FString GetTime();
	void InitMap();
	void mapKeyAdd(TMap<FString, FString>& InputMap, FString Key);
	bool UpdateMapValue(TMap<FString, FString>& Map, const FString& Key, const FString& Value);
	bool TryReconnect();
	void HandleConnectionError();

private:
	// 网络相关成员
	TArray<uint8> ReceivedData;
	FIPv4Endpoint Sender;
	FString IP;
	int32 Port;
	FSocket* Socket;
	FIPv4Endpoint RemoteEndpoint;

	// 线程相关成员
	FThreadSafeBool bShouldRun{true};
	FRunnableThread* Thread{nullptr};
	FThreadSafeCounter RetryCount{0};
	FThreadSafeCounter ProcessedFrameCount{0};
	double LastProcessTime{0.0};

    static const int32 BUFFER_SIZE = 1024 * 500;

	// 数据存储
	TMap<FString, FString> SelfCarMap;
	TMap<FString, FString> SlotParkingMap[32];
	TMap<FString, FString> ObstacleMap[60];
	TMap<FString, FString> TrafficSignMap[10];
	TMap<FString, FString> RoadRightPointMap[100];
	TMap<FString, FString> RoadLeftPointMap[100];
	TArray<FVector> LeftPoints;

	// 禁止拷贝和赋值
	FUDPClientThread(const FUDPClientThread&) = delete;
	FUDPClientThread& operator=(const FUDPClientThread&) = delete;

    // 修改函数声明，移除Map参数
    void DeserializeSelfCarData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& selfData);
    void DeserializeSlotData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot& slotData, int32 SlotNumber);
	
	
    // 日志输出函数
    void LogSelfCarData(const TMap<FString, FString>& Map);
    void LogSlotData(int32 SlotNumber, const TMap<FString, FString>& Map);
    void LogObstacleData(int32 ObstacleNumber, const TMap<FString, FString>& Map);
    void LogTrafficSignData(int32 SignNumber, const TMap<FString, FString>& Map);
    void LogRoadPointData(bool bIsRight, int32 PointNumber, const TMap<FString, FString>& Map);
    void LogPerspectiveModeData(const TMap<FString, FString>& Map);
    void LogTrackPlanningData(bool bIsLeft, int32 PointNumber, const TMap<FString, FString>& Map);

};

// 线程管理器类
class SOLOAD_API FUDPClientThreadManager
{
public:
	static FUDPClientThreadManager& Get();
	void StartThread(const FString& IP, int32 Port);
	void StopThread();

	
public:
	FUDPClientThread* ClientThread;

private:
	FUDPClientThreadManager();
	~FUDPClientThreadManager();

	// 禁止拷贝和赋值
	FUDPClientThreadManager(const FUDPClientThreadManager&) = delete;
	FUDPClientThreadManager& operator=(const FUDPClientThreadManager&) = delete;
};