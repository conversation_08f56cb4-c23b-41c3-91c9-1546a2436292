

[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=38E1F9E1454BB8D382AB0385A27973F8

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Shipping
BuildTarget=
StagingDirectory=(Path="D:/Project/ue4_only/CarLinx_L946_UE")
FullRebuild=False
ForDistribution=False
IncludeDebugFiles=False
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bUseIoStore=False
bMakeBinaryConfig=False
bGenerateChunks=False
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=False
MaxChunkSize=0
bBuildHttpChunkInstallData=False
HttpChunkInstallDataDirectory=(Path="")
bCompressed=True
PakFileCompressionFormats=Oodle
bForceUseProjectCompressionFormatIgnoreHardwareOverride=False
PakFileAdditionalCompressionOptions=-compressionblocksize=256KB
PakFileCompressionMethod=Kraken
PakFileCompressionLevel_DebugDevelopment=3
PakFileCompressionLevel_TestShipping=5
PakFileCompressionLevel_Distribution=7
HttpChunkInstallDataVersion=
IncludePrerequisites=True
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=True
bDeterministicShaderCodeOrder=False
bSharedMaterialNativeLibraries=True
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=False
InternationalizationPreset=English
-CulturesToStage=en
+CulturesToStage=en
LocalizationTargetCatchAllChunkId=0
bCookAll=False
bCookMapsOnly=False
bSkipEditorContent=False
bSkipMovies=False
-IniKeyBlacklist=KeyStorePassword
-IniKeyBlacklist=KeyPassword
-IniKeyBlacklist=rsa.privateexp
-IniKeyBlacklist=rsa.modulus
-IniKeyBlacklist=rsa.publicexp
-IniKeyBlacklist=aes.key
-IniKeyBlacklist=SigningPublicExponent
-IniKeyBlacklist=SigningModulus
-IniKeyBlacklist=SigningPrivateExponent
-IniKeyBlacklist=EncryptionKey
-IniKeyBlacklist=IniKeyBlacklist
-IniKeyBlacklist=IniSectionBlacklist
+IniKeyBlacklist=KeyStorePassword
+IniKeyBlacklist=KeyPassword
+IniKeyBlacklist=rsa.privateexp
+IniKeyBlacklist=rsa.modulus
+IniKeyBlacklist=rsa.publicexp
+IniKeyBlacklist=aes.key
+IniKeyBlacklist=SigningPublicExponent
+IniKeyBlacklist=SigningModulus
+IniKeyBlacklist=SigningPrivateExponent
+IniKeyBlacklist=EncryptionKey
+IniKeyBlacklist=IniKeyBlacklist
+IniKeyBlacklist=IniSectionBlacklist
+DirectoriesToAlwaysStageAsNonUFS=(Path="Version")

