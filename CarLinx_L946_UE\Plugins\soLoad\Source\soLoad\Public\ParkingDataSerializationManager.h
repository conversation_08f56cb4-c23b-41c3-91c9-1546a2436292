#pragma once

// 基础UE4头文件
#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "Containers/Array.h"
#include "Containers/UnrealString.h"
#include "HAL/FileManager.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "TimerManager.h"

// 项目头文件
#include "DataTypes.h"
#include "FileWriterThread.h"
#include "GlobalConfig.h"

// 生成的头文件必须放在最后
#include "ParkingDataSerializationManager.generated.h"

UCLASS(Blueprintable)
class SOLOAD_API UParkingDataSerializationManager : public UObject {
    GENERATED_BODY()

public:
    UParkingDataSerializationManager();
    virtual ~UParkingDataSerializationManager();
    
    static UParkingDataSerializationManager* GetInstance();
    
    // 开始记录数据
    UFUNCTION(BlueprintCallable, Category = "Parking Data")
    bool StartRecording();
    
    // 停止记录数据
    UFUNCTION(BlueprintCallable, Category = "Parking Data")
    void StopRecording();
    
    // 记录一帧数据
    void RecordFrame(const uint8_t* Data, uint32_t DataSize);

private:
    UPROPERTY()
    FString BaseFilePath;  // 基础文件路径
    
    UPROPERTY()
    FString CurrentFilePath;  // 当前使用的文件路径
    
    UPROPERTY()
    int32 CurrentPlaybackFrame;
    
    TUniquePtr<FFileWriterThread> FileWriterThread;

    // 根据状态生成文件路径
    FString GenerateFilePathForState();

    // 切换状态时更新文件
    void UpdateRecordingFile();
};