#ifndef VIRTUALWORLDMANAGER_H_
#define VIRTUALWORLDMANAGER_H_

#include <functional>
#include <memory>
#include "VehValtPrkgHmiGenr_types.h"

#ifdef __cplusplus
extern "C"
{
#endif

    namespace VirtualWorldManager
    {
        //void StartBinfile();

        void InitSocket();

        void CreateNewThread();

        int InitClient();

        void print(int8_t Posn);

        void SendPrkgSelfVehEvtValue(int8_t PosnZ, int16_t PosnY, int16_t PosnX, int16_t Roll, int16_t Pitch, int16_t Yaw, uint8_t VehWave, int8_t Direction, int16_t headRoll);
        // void SendPrkgFloorEvtValue(int8_t val);
        // void SendPrkgPerspectiveModeEvtValue(uint8_t val);
        // void SendPrkgRoutePlanningEvtValue(int val);
        // void SendPrkgPrkgTimestampEvtValue(uint64_t val);

        // void SendPrkgTrafficSignEvtValue(int val);
        // void SendPrkgResEvtValue(uint32_t val);
        // void SendPrkgMeyPrkgSpeEvtValue(int val);
        // void SendPrkgDestPosnEvtValue(int val);
        // void SendPrkgObstacleEvtValue(int val);

        void SendPrkgSlotEvtValue(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot val);
        // void SendPrkgRoadRightPointEvtValue(int val);
        // void SendPrkgRoadLeftPointEvtValue(int val);
        // void SendPrkgTtrackPlanningLArrEvtValue(int val);
        // void SendPrkgTtrackPlanningRArrEvtValue(int val);

        // UE中注册回调函数
        void RegisterPrkgSelfVehEvtCallback(std::function<void(int8_t, int16_t, int16_t, int16_t, int16_t, int16_t, uint8_t, int8_t, int16_t)> cb);
        //void RegisterPrkgSelfVehEvtCallback(void (*callback)(int8_t, int16_t, int16_t, int16_t, int16_t, int16_t, uint8_t, int8_t, int16_t));
        // void RegisterPrkgFloorEvtCallback(std::function<void(int8_t)> cb);
        // void RegisterPrkgPerspectiveModeEvtCallback(std::function<void(uint8_t)> cb);
        // void RegisterPrkgRoutePlanningEvtCallback(std::function<void(int)> cb);
        // void RegisterPrkgPrkgTimestampEvtCallback(std::function<void(uint64_t)> cb);

        // void RegisterPrkgTrafficSignEvtCallback(std::function<void(int)> cb);
        // void RegisterPrkgResEvtCallback(std::function<void(uint32_t)> cb);
        // void RegisterPrkgMeyPrkgSpeEvtCallback(std::function<void(int)> cb);
        // void RegisterPrkgDestPosnEvtCallback(std::function<void(int)> cb);
        // void RegisterPrkgObstacleEvtCallback(std::function<void(int)> cb);

        void RegisterPrkgSlotEvtCallback(std::function<void(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot&)> cb);
        // void RegisterPrkgRoadRightPointEvtCallback(std::function<void(int)> cb);
        // void RegisterPrkgRoadLeftPointEvtCallback(std::function<void(int)> cb);
        // void RegisterPrkgTtrackPlanningLArrEvtCallback(std::function<void(int)> cb);
        // void RegisterPrkgTtrackPlanningRArrEvtCallback(std::function<void(int)> cb);

        

    };

#ifdef __cplusplus
}
#endif

#endif