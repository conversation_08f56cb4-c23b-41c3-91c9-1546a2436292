// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "ProceduralMeshComponent.h"
#include "UObject/UObjectGlobals.h"
#include "KismetProceduralMeshLibrary.h"
#include "Kismet/KismetMaterialLibrary.h"
#include "TrackActor.generated.h"

UCLASS()
class TRACK_API ATrackActor : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	ATrackActor();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	UFUNCTION(BluePrintCallable, Category = "TrackActor")
	void BeginUpdate();

	UFUNCTION(BluePrintCallable, Category = "TrackActor")
	void AddPoint(const FVector& Point);

	UFUNCTION(BluePrintCallable, Category = "TrackActor")
	void EndUpdate();

	UFUNCTION(BluePrintCallable, Category = "TrackActor")
	void SetMeshSpacing(float InMeshSpacing);

	UFUNCTION(BluePrintCallable, Category = "TrackActor")
	void SetPoints();

	UFUNCTION(BluePrintCallable, Category = "TrackActor")
	void SetTrackInterp(FString TrackInterp);

//圆形拐角函数
public:

	//已知点集，计算两条平行点集
	UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	void SetTrackPoint();

	//绘制Mesh
	UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	void DrawMesh();

	//设置线段是否弯曲
	UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	void SetbCurve(bool bIsCurve);

	 UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	void SetPointsEXT(const TArray<FVector>& Paths);

	UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	void SetTypeEXT(const FString& NewTrackType);

	UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	void SetInterpEXT(const FString& NewInterp);

	UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	void SetSizeEXT(const float& NewSize);

	UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	void SetHighlightEXT(const bool& NewbHighLight);

	UFUNCTION(BluePrintCallable, Category = "PSVectorRectangle")
	void SetHighLightColorEXT(const FString& NewHighLightColor);

//圆形函数
private:
	//获取曲率的点集
	TArray<FVector> CalcaulatePointCurvature(const TArray<FVector>& InPoints, const TArray<FVector>& InPoints1, float Size);

	//获取直线的点集
	TArray<FVector> CalcaulatePoint(const TArray<FVector>& InPoints, const TArray<FVector>& InPoints1);

	//计算圆上两点的距离
	FVector GetAngle(FVector Point1, FVector Point2, float Radius, FVector CircleCenter);

	//两条线的交点坐标
	FVector Getintersect(float K1, float B1, float K2, float B2, float Z);

	//1计算点集的总长度
	float CalculatePrimater(TArray<FVector> Points);

	//计算距离点一定长度的点
	FVector GetDistanceForPoint(FVector PointStart, FVector PointEnd, float Distance);

	//计算圆心和半径
	//point1,point2圆上两点
	//K1,K2圆切线K值
	//bNoK1,bNoK2切线是否有K值，true为无K值
	//B1，B2切线B值
	//R，Center返回圆的半径和圆心坐标
	void GetCercleCenter(FVector Point1, FVector Point2, float K1, float K2, float B1, float B2, bool bNoK1, bool bNoK2, float& R, FVector2D& Center);

public:
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "TrackActor")
	UProceduralMeshComponent* ProcMeshComponent;

private:

	void ClearSplineMesh(bool bProcMeshVisible);

//圆形变量
private:
    // 轨迹宽度
	float LineSize;


	//左右路径数组
	UPROPERTY()
		TArray<FVector> RightCurvePoint;

	UPROPERTY()
		TArray<FVector> LeftCurvePoint;

	UPROPERTY()
		bool bCurve;

	float LineK = 1, LeftB = 1, RightB = 1, LeftUp = 1, RightUp = 1;


	//线是否垂直X轴
	bool IsUp = false;



	//顶点集
	TArray<FVector> VertexArr;
	//顶点Index
	TArray<int32> TriangleArr;
	//法线
	TArray<FVector> NormalArr;
	//UVs
	TArray<FVector2D> UVArr;
	//切线
	TArray<FProcMeshTangent> TangentArr;

	//记录拐弯的点
	TMap<int32, bool> CurvrPointStart;

	//曲线点的数量
	int32 CurvePointNum;

public:
	UPROPERTY(BlueprintReadWrite, Category = "TrackActor")
		TArray<UMeshComponent*> SplineMeshComponents;

private:

	UPROPERTY()
		float MeshSpacing;

	UPROPERTY()
		USplineComponent* SplineComponent;



	UPROPERTY(EditAnywhere, Category = "TrackActor")
		UStaticMesh* TrackMesh;

	UPROPERTY()
		UStaticMesh* TrackMeshColumnar;

	UPROPERTY()
		UMaterialInstanceDynamic* TrackDynamicMat;

	UPROPERTY()
		bool bIsColumnar;
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "TrackActor")
		UMaterialInstance* TrackMat;
			
	FString EXTTrackType;
	FString EXTInterp;
	float EXTSize;
	bool EXTbHighLight;
	FString EXTHighLightColor;
	TArray<FVector> EXTPaths;
	bool HighLight;

	FString HighLightColor;
		
	float RoundSize;

	UPROPERTY(BlueprintReadWrite, Category = "TrackActor")
	float PointPrimater;

};
