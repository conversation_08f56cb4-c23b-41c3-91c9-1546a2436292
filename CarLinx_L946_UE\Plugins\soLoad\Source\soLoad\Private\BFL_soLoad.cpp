#include "BFL_soLoad.h"
#include "Engine/Engine.h"
#include "Logger.h"
#include "HAL/PlatformProperties.h"
#include "Misc/EngineVersion.h"
#include "GenericPlatform/GenericPlatformMisc.h"
#include "HAL/PlatformMemory.h"
#include "Misc/ConfigCacheIni.h"
#include "Containers/UnrealString.h"
#include "Misc/Paths.h"
#include "Internationalization/text.h"
#include "Misc/App.h"
#include "HAL/PlatformTime.h"
#include "GlobalConfig.h"

// 初始化静态成员
const float UBFL_soLoad::CPU_SAMPLE_INTERVAL = 0.1f;  // 100ms采样间隔
const float UBFL_soLoad::LOG_INTERVAL = 1.0f;         // 1s日志间隔
TArray<UBFL_soLoad::FCPUSample> UBFL_soLoad::SystemCPUSamples;
TArray<UBFL_soLoad::FCPUSample> UBFL_soLoad::ProcessCPUSamples;

FCriticalSection UBFL_soLoad::SampleDataLock;
double UBFL_soLoad::LastSampleTime = 0.0;
double UBFL_soLoad::LastLogTime = 0.0;

float UBFL_soLoad::GetCurrentFPS()
{
    extern ENGINE_API float GAverageFPS;
    return GAverageFPS;
}

float UBFL_soLoad::GetCurrentFrameTime()
{
    // extern ENGINE_API float GGameThreadTime;
    // return GGameThreadTime;
    return 0;
}

float UBFL_soLoad::GetAverageFrameTime()
{
    extern ENGINE_API float GAverageMS;
    return GAverageMS;
}

void UBFL_soLoad::UpdateCPUSamples()
{
    double CurrentTime = FPlatformTime::Seconds();
    
    // 检查是否需要采样
    if (CurrentTime - LastSampleTime < CPU_SAMPLE_INTERVAL)
    {
        return;
    }
    
    // 获取当前CPU时间
    FCPUTime CurrentCPUTime = FPlatformTime::GetCPUTime();
    
    // 创建新的采样
    FCPUSample NewSystemSample;
    NewSystemSample.TimeStamp = CurrentTime;
    NewSystemSample.CPUTime = CurrentCPUTime.CPUTimePct;
    
    FCPUSample NewProcessSample;
    NewProcessSample.TimeStamp = CurrentTime;
    NewProcessSample.CPUTime = CurrentCPUTime.CPUTimePct; // 修改为CPUTimePct
    
    // 如果有前一个采样，计算使用率
    {
        FScopeLock Lock(&SampleDataLock);
        
        if (SystemCPUSamples.Num() > 0)
        {
            const FCPUSample& LastSystemSample = SystemCPUSamples.Last();
            const FCPUSample& LastProcessSample = ProcessCPUSamples.Last();
        
            double TimeInterval = CurrentTime - LastSystemSample.TimeStamp;
            if (TimeInterval > 0)
            {
                NewSystemSample.Usage = static_cast<float>((NewSystemSample.CPUTime - LastSystemSample.CPUTime) / TimeInterval);
                NewProcessSample.Usage = static_cast<float>((NewProcessSample.CPUTime - LastProcessSample.CPUTime) / TimeInterval);
            }
        }
    
        // 添加新采样
        {
            SystemCPUSamples.Add(NewSystemSample);
            ProcessCPUSamples.Add(NewProcessSample);
        
            // 保持采样数量在限制内
            if (SystemCPUSamples.Num() > CPU_SAMPLE_COUNT)
            {
                SystemCPUSamples.RemoveAt(0);
                ProcessCPUSamples.RemoveAt(0);
            }
        }
    }
    
    
    
    
    LastSampleTime = CurrentTime;
}

float UBFL_soLoad::CalculateAverageUsage(const TArray<FCPUSample>& Samples)
{
    if (Samples.Num() < 2)
    {
        return 0.0f;
    }
    
    float TotalUsage = 0.0f;
    int32 ValidSamples = 0;
    
    for (int32 i = 1; i < Samples.Num(); ++i)
    {
        if (Samples[i].Usage >= 0.0f)
        {
            TotalUsage += FMath::Clamp(Samples[i].Usage, 0.0f, 1.0f);
            ValidSamples++;
        }
    }
    
    return ValidSamples > 0 ? TotalUsage / ValidSamples : 0.0f;
}

float UBFL_soLoad::GetSystemCPUUsage()
{
    //UpdateCPUSamples();
    return CalculateAverageUsage(SystemCPUSamples);
}

float UBFL_soLoad::GetProcessCPUUsage()
{
    //UpdateCPUSamples();
    return CalculateAverageUsage(ProcessCPUSamples);
}

FString UBFL_soLoad::GetUEVersion()
{
    return FEngineVersion::Current().ToString();
}

void UBFL_soLoad::LogEngineInfo()
{
    if (!UGlobalConfig::GetInstance()->GetEnablePerformanceLog())
    {
        return;
    }

    CARLINX_LOG(LogCarLinx, Log, TEXT("Engine Info:"));
    CARLINX_LOG(LogCarLinx, Log, TEXT("UE Version:\t%s"), *GetUEVersion());
    CARLINX_LOG(LogCarLinx, Log, TEXT("Build Configuration: %s"), LexToString(FApp::GetBuildConfiguration()));
    CARLINX_LOG(LogCarLinx, Log, TEXT("Build Target:\t%s"), *FString(FPlatformProperties::IniPlatformName()));
    CARLINX_LOG(LogCarLinx, Log, TEXT("Engine Mode:\t%s"), FApp::IsGame() ? TEXT("Game") : TEXT("Editor"));
}

FString UBFL_soLoad::GetPlatformInfo()
{
    return FString::Printf(TEXT("%s %s"),
        *FString(FPlatformProperties::PlatformName()),
        *FString(FPlatformMisc::GetOSVersion()));
}

void UBFL_soLoad::LogSystemStaticInfo()
{
    if (!UGlobalConfig::GetInstance()->GetEnablePerformanceLog())
    {
        return;
    }

    CARLINX_LOG(LogCarLinx, Log, TEXT("System Static Info:"));
    CARLINX_LOG(LogCarLinx, Log, TEXT("Platform: \t%s"), *GetPlatformInfo());
    CARLINX_LOG(LogCarLinx, Log, TEXT("CPU: \t%s"), *FString(FPlatformMisc::GetCPUBrand()));
    CARLINX_LOG(LogCarLinx, Log, TEXT("CPU Cores: \t%d"), FPlatformMisc::NumberOfCores());
    
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    CARLINX_LOG(LogCarLinx, Log, TEXT("Total Physical Memory: \t%.2f MB"), MemStats.TotalPhysical / (1024.0f * 1024.0f));
    CARLINX_LOG(LogCarLinx, Log, TEXT("Total Virtual Memory: \t%.2f MB"), MemStats.TotalVirtual / (1024.0f * 1024.0f));
}

void UBFL_soLoad::LogSystemDynamicInfo()
{
    if (!UGlobalConfig::GetInstance()->GetEnablePerformanceLog())
    {
        return;
    }

    extern ENGINE_API float GAverageFPS;
    extern ENGINE_API float GAverageMS;

    CARLINX_LOG(LogCarLinx, Log, TEXT("Performance Info:"));
    CARLINX_LOG(LogCarLinx, Log, TEXT("FPS: %.2f, Avg Frame Time: %.2f ms"), GAverageFPS, GAverageMS);
    
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();

    CARLINX_LOG(LogCarLinx, Log, TEXT("System Dynamic Info:"));
    CARLINX_LOG(LogCarLinx, Log, TEXT("Process CPU Usage: \t\t\t%.1f%%"), GetProcessCPUUsage() * 100.0f);
    CARLINX_LOG(LogCarLinx, Log, TEXT("System CPU Usage: \t\t\t%.1f%%"), GetSystemCPUUsage() * 100.0f);
    CARLINX_LOG(LogCarLinx, Log, TEXT("Available Physical Memory: \t%.2f MB"), MemStats.AvailablePhysical / (1024.0f * 1024.0f));
    CARLINX_LOG(LogCarLinx, Log, TEXT("Used Virtual Memory: \t\t%.2f MB"), MemStats.UsedVirtual / (1024.0f * 1024.0f));
    CARLINX_LOG(LogCarLinx, Log, TEXT("Peak Used Virtual Memory: \t%.2f MB"), MemStats.PeakUsedVirtual / (1024.0f * 1024.0f));
}

void UBFL_soLoad::LogAllStaticInfo()
{
    if (!UGlobalConfig::GetInstance()->GetEnablePerformanceLog())
    {
        return;
    }

    CARLINX_LOG(LogCarLinx, Log, TEXT("================ Static Information ================"));
    LogEngineInfo();
    LogSystemStaticInfo();
    CARLINX_LOG(LogCarLinx, Log, TEXT("================================================"));
}

void UBFL_soLoad::LogDynamicInfoImpl()
{
    if (!UGlobalConfig::GetInstance()->GetEnablePerformanceLog())
    {
        return;
    }

    CARLINX_LOG(LogCarLinx, Log, TEXT("---------------- Dynamic Information ----------------"));
    LogSystemDynamicInfo();
    CARLINX_LOG(LogCarLinx, Log, TEXT("--------------------------------------------------"));
}

void UBFL_soLoad::LogAllDynamicInfo()
{
    double CurrentTime = FPlatformTime::Seconds();
    
    // 检查是否需要打印日志
    if (CurrentTime - LastLogTime < LOG_INTERVAL)
    {
        return;
    }
    
    LogDynamicInfoImpl();
    LastLogTime = CurrentTime;
}

void UBFL_soLoad::ForceLogAllDynamicInfo()
{
    LogDynamicInfoImpl();
    LastLogTime = FPlatformTime::Seconds();
}


void UBFL_soLoad::LogPerformanceInfo()
{
    if (!UGlobalConfig::GetInstance()->GetEnablePerformanceLog())
    {
        return;
    }

    extern ENGINE_API float GAverageFPS;
    extern ENGINE_API float GAverageMS;

    CARLINX_LOG(LogCarLinx, Log, TEXT("Performance Info:"));
    CARLINX_LOG(LogCarLinx, Log, TEXT("FPS: %.2f, Avg Frame Time: %.2f ms"), GAverageFPS, GAverageMS);

    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    CARLINX_LOG(LogCarLinx, Log, TEXT("Process CPU Usage: %.1f%%"), GetProcessCPUUsage() * 100.0f);
    CARLINX_LOG(LogCarLinx, Log, TEXT("System CPU Usage: %.1f%%"), GetSystemCPUUsage() * 100.0f);
}

void UBFL_soLoad::LogCustomString(const FString& Message)
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[BP] %s"), *Message);
}