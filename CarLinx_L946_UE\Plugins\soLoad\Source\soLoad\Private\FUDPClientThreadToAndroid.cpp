#include "../Public/FUDPClientThreadToAndroid.h"

// UE4 Core headers
#include "CoreMinimal.h"
#include "HAL/PlatformFilemanager.h"
#include "HAL/PlatformProcess.h"
#include "HAL/FileManager.h"
#include "HAL/RunnableThread.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "Async/AsyncWork.h"
#include "Async/TaskGraphInterfaces.h"

// UE4 Network headers
#include "Interfaces/IPv4/IPv4Address.h"
#include "Sockets.h"
#include "SocketSubsystem.h"
#include "Common/UdpSocketBuilder.h"

// UE4 Gameplay headers
#include "Kismet/GameplayStatics.h"

// Project headers
#include "../Public/Logger.h"
#include "../Public/ParkingDataManager.h"
#include "../Public/ParkingDataProcessor2UE.h"
#include "../Public/ParkingDataSerializationManager.h"
#include "../Public/GlobalConfig.h"
#include "../Public/ParkingDataProcessManager.h"
#include "../Public/GlobalConfig.h"
#include "../Public/BFL_soLoad.h"

// Standard headers
#include <chrono>
#include <iomanip>
#include <sstream>
#include <string>

#include "Async.h"
#include "FileManager.h"
#include "SocketSubsystem.h"
#include "Common/UdpSocketBuilder.h"
#include "ParkingDataManager.h"
#include "Logger.h"

// Constants
static constexpr float UDP_SLEEP_INTERVAL_S = 0.001f;  // 1ms
static constexpr float UDP_SEND_INTERVAL_S = 0.10f;   // 100ms
static constexpr int32 UDP_RECV_TIMEOUT_S = 200;      // 1ms
static constexpr int32 BUFFER_SIZE_S = 1024 * 500;
static constexpr int32 MAX_RETRY_COUNT_S = 5;


FUDPClientThreadToAndroid::FUDPClientThreadToAndroid(const FString& InIP, int32 InPort)
	: IP(InIP)
	, Port(InPort)
	, bShouldRun(true)
	, Socket(nullptr)
	, Thread(nullptr)
	, LastProcessTime(0.0)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Constructor Begin"), this);



	UBFL_soLoad::LogAllStaticInfo();
	
	// 预分配接收缓冲区
	ReceivedData.SetNumUninitialized(BUFFER_SIZE_S);
	
	// 创建线程
	Thread = FRunnableThread::Create(this, TEXT("UDPToAndroidClientThread"));
	if (!Thread)
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to create UDP To Android client thread"));
	}
#if WITH_EDITOR

	JsonPath = UFileManager::GetAbsolutePath(TEXT("/data/carlinx_data/HPA/HPAMapID/MapID.json"));
#else
	JsonPath = UFileManager::GetAbsolutePath(TEXT("/data/carlinx_data/HPA/HPAMapID/MapID.json"));
#endif
	CARLINX_LOG(LogCarLinx, Log, TEXT("JsonPath initialized in constructor: %s"), *JsonPath);
	
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Constructor End"), this);
}

FUDPClientThreadToAndroid::~FUDPClientThreadToAndroid()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor Begin"), this);
	


	delete Thread;
	Thread = nullptr;
	
	// 关闭socket
	if (Socket)
	{
		Socket->Close();
		ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(Socket);
		Socket = nullptr;
	}



	
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor End"), this);
}

bool FUDPClientThreadToAndroid::Init()
{



	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Init Begin"), this);
	
	// 创建socket
	Socket = FUdpSocketBuilder(TEXT("UDPClientSocket"))
		.AsNonBlocking()
		.WithReceiveBufferSize(BUFFER_SIZE_S)
		.WithSendBufferSize(BUFFER_SIZE_S)
		.Build();

	if (!Socket)
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to create socket"));
		return false;
	}

	// 解析IP地址
	FIPv4Address IPAddress;
	if (!FIPv4Address::Parse(IP, IPAddress))
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to parse IP address: %s"), *IP);
		return false;
	}
	
	RemoteEndpoint = FIPv4Endpoint(IPAddress, Port);
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Init End"), this);






	return true;
}

uint32 FUDPClientThreadToAndroid::Run()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Run Begin"), this);
	
	int32 BytesSent = 0;
	double LastSendTime = FPlatformTime::Seconds();

	while (bShouldRun)
	{
		// 当前时间
		double CurrentTime = FPlatformTime::Seconds();

		// 应用前台时每隔指定时间发一次消息
		if (CurrentTime - LastSendTime >= UDP_SEND_INTERVAL_S) 
		{
			// 尝试更新统计采样，应用后台时，也更新
			//UBFL_soLoad::UpdateCPUSamples();
			// 尝试打印所有动态信息
			UBFL_soLoad::LogAllDynamicInfo();
			
			// 如果是需要someip数据的前台
			
			
				LastSendTime = CurrentTime;
				
				// 发送心跳消息
				bool bSendSuccess = Socket && Socket->SendTo(
					reinterpret_cast<const uint8*>(TCHAR_TO_UTF8(*Message)), 
					Message.Len(),
					BytesSent,
					*RemoteEndpoint.ToInternetAddr()
				);

				if (!bSendSuccess)
				{
					CARLINX_LOG(LogCarLinx, Warning, TEXT("Failed to send message: %s"),
						ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetSocketError());
						
					// 尝试重连
					if (RetryCount.Increment() > MAX_RETRY_COUNT_S)
					{
						HandleConnectionError();
					}
				}
				else
				{
					RetryCount.Set(0);
					CARLINX_LOG(LogCarLinx, Log, TEXT("Sent: \"%s\", Bytes: %d, to Server: %s:%d"),
						*Message, BytesSent, *RemoteEndpoint.Address.ToString(), RemoteEndpoint.Port);
				}
			
			//else
			//{
				// 当前处于后台，每秒提示一次
			//	double CurrentBackgroundTime = FPlatformTime::Seconds();
			//	if (CurrentBackgroundTime - LastSendTime >= 1.0)
			////	{
			//		CARLINX_LOG(LogCarLinx, Log, TEXT("Currently in background, no SOMEIP data interaction, current state: %s"), *UEnum::GetValueAsString(UGlobalConfig::GetInstance()->GetCurrentState()));
				//	LastSendTime = CurrentBackgroundTime;
			//	}
			//	FPlatformProcess::Sleep(UDP_SLEEP_INTERVAL_S);
			//	continue;
			//}
		}
		else {
			// 本次循环未到50ms，等待10ms
			FPlatformProcess::Sleep(UDP_SLEEP_INTERVAL_S);
			continue;
		}

		// 等待接收数据
		bool bHasData = Socket && Socket->Wait(ESocketWaitConditions::WaitForRead,
			FTimespan::FromMilliseconds(UDP_RECV_TIMEOUT_S));

		if (bHasData)
		{
			uint32 PendingDataSize = 0;
			if (Socket->HasPendingData(PendingDataSize))
			{
				// 确保缓冲区足够大
				if (PendingDataSize > static_cast<uint32>(ReceivedData.Num()))
				{
					ReceivedData.SetNumUninitialized(PendingDataSize);
				}

				int32 BytesRead = 0;
				if (Socket->RecvFrom(ReceivedData.GetData(), PendingDataSize,
					BytesRead, *Sender.ToInternetAddr()))
				{
					if (BytesRead > 0)
					{
						// 处理接收到的数据
						std::vector<uint8_t> DataCopy(
							ReceivedData.GetData(),
							ReceivedData.GetData() + BytesRead
						);
						// TODO：增加数据处理 
						// 将数据转换为字符串
						FString DataString(UTF8_TO_TCHAR(reinterpret_cast<const char*>(DataCopy.data())));
						
						// 查找":"后的数字
						int32 ColonPos = DataString.Find(TEXT(":"));
						if (ColonPos != INDEX_NONE)
						{
							// 提取数字部分
							FString NumberStr = DataString.Mid(ColonPos + 1);
							// 移除结尾的 "}"
							NumberStr = NumberStr.Replace(TEXT("}"), TEXT(""));
							// 转换为整数
							int32 Value = FCString::Atoi(*NumberStr);
							CARLINX_LOG(LogCarLinx, Log, TEXT("Parsed value: %d"), Value);
//修改为后台校验地图
							if (Value== 2102021)//收到安卓要求同步信号处理地图数据更新message；下次心跳发送
							{
								
								TArray<int32> List= ConvertJsonListsToIntArray(JsonPath);
								CARLINX_LOG(LogCarLinx,Log, TEXT("[0x%p] OnSyncMap() Begin"), this);

								// 构建JSON对象
								if (List.Num() >=4)
								{
									TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
									JsonObject->SetNumberField("CheckouMapList1", List[0]);
									JsonObject->SetNumberField("CheckouMapList2", List[1]);
									JsonObject->SetNumberField("CheckouMapList3", List[2]);
									JsonObject->SetNumberField("CheckouMapList4", List[3]);

									// 将JSON对象转换为字符串
									FString JsonString;
									TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
									FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

									// 发送JSON字符串给Android
									SetMessage(JsonString);
									CARLINX_LOG(LogCarLinx,Log, TEXT("[0x%p] OnSyncMap() End"), this);
								}
								else
								{
									
										// 处理List为空或元素不足的情况
										CARLINX_LOG(LogCarLinx,Warning, TEXT("[0x%p] OnSyncMap() failed: List is empty or has less than 4 elements"), this);
									
								}
							}
							else if (Value ==-1)//收到安卓清理消息
							{
								SetMessage(TEXT(" "));

							}


						/*	// 发送带参数的广播
							FFunctionGraphTask::CreateAndDispatchWhenReady([this, Value]() {
								CARLINX_LOG(LogCarLinx, Log, TEXT("will call OnSyncMapDelegate Broadcast"));
								FUDPClientThreadToAndroidManager::Get().OnSyncMapDelegate.Broadcast(Value);
								CARLINX_LOG(LogCarLinx, Log, TEXT("call OnSyncMapDelegate Broadcast over"));
							}, TStatId(), nullptr, ENamedThreads::GameThread);*/
							CARLINX_LOG(LogCarLinx, Log, TEXT("Parsed value: %d over"), Value);

						}
					}
				}
				else
				{
					CARLINX_LOG(LogCarLinx, Warning,
						TEXT("Failed to receive data: %s"),
						ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetSocketError());
				}
			}
		} else {
			CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] UDP receive timeout: %s"), this,
				ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetSocketError());
		}
	}

	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Run End"), this);
	return 0;
}

void FUDPClientThreadToAndroid::Stop()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Stop Begin"), this);
	bShouldRun = false;
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Stop End"), this);
}

void FUDPClientThreadToAndroid::Exit()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Exit Begin"), this);
	if (Socket)
	{
		Socket->Close();
		ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(Socket);
		Socket = nullptr;
	}
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Exit End"), this);
}

void FUDPClientThreadToAndroid::InitMap()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("Enter"));
	CARLINX_LOG(LogCarLinx, Log, TEXT("Exit"));
}

void FUDPClientThreadToAndroid::mapKeyAdd(TMap<FString, FString>& inputMap, FString Key)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("Enter"));
	FString value = FString::FromInt(FCString::Atoi(*inputMap[Key] + 1));
	inputMap.Add(Key, FString::Printf(TEXT("%d"), 0));
	CARLINX_LOG(LogCarLinx, Log, TEXT("Exit"));
}

void FUDPClientThreadToAndroid::ProcessReceivedData(const std::vector<uint8_t>& data)
{
	FDateTime StartTime = FDateTime::Now();
	CARLINX_LOG(LogCarLinx, Log, TEXT("Enter >>>>>"));

	// 检查UGlobalConfig是否有效
	UGlobalConfig* GlobalConfig = UGlobalConfig::GetInstance();
	if (!GlobalConfig)
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("GlobalConfig is null"));
		return;
	}

	bool CurrentIsBackground = GlobalConfig->IsInBackground();

	if (CurrentIsBackground)
	{
		// 如果当前状态为后台，则不处理数据
		return;
	}

	// 检查数据处理管理器是否有效
	UParkingDataProcessManager* ProcessManager = UParkingDataProcessManager::GetInstance();
	if (!ProcessManager)
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("ProcessManager is null"));
		return;
	}

	// 使用数据处理管理器处理数据
	ProcessManager->ProcessReceivedData(data);
	
	if (GlobalConfig->GetEnableDataRecording())
	{
		// 只在特定状态下且不在后台时记录数据 APA LSDA HPA 
		if (GlobalConfig->GetCurrentState() >= EParkingState::APA)
		{
			UParkingDataSerializationManager* SerializationManager = UParkingDataSerializationManager::GetInstance();
			if (SerializationManager)
			{
				SerializationManager->RecordFrame(data.data(), data.size());
			}
			else
			{
				CARLINX_LOG(LogCarLinx, Error, TEXT("SerializationManager is null"));
			}
		}
	}

	FDateTime EndTime = FDateTime::Now();
	FTimespan TimeDiff = EndTime - StartTime;
	CARLINX_LOG(LogCarLinx, Log, TEXT("Exit <<<<<< Time elapsed: %d ms"), TimeDiff.GetTotalMilliseconds());
}

void FUDPClientThreadToAndroid::HandleConnectionError()
{
    CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Connection error, attempting to reconnect..."), this);
    
    if (Socket)
    {
        Socket->Close();
        ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(Socket);
        Socket = nullptr;
    }

    if (!TryReconnect())
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to reconnect, stopping thread..."), this);
        Stop();
    }
}

bool FUDPClientThreadToAndroid::TryReconnect()
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Attempting to reconnect..."), this);
    
    // 创建新的socket
    Socket = FUdpSocketBuilder(TEXT("UDPClientSocket"))
        .AsNonBlocking()
        .WithReceiveBufferSize(BUFFER_SIZE_S)
        .WithSendBufferSize(BUFFER_SIZE_S)
        .Build();

    if (!Socket)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to create new socket"), this);
        return false;
    }

    // 解析IP地址
    FIPv4Address IPAddress;
    if (!FIPv4Address::Parse(IP, IPAddress))
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to parse IP address: %s"), this, *IP);
        return false;
    }

    RemoteEndpoint = FIPv4Endpoint(IPAddress, Port);
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Reconnected successfully"), this);
    return true;
}

bool FUDPClientThreadToAndroid::UpdateMapValue(TMap<FString, FString>& Map, const FString& Key, const FString& Value)
{
	const FString* OldValue = Map.Find(Key);
	if (!OldValue || *OldValue != Value)
	{
		Map.Add(Key, Value);
		return true;
	}
	return false;
}

void FUDPClientThreadToAndroid::SetMessage(FString InMessage)
{
	Message = InMessage;
}

// FUDPClientThreadToAndroidManager implementation
FUDPClientThreadToAndroidManager& FUDPClientThreadToAndroidManager::Get()
{
	static FUDPClientThreadToAndroidManager Instance;
	return Instance;
}

void FUDPClientThreadToAndroidManager::StartThread(const FString& IP, int32 Port)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadToAndroidManager::StartThread() Enter"));
	if (!ClientThread)
	{
		ClientThread = new FUDPClientThreadToAndroid(IP, Port);
	}
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadToAndroidManager::StartThread() Exit"));
}

void FUDPClientThreadToAndroidManager::StopThread()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadToAndroidManager::StopThread() Enter"));
	if (ClientThread)
	{
		ClientThread->Stop();
		delete ClientThread;
		ClientThread = nullptr;
	}
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadToAndroidManager::StopThread() Exit"));
}




FUDPClientThreadToAndroidManager::FUDPClientThreadToAndroidManager() : ClientThread(nullptr)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadToAndroidManager::FUDPClientThreadToAndroidManager() Enter"));



	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadToAndroidManager::FUDPClientThreadToAndroidManager() Exit"));
}

FUDPClientThreadToAndroidManager::~FUDPClientThreadToAndroidManager()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadToAndroidManager::~FUDPClientThreadToAndroidManager() Enter"));
	StopThread();
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadToAndroidManager::~FUDPClientThreadToAndroidManager() Exit"));
}


void FUDPClientThreadToAndroidManager::SetMessage(FString InMessage)
{
	if (ClientThread)
	{
		ClientThread->SetMessage(InMessage);
	}
}

TArray<int32> FUDPClientThreadToAndroid::ConvertJsonListsToIntArray(const FString& JsonFilePath)
{
	TArray<int32> ResultArray;

	// 检查文件是否存在
	if (!FPaths::FileExists(JsonFilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("JSON file not found at path: %s"), *JsonFilePath);
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] JSON file not found at path: %s"), *JsonFilePath);
		return ResultArray;
	}
	UFileManager::SetCurrentFilePermissions(JsonFilePath, false);
	FString JsonContent;
	// 读取文件内容
	if (!FFileHelper::LoadFileToString(JsonContent, *JsonFilePath))
	{
		//UE_LOG(LogTemp, Error, TEXT("Failed to load JSON file: %s"), *JsonFilePath);
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] Failed to load JSON file: %s"), *JsonFilePath);
		return ResultArray;
	}

	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonContent);

	// 解析 JSON 文件
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON file: %s"), *JsonFilePath);
		CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] Failed to parse JSON file: %s"), *JsonFilePath);
		return ResultArray;
	}

	// 遍历 List1 到 List4
	for (int32 i = 1; i <= 4; i++)
	{
		FString ListKey = FString::Printf(TEXT("List%d"), i);

		// 获取对应的 List 数组
		const TArray<TSharedPtr<FJsonValue>>* JsonArray;
		if (!JsonObject->TryGetArrayField(ListKey, JsonArray))
		{
			UE_LOG(LogTemp, Error, TEXT("List key %s not found or invalid in JSON file."), *ListKey);
			CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] List key %s not found or invalid in JSON file."), *ListKey);

			ResultArray.Add(0); // 添加默认值
			continue;
		}

		// 检查数组大小
		if (JsonArray->Num() != 32)
		{
			UE_LOG(LogTemp, Error, TEXT("%s does not contain exactly 32 elements."), *ListKey);
			CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray] %s does not contain exactly 32 elements."), *ListKey);
			ResultArray.Add(0); // 添加默认值
			continue;
		}

		int32 ConvertedValue = 0;
		for (int32 BitIndex = 0; BitIndex < 32; ++BitIndex)
		{
			int32 BitValue = (*JsonArray)[BitIndex]->AsNumber();
			if (BitValue != 0 && BitValue != 1)
			{
				UE_LOG(LogTemp, Error, TEXT("Invalid bit value %d in %s at index %d."), BitValue, *ListKey, BitIndex);
				CARLINX_LOG(LogCarLinx,Error, TEXT("[ConvertJsonListsToIntArray]Invalid bit value % d in % s at index % d."), BitValue, *ListKey, BitIndex);
				ConvertedValue = 0; // 遇到错误时设置为默认值
				break;
			}

			// 使用位操作将 bit 加入结果
			ConvertedValue |= (BitValue << BitIndex);
		}

		ResultArray.Add(ConvertedValue);
	}

	return ResultArray;




}
