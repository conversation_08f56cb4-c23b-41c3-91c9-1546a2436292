#include "ParkingDataManager.h"
#include <cstring>
#include "Logger.h"

void ParkingDataManager::updatePrkgSelfST(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& data) {
    m_prkgSelfST = data;
    setFlag(FLAG_SELF_ST);
}

const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& ParkingDataManager::getPrkgSelfST() const {
    return m_prkgSelfST;
}

void ParkingDataManager::updatePrkgSlotArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot, 32>& data) {
    m_prkgSlotArr = data;
    setFlag(FLAG_SLOT_ARR);
}

const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot, 32>& ParkingDataManager::getPrkgSlotArr() const {
    return m_prkgSlotArr;
}

void ParkingDataManager::updatePrkgObstacleArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle, 60>& data) {
    m_prkgObstacleArr = data;
    setFlag(FLAG_OBSTACLE_ARR);
}

const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle, 60>& ParkingDataManager::getPrkgObstacleArr() const {
    return m_prkgObstacleArr;
}

void ParkingDataManager::updatePrkgTrafficsignArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgTrafficsign, 10>& data) {
    m_prkgTrafficsignArr = data;
    setFlag(FLAG_TRAFFICSIGN_ARR);
}

const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgTrafficsign, 10>& ParkingDataManager::getPrkgTrafficsignArr() const {
    return m_prkgTrafficsignArr;
}

void ParkingDataManager::updatePrkgRoadRightPointArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& data) {
    m_prkgRoadRightPointArr = data;
    setFlag(FLAG_ROAD_RIGHT_POINT_ARR);
}

const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& ParkingDataManager::getPrkgRoadRightPointArr() const {
    return m_prkgRoadRightPointArr;
}

void ParkingDataManager::updatePrkgRoadLeftPointArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& data) {
    m_prkgRoadLeftPointArr = data;
    setFlag(FLAG_ROAD_LEFT_POINT_ARR);
}

const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& ParkingDataManager::getPrkgRoadLeftPointArr() const {
    return m_prkgRoadLeftPointArr;
}

void ParkingDataManager::updatePrkgTtrackPlanningLArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& data) {
    m_prkgTtrackPlanningLArr = data;
    setFlag(FLAG_TTRACK_PLANNING_L_ARR);
}

const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& ParkingDataManager::getPrkgTtrackPlanningLArr() const {
    return m_prkgTtrackPlanningLArr;
}

void ParkingDataManager::updatePrkgTtrackPlanningRArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& data) {
    m_prkgTtrackPlanningRArr = data;
    setFlag(FLAG_TTRACK_PLANNING_R_ARR);
}

const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& ParkingDataManager::getPrkgTtrackPlanningRArr() const {
    return m_prkgTtrackPlanningRArr;
}

void ParkingDataManager::updatePerspectiveMode(uint8_t mode) {
    m_perspectiveMode = mode;
    setFlag(FLAG_PERSPECTIVE_MODE);
}

uint8_t ParkingDataManager::getPerspectiveMode() const {
    return m_perspectiveMode;
}

bool ParkingDataManager::hasUpdates() const {
    return m_updateFlags != FLAG_NONE;
}

bool ParkingDataManager::hasDefaultFlags() const {
    return (m_updateFlags & FLAG_DEFAULT) == FLAG_DEFAULT;
}

void ParkingDataManager::setFlag(DataFlag flag) {
    m_updateFlags |= flag;
}

void ParkingDataManager::clearFlag(DataFlag flag) {
    m_updateFlags &= ~flag;
}

void ParkingDataManager::clearAllFlags() {
    m_updateFlags = FLAG_NONE;
}

void ParkingDataManager::resetToDefaultFlags() {
    m_updateFlags = FLAG_DEFAULT;
}

uint32_t ParkingDataManager::getFlags() const {
    return m_updateFlags;
}

std::vector<uint8_t> ParkingDataManager::serialize() const {
    std::vector<uint8_t> buffer;
    
        // 打印并计算总大小
    size_t size_prkgSelfST = sizeof(m_prkgSelfST);
    size_t size_prkgSlotArr = sizeof(m_prkgSlotArr);
    size_t size_prkgObstacleArr = sizeof(m_prkgObstacleArr);
    size_t size_prkgTrafficsignArr = sizeof(m_prkgTrafficsignArr);
    size_t size_prkgRoadRightPointArr = sizeof(m_prkgRoadRightPointArr);
    size_t size_prkgRoadLeftPointArr = sizeof(m_prkgRoadLeftPointArr);
    size_t size_prkgTtrackPlanningLArr = sizeof(m_prkgTtrackPlanningLArr);
    size_t size_prkgTtrackPlanningRArr = sizeof(m_prkgTtrackPlanningRArr);
    size_t size_perspectiveMode = sizeof(m_perspectiveMode);
    
    printf("Size of members:\n");
    printf("m_prkgSelfST: %zu\n", size_prkgSelfST);
    printf("m_prkgSlotArr: %zu\n", size_prkgSlotArr);
    printf("m_prkgObstacleArr: %zu\n", size_prkgObstacleArr);
    printf("m_prkgTrafficsignArr: %zu\n", size_prkgTrafficsignArr);
    printf("m_prkgRoadRightPointArr: %zu\n", size_prkgRoadRightPointArr);
    printf("m_prkgRoadLeftPointArr: %zu\n", size_prkgRoadLeftPointArr);
    printf("m_prkgTtrackPlanningLArr: %zu\n", size_prkgTtrackPlanningLArr);
    printf("m_prkgTtrackPlanningRArr: %zu\n", size_prkgTtrackPlanningRArr);
    printf("m_perspectiveMode: %zu\n", size_perspectiveMode);
    size_t totalSize = size_prkgSelfST +
                      size_prkgSlotArr +
                      size_prkgObstacleArr +
                      size_prkgTrafficsignArr +
                      size_prkgRoadRightPointArr +
                      size_prkgRoadLeftPointArr +
                      size_prkgTtrackPlanningLArr +
                      size_prkgTtrackPlanningRArr +
                      size_perspectiveMode;
                      
    printf("Total size: %zu\n", totalSize);
    
    // 计算总大小
    totalSize = sizeof(m_prkgSelfST) +
                      sizeof(m_prkgSlotArr) +
                      sizeof(m_prkgObstacleArr) +
                      sizeof(m_prkgTrafficsignArr) +
                      sizeof(m_prkgRoadRightPointArr) +
                      sizeof(m_prkgRoadLeftPointArr) +
                      sizeof(m_prkgTtrackPlanningLArr) +
                      sizeof(m_prkgTtrackPlanningRArr) +
                      sizeof(m_perspectiveMode);
    
    buffer.resize(totalSize);
    uint8_t* ptr = buffer.data();
    
    // 序列化每个成员
    memcpy(ptr, &m_prkgSelfST, sizeof(m_prkgSelfST));
    ptr += sizeof(m_prkgSelfST);
    
    memcpy(ptr, m_prkgSlotArr.data(), sizeof(m_prkgSlotArr));
    ptr += sizeof(m_prkgSlotArr);
    
    memcpy(ptr, m_prkgObstacleArr.data(), sizeof(m_prkgObstacleArr));
    ptr += sizeof(m_prkgObstacleArr);
    
    memcpy(ptr, m_prkgTrafficsignArr.data(), sizeof(m_prkgTrafficsignArr));
    ptr += sizeof(m_prkgTrafficsignArr);
    
    memcpy(ptr, m_prkgRoadRightPointArr.data(), sizeof(m_prkgRoadRightPointArr));
    ptr += sizeof(m_prkgRoadRightPointArr);
    
    memcpy(ptr, m_prkgRoadLeftPointArr.data(), sizeof(m_prkgRoadLeftPointArr));
    ptr += sizeof(m_prkgRoadLeftPointArr);
    
    memcpy(ptr, m_prkgTtrackPlanningLArr.data(), sizeof(m_prkgTtrackPlanningLArr));
    ptr += sizeof(m_prkgTtrackPlanningLArr);
    
    memcpy(ptr, m_prkgTtrackPlanningRArr.data(), sizeof(m_prkgTtrackPlanningRArr));
    ptr += sizeof(m_prkgTtrackPlanningRArr);
    
    memcpy(ptr, &m_perspectiveMode, sizeof(m_perspectiveMode));
    
    // 序列化完成后重置为默认标志位
    const_cast<ParkingDataManager*>(this)->resetToDefaultFlags();
    
    return buffer;
}

bool ParkingDataManager::deserialize(const std::vector<uint8_t>& data) {

    // 打印并计算总大小
    size_t size_prkgSelfST = sizeof(m_prkgSelfST);
    size_t size_prkgSlotArr = sizeof(m_prkgSlotArr);
    size_t size_prkgObstacleArr = sizeof(m_prkgObstacleArr);
    size_t size_prkgTrafficsignArr = sizeof(m_prkgTrafficsignArr);
    size_t size_prkgRoadRightPointArr = sizeof(m_prkgRoadRightPointArr);
    size_t size_prkgRoadLeftPointArr = sizeof(m_prkgRoadLeftPointArr);
    size_t size_prkgTtrackPlanningLArr = sizeof(m_prkgTtrackPlanningLArr);
    size_t size_prkgTtrackPlanningRArr = sizeof(m_prkgTtrackPlanningRArr);
    size_t size_perspectiveMode = sizeof(m_perspectiveMode);
    
    // printf("Size of members:\n");
    // printf("m_prkgSelfST: %zu\n", size_prkgSelfST);
    // printf("m_prkgSlotArr: %zu\n", size_prkgSlotArr);
    // printf("m_prkgObstacleArr: %zu\n", size_prkgObstacleArr);
    // printf("m_prkgTrafficsignArr: %zu\n", size_prkgTrafficsignArr);
    // printf("m_prkgRoadRightPointArr: %zu\n", size_prkgRoadRightPointArr);
    // printf("m_prkgRoadLeftPointArr: %zu\n", size_prkgRoadLeftPointArr);
    // printf("m_prkgTtrackPlanningLArr: %zu\n", size_prkgTtrackPlanningLArr);
    // printf("m_prkgTtrackPlanningRArr: %zu\n", size_prkgTtrackPlanningRArr);
    // printf("m_perspectiveMode: %zu\n", size_perspectiveMode);
    

    // CARLINX_LOG(LogCarLinx, Log, TEXT("[carlinx][Debug][0x%p] Size of members:\n\
    //             m_prkgSelfST: %zu\n\
    //             m_prkgSlotArr: %zu\n\
    //             m_prkgObstacleArr: %zu\n\
    //             m_prkgTrafficsignArr: %zu\n\
    //             m_prkgRoadRightPointArr: %zu\n\
    //             m_prkgRoadLeftPointArr: %zu\n\
    //             m_prkgTtrackPlanningLArr: %zu\n\
    //             m_prkgTtrackPlanningRArr: %zu\n\
    //             m_perspectiveMode: %zu"),
    //             this, 
    //             size_prkgSelfST, size_prkgSlotArr, size_prkgObstacleArr, 
    //             size_prkgTrafficsignArr, size_prkgRoadRightPointArr, size_prkgRoadLeftPointArr, 
    //             size_prkgTtrackPlanningLArr, size_prkgTtrackPlanningRArr, size_perspectiveMode);

    // 检查数据大小是否正确
    size_t expectedSize = sizeof(m_prkgSelfST) +
                         sizeof(m_prkgSlotArr) +
                         sizeof(m_prkgObstacleArr) +
                         sizeof(m_prkgTrafficsignArr) +
                         sizeof(m_prkgRoadRightPointArr) +
                         sizeof(m_prkgRoadLeftPointArr) +
                         sizeof(m_prkgTtrackPlanningLArr) +
                         sizeof(m_prkgTtrackPlanningRArr) +
                         sizeof(m_perspectiveMode);
    
    size_t totalSize = size_prkgSelfST +
                      size_prkgSlotArr +
                      size_prkgObstacleArr +
                      size_prkgTrafficsignArr +
                      size_prkgRoadRightPointArr +
                      size_prkgRoadLeftPointArr +
                      size_prkgTtrackPlanningLArr +
                      size_prkgTtrackPlanningRArr +
                      size_perspectiveMode;
                      

    if (data.size() != expectedSize) {
        CARLINX_LOG(LogCarLinx, Error, TEXT("size mismatch: data size: %zu, total size: %zu, expected size: %zu"), data.size(), totalSize, expectedSize);
        return false;
    }
    
    const uint8_t* ptr = data.data();
    
    // 反序列化每个成员
    memcpy(&m_prkgSelfST, ptr, sizeof(m_prkgSelfST));
    ptr += sizeof(m_prkgSelfST);
    
    memcpy(m_prkgSlotArr.data(), ptr, sizeof(m_prkgSlotArr));
    ptr += sizeof(m_prkgSlotArr);
    
    memcpy(m_prkgObstacleArr.data(), ptr, sizeof(m_prkgObstacleArr));
    ptr += sizeof(m_prkgObstacleArr);
    
    memcpy(m_prkgTrafficsignArr.data(), ptr, sizeof(m_prkgTrafficsignArr));
    ptr += sizeof(m_prkgTrafficsignArr);
    
    memcpy(m_prkgRoadRightPointArr.data(), ptr, sizeof(m_prkgRoadRightPointArr));
    ptr += sizeof(m_prkgRoadRightPointArr);
    
    memcpy(m_prkgRoadLeftPointArr.data(), ptr, sizeof(m_prkgRoadLeftPointArr));
    ptr += sizeof(m_prkgRoadLeftPointArr);
    
    memcpy(m_prkgTtrackPlanningLArr.data(), ptr, sizeof(m_prkgTtrackPlanningLArr));
    ptr += sizeof(m_prkgTtrackPlanningLArr);
    
    memcpy(m_prkgTtrackPlanningRArr.data(), ptr, sizeof(m_prkgTtrackPlanningRArr));
    ptr += sizeof(m_prkgTtrackPlanningRArr);
    
    memcpy(&m_perspectiveMode, ptr, sizeof(m_perspectiveMode));
    
    // 反序列化成功后设置所有标志位
    m_updateFlags = FLAG_ALL;
    
    return true;
}

bool ParkingDataManager::isFlagSet(DataFlag flag) const
{
    return (m_updateFlags & static_cast<uint32_t>(flag)) != 0;
}