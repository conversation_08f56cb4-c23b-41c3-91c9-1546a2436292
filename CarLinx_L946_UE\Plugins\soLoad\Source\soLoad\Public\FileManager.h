#pragma once

#include "CoreMinimal.h"
#include "DataTypes.h"
#include "FileManager.generated.h"

class UTextureRenderTarget2D;

UCLASS(Blueprintable, BlueprintType)
class SOLOAD_API UFileManager : public UObject
{
    GENERATED_BODY()

public:

    //获取绝对路径
    UFUNCTION(BlueprintPure, Category = "File|Operations")
    static FString GetAbsolutePath(const FString& RelativePath);

    //设置文件读写权限
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool SetCurrentFilePermissions(const FString& FilePath, bool bIsDirectory);
    // 写入文本文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool WriteTextFile(const FString& RelativePath, const FString& Content, bool bOverwrite = true);
    
    // 追加文本到文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool AppendTextFile(const FString& RelativePath, const FString& Content);
    
    // 读取文本文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool ReadTextFile(const FString& RelativePath, FString& OutContent);
    
    // 写入二进制文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool WriteBinaryFile(const FString& RelativePath, const TArray<uint8>& Content, bool bOverwrite = true);

    //创建Json文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool CreateJsonFile(const FString& AllMapIDFileName);
    
    // 读取二进制文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool ReadBinaryFile(const FString& RelativePath, TArray<uint8>& OutContent);

    //保存路径点
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool WriteRoadPointFile(const TArray<FRoadPoint>& Points, const FString& RelativePath);

    //读取保存路径点
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool ReadRoadPointsFromJson(const FString& LoadPath,TArray<FRoadPoint>& OutContent);

    //获取纹理图片
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static UTexture2D* ReadTextureFromPNG(const FString& RelativePath);

    //保存时间到Json格式
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool SaveTimesToJson(const FString& RelativePath, const FString& Times);

    //读取时间格式
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool ReadTimeFromJson(const FString& RelativePath, FString& OutTimes);

    //保存RenderTarget为PNG
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool SaveRenderTargetToPNG(UTextureRenderTarget2D* RenderTarget, const FString& FullFilePath);

    // 文件是否存在
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool FileExists(const FString& RelativePath);
    
    // 删除文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool DeleteFile(const FString& RelativePath);
    
    // 创建目录
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool CreateDirectory(const FString& RelativePath);

    // 获取文件大小
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static int64 GetFileSize(const FString& RelativePath);

    // 复制文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool CopyFile(const FString& SourcePath, const FString& DestPath);

    // 移动文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool MoveFile(const FString& SourcePath, const FString& DestPath);

    // 获取目录下所有文件
    UFUNCTION(BlueprintCallable, Category = "File|Operations")
    static bool GetFilesInDirectory(const FString& Directory, const FString& Extension, TArray<FString>& OutFiles);

private:
    // 转换为绝对路径
    static FString ConvertToAbsolutePath(const FString& RelativePath);
    
    // 确保目录存在
    static bool EnsureDirectoryExists(const FString& AbsolutePath);
}; 