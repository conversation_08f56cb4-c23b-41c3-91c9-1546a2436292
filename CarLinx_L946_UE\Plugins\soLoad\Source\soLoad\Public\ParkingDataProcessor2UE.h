#pragma once

#include "CoreMinimal.h"
#include "Logger.h"
#include "VehValtPrkgHmiGenr_minimal_types.h"
#include "DataTypes.h"
#include <array>
#include "UObject/NoExportTypes.h"
#include "ParkingDataProcessor2UE.generated.h"


USTRUCT(BlueprintType)
struct SOLOAD_API FParkingSlotData
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 SlotID;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 SlotYawAg;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 SlotRollAg;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 SlotPitchAg;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 ObiqueSlotAg;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    uint8 SlotTypInfo;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    FVector SlotStart;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    FVector SlotEnd;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    FVector SlotStartRear;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    FVector SlotEndRear;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    uint8 SlotSts1;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    uint8 SlotNumber;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    uint8 SlotCod1;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    uint8 SlotCod2;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    uint8 SlotCod3;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    uint8 SlotCod4;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    uint8 SlotCod5;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 HasWheelBlock;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 WheelBlockFront;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 WheelBlockRear;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 HasGroundLock;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    int32 GroundLockStatus;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    FVector CenterPosition;

    UPROPERTY(BlueprintReadWrite, Category = "Parking")
    float Angle;
};

// 原始的数据处理类，供内部使用
class ParkingDataProcessor2UEInternal {
public:
    static bool DeserializeSelfCarData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& selfData, 
                                      TMap<FString, FString>& SelfCarMap);
    
    static bool DeserializeSlotData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot& slotData,
                                   TMap<FString, FString>& SlotMap);
                                   
    static bool DeserializeObstacleData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle& obstacleData,
                                       TMap<FString, FString>& ObstacleMap);
                                       
    static bool DeserializeTrafficSignData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgTrafficsign& signData,
                                          TMap<FString, FString>& TrafficSignMap);
                                          
    static bool DeserializeRoadPointData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint& pointData,
                                        TMap<FString, FString>& RoadPointMap);
                                        
    static bool DeserializePerspectiveMode(uint8_t mode,
                                          TMap<FString, FString>& PerspectiveMode);
                                          
    static bool DeserializeTrackPlanningData(const gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning& planningData,
                                            TMap<FString, FString>& TrackPlanningMap);

    static bool DeserializeTrackPlanningData(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& left, 
        const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& right, const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& selfData,
        TArray<FSTCTRACK>& TrackPlanningPoints, int& LineType);

    static bool DeserializeHpaRoadPoint(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& Left,
        const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& Right, TArray<FRoadPoint>& HPARoadPoints);

    static void StartRecordingRange();
    static void ProcessCarRange(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& selfData);
    static bool GetRangeInfo(FVector& OutCenter, float& OutDistance);

    static void StartRecordingSlots();
    static const TMap<int32, FParkingSlotData>& GetSlotHistory();

    static void ClearSlotHistory();

private:
    static bool UpdateMapValue(TMap<FString, FString>& Map, const FString& Key, const FString& Value);
    static FString FloatToString(float Value);
    static FString Int32ToString(int32 Value);
    static FString UInt8ToString(uint8_t Value);
    static FString BoolToString(bool Value);

    static int CheckBitStatus(int32 value);
    static int CheckTraceID(float MiniOffset, float ChangeOffset, FVector StartPoint, FVector EndPoint);

    static void ProcessSlotData(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot& slotData);
    

    static bool bNearSmallOffset;
    static float RecordingTraceOffset;
    static int TraceID;

    static bool bIsRecording;
    static bool bHasRecordedData;
    static FVector MinRange;
    static FVector MaxRange;

    static const float HALF_VEHICLE_LENGTH;  // 半个车身长度，单位cm
    static const float SCALE_FACTOR;         // 缩放因子
    static TMap<int32, FParkingSlotData> SlotHistory;
    static bool bIsRecordingSlots;
};

// 蓝图可调用的类
UCLASS(BlueprintType)
class SOLOAD_API UParkingDataProcessor2UE : public UObject
{
    GENERATED_BODY()

public:
    // 开始记录范围
    UFUNCTION(BlueprintCallable, Category = "ParkingDataProcessor")
    static void StartRecordingRange();

    // 获取范围信息
    UFUNCTION(BlueprintCallable, Category = "ParkingDataProcessor")
    static bool GetRangeInfo(FVector& OutCenter, float& OutDistance);

    // 开始记录车位
    UFUNCTION(BlueprintCallable, Category = "ParkingDataProcessor")
    static void StartRecordingSlots();

    // 清除历史车位数据
    UFUNCTION(BlueprintCallable, Category = "ParkingDataProcessor")
    static void ClearSlotHistory();

    // 获取历史车位数据
    UFUNCTION(BlueprintCallable, Category = "ParkingDataProcessor")
    static void GetSlotHistory(TMap<int32, FParkingSlotData>& OutSlotHistory);
};