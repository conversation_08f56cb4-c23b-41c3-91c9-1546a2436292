// Copyright Pandores Marketplace 2021. All Rights Reserved.

#include "BlueprintJsonEditorNodes.h"

#include "KismetCompiler.h"
#include "BlueprintActionDatabaseRegistrar.h"
#include "BlueprintNodeSpawner.h"
#include "K2Node_CallFunction.h"

#include "BlueprintJsonWrapper.h"
#include "BlueprintJsonLibrary.h"

#define LOCTEXT_NAMESPACE "UK2_BlueprintJson_AddFieldToJsonObject"

struct FPinName
{
#define AddEntry(Key, Name) static const FName& Key (){static const FName _Name = TEXT(Name);return _Name;}

    AddEntry(FieldValue, "FieldValue");
    AddEntry(FieldName,  "FieldName");
    AddEntry(JsonObject, "JsonObject");
    AddEntry(ForBody,    "Loopr Body");
    AddEntry(Completed,  "Completed");

#undef AddEntry
};

UK2Node_AddFieldToJsonObject::UK2Node_AddFieldToJsonObject(const FObjectInitializer& OI)
	: Super(OI)
{}

FLinearColor UK2Node_AddFieldToJsonObject::GetNodeTitleColor() const 
{
    return FLinearColor(1.f, 0.7f, 0.f);
}

FText UK2Node_AddFieldToJsonObject::GetNodeTitle(ENodeTitleType::Type TitleType) const
{
    return LOCTEXT("UK2_BlueprintJson_AddFieldToJsonObject_Title", "Add Field to JSON Object");
}

FText UK2Node_AddFieldToJsonObject::GetTooltipText() const
{
    return LOCTEXT("UK2_BlueprintJson_AddFieldToJsonObject_Tooltip", "Add a Field with the specified value to a JSON object.");
}

FText UK2Node_AddFieldToJsonObject::GetMenuCategory() const
{
    return LOCTEXT("UK2_BlueprintJson_AddFieldToJsonObject_MenuCategory", "JSON");
}

UEdGraphPin* UK2Node_AddFieldToJsonObject::GetFieldValuePin()
{
    return FindPinChecked(FPinName::FieldValue());
}

UEdGraphPin* UK2Node_AddFieldToJsonObject::GetJsonObjectPin()
{
    return FindPinChecked(FPinName::JsonObject());
}

UEdGraphPin* UK2Node_AddFieldToJsonObject::GetFieldNamePin()
{
    return FindPinChecked(FPinName::FieldName());
}

bool UK2Node_AddFieldToJsonObject::IsSupportedStructType(const FString& Type) const
{
    static const TSet<FString> SupportedStructs =
    {
        TEXT("Vector"),
        TEXT("Vector2D"),
        TEXT("Transform"),
        TEXT("Rotator"),
        TEXT("LinearColor"),
        TEXT("Color"),
        TEXT("Quat"),
        TEXT("HitResult"),
        TEXT("Guid")
    };

    return SupportedStructs.Contains(Type);
}

void UK2Node_AddFieldToJsonObject::ReallocatePinsDuringReconstruction(TArray<UEdGraphPin*>& OldPins)
{
    Super::ReallocatePinsDuringReconstruction(OldPins);

    for (const auto& OldPin : OldPins)
    {
        if (OldPin->PinName == FPinName::FieldValue())
        {
            UEdGraphPin* const NewPin = GetFieldValuePin();
            if (NewPin)
            {
                NewPin->PinType = OldPin->PinType;
            }
            break;
        }
    }
}

void UK2Node_AddFieldToJsonObject::UpdateFieldValuePin()
{
    UEdGraphPin* const ValuePin = GetFieldValuePin();

    if (ValuePin->LinkedTo.Num() > 0)
    {
        ValuePin->PinType = ValuePin->LinkedTo[0]->PinType;
    }

    else
    {
        ValuePin->PinType = FEdGraphPinType(UEdGraphSchema_K2::PC_Wildcard, NAME_None, nullptr, EPinContainerType::None, false, FEdGraphTerminalType());
        ValuePin->BreakAllPinLinks();
        ValuePin->ResetDefaultValue();
    }
}

void UK2Node_AddFieldToJsonObject::PinDefaultValueChanged(UEdGraphPin* const Pin)
{
    Super::PinDefaultValueChanged(Pin);

    if (Pin)
    {
        if (Pin->PinName == FPinName::FieldValue())
        {
            UpdateFieldValuePin();
        }
    }
}

void UK2Node_AddFieldToJsonObject::NotifyPinConnectionListChanged(UEdGraphPin* Pin)
{
    if (Pin)
    {
        if (Pin->PinName == FPinName::FieldValue())
        {
            UpdateFieldValuePin();
        }
    }
}

void UK2Node_AddFieldToJsonObject::AllocateDefaultPins()
{
	const UEdGraphSchema_K2* const K2Schema = GetDefault<UEdGraphSchema_K2>();

    // Execution pins
	CreatePin(EGPD_Input,  UEdGraphSchema_K2::PC_Exec, UEdGraphSchema_K2::PN_Execute);
	CreatePin(EGPD_Output, UEdGraphSchema_K2::PC_Exec, UEdGraphSchema_K2::PN_Then);

    // Input pins
    UEdGraphPin* const JsonObjectPin = CreatePin(EGPD_Input, UEdGraphSchema_K2::PC_Object,   UJsonObject::StaticClass(), FPinName::JsonObject());
    UEdGraphPin* const FieldNamePin  = CreatePin(EGPD_Input, UEdGraphSchema_K2::PC_String,   FPinName::FieldName());
    UEdGraphPin* const FieldValuePin = CreatePin(EGPD_Input, UEdGraphSchema_K2::PC_Wildcard, FPinName::FieldValue());

    K2Schema->SetPinAutogeneratedDefaultValueBasedOnType(FieldNamePin);

	Super::AllocateDefaultPins();
}

void UK2Node_AddFieldToJsonObject::GetMenuActions(FBlueprintActionDatabaseRegistrar& ActionRegistrar) const
{
    Super::GetMenuActions(ActionRegistrar);

    UClass* const Action = GetClass();
    if (ActionRegistrar.IsOpenForRegistration(Action))
    {
        UBlueprintNodeSpawner* const Spawner = UBlueprintNodeSpawner::Create(GetClass());
        check(Spawner != nullptr);
        ActionRegistrar.AddBlueprintAction(Action, Spawner);
    }
}

bool UK2Node_AddFieldToJsonObject::IsValidValueType(const UEdGraphPin* const Pin, FString & OutReason) const
{
    const FName & PinType = Pin->PinType.PinCategory;

    if (PinType == UEdGraphSchema_K2::PC_Object && Pin->PinType.PinSubCategoryObject.IsValid())
    {
        const FString & ObjectName = Pin->PinType.PinSubCategoryObject->GetName();
        if (ObjectName == TEXT("JsonObject") || ObjectName == TEXT("JsonValue"))
        {
            OutReason = FString::Printf(TEXT("Add %s field."), *ObjectName);
            return true;
        }
        
        OutReason = FString::Printf(TEXT("Type %s is not supported."), *ObjectName);
        return false;
    }

    if (PinType == UEdGraphSchema_K2::PC_Struct && Pin->PinType.PinSubCategoryObject.IsValid())
    {
        const FString & StructName = Pin->PinType.PinSubCategoryObject->GetName();
        if (IsSupportedStructType(StructName))
        {
            OutReason = FString::Printf(TEXT("Add %s field."), *StructName);
            return true;
        }
        OutReason = FString::Printf(TEXT("Struct type %s is not supported."), *StructName);
        return false;
    }

    const bool bValidType =
        PinType == UEdGraphSchema_K2::PC_Int    ||
        PinType == UEdGraphSchema_K2::PC_Int64  ||
        PinType == UEdGraphSchema_K2::PC_String ||
        PinType == UEdGraphSchema_K2::PC_Text   ||
        PinType == UEdGraphSchema_K2::PC_Name   ||
        PinType == UEdGraphSchema_K2::PC_Float  ||
        PinType == UEdGraphSchema_K2::PC_Boolean;

    if (!bValidType)
    {
        OutReason = FString::Printf(TEXT("Type %s is not supported."), *PinType.ToString());
    }

    else
    {
        OutReason = FString::Printf(TEXT("Add %s field."), *PinType.ToString());
    }

    return bValidType;
}

bool UK2Node_AddFieldToJsonObject::IsConnectionDisallowed(const UEdGraphPin* const MyPin, const UEdGraphPin* const OtherPin, FString& OutReason) const
{
    if (MyPin->PinName == FPinName::FieldValue())
    {   
        return !IsValidValueType(OtherPin, OutReason);
    }

    return false;
}

void UK2Node_AddFieldToJsonObject::ExpandNode(FKismetCompilerContext& CompilerContext, UEdGraph* SourceGraph) 
{
	Super::ExpandNode(CompilerContext, SourceGraph);

    UEdGraphPin* const JsonObjectPin = GetJsonObjectPin();
    UEdGraphPin* const FieldNamePin  = GetFieldNamePin();
    UEdGraphPin* const FieldValuePin = GetFieldValuePin();

    UFunction* const Function = GetMatchingFunction(CompilerContext);

    if (!Function)
    {
        CompilerContext.MessageLog.Error(TEXT("Add Field to JSON Object: Function not found."));
        return;
    }

    UK2Node_CallFunction* const CallFunction = CompilerContext.SpawnIntermediateNode<UK2Node_CallFunction>(this, SourceGraph);
    
    CallFunction->SetFromFunction(Function);
    CallFunction->AllocateDefaultPins();
    
    CompilerContext.MessageLog.NotifyIntermediateObjectCreation(CallFunction, this);

    CompilerContext.MovePinLinksToIntermediate(*GetJsonObjectPin(), *CallFunction->FindPinChecked(FPinName::JsonObject()));
    CompilerContext.MovePinLinksToIntermediate(*GetFieldNamePin (), *CallFunction->FindPinChecked(FPinName::FieldName()));
    CompilerContext.MovePinLinksToIntermediate(*GetFieldValuePin(), *CallFunction->FindPinChecked(FPinName::FieldValue()));

    UEdGraphPin* const NodeExec = GetExecPin();
    UEdGraphPin* const NodeThen = FindPin(UEdGraphSchema_K2::PN_Then);

    UEdGraphPin* const InternalExec = CallFunction->GetExecPin();
    UEdGraphPin* const InternalThen = CallFunction->GetThenPin();

    CompilerContext.MovePinLinksToIntermediate(*NodeExec, *InternalExec);
    CompilerContext.MovePinLinksToIntermediate(*NodeThen, *InternalThen);

	BreakAllNodeLinks();
}

UFunction* UK2Node_AddFieldToJsonObject::GetMatchingFunction(FKismetCompilerContext& CompilerContext)
{
#define IF_PIN(Type) if (PinType.PinCategory == UEdGraphSchema_K2::PC_ ## Type)FunctionName += TEXT( # Type )

    UEdGraphPin* const Pin = GetFieldValuePin();

    const FEdGraphPinType& PinType = Pin->PinType;

    FString FunctionName = TEXT("JO_AddField_");

    if (PinType.IsArray())
    {
        FunctionName += TEXT("Arr_");
    }

    else if (PinType.IsSet() || PinType.IsMap())
    {
        CompilerContext.MessageLog.Error(TEXT("Add Field to JSON Object: Set and Map aren't supported at the moment."));
        return nullptr;
    }

    if (PinType.PinCategory == UEdGraphSchema_K2::PC_Wildcard)
    {
        CompilerContext.MessageLog.Error(TEXT("Add Field to JSON Object: Field Value must be set."));
        return nullptr;
    }

         IF_PIN(Boolean);
    else IF_PIN(Int);
    else IF_PIN(Int64);
    else IF_PIN(String);
    else IF_PIN(Object);
    else IF_PIN(Float);
    else IF_PIN(Name);
    else IF_PIN(Text);
    else IF_PIN(Object);
    else if (PinType.PinCategory == UEdGraphSchema_K2::PC_Struct)
    {
        const FString StructName = PinType.PinSubCategoryObject->GetName();
        if (IsSupportedStructType(StructName))
        {
            FunctionName += StructName;
        }
        else
        {
            CompilerContext.MessageLog.Error(*FString::Printf(TEXT("Add Field to JSON Object: Struct of type %s is not supported."), *StructName));
            return nullptr;
        }
    }
    else
    {
        CompilerContext.MessageLog.Error(*FString::Printf(TEXT("Add Field to JSON Object: Type %s is not supported."), *PinType.PinCategory.ToString()));
        return nullptr;
    }

    return UInternalJsonLibrary::StaticClass()->FindFunctionByName(*FunctionName);

#undef IF_PIN
}

#undef LOCTEXT_NAMESPACE

/*#define LOCTEXT_NAMESPACE "UK2_BlueprintJson_AddFieldToJsonObject"

class FKCHandler_JsonObjectLoop : public FNodeHandlingFunctor
{
public:
    FKCHandler_JsonObjectLoop(FKismetCompilerContext& InCompilerContext)
        : FNodeHandlingFunctor(InCompilerContext)
    {}

    virtual void Compile(FKismetFunctionContext& Context, UEdGraphNode* Node) override
    {
        // Find the valid, connected output pins, and add them to the processing list
        UEdGraphPin* BodyPin      = nullptr;
        UEdGraphPin* CompletedPin = nullptr;

        for (const auto& Pin : Node->Pins)
        {
            if (Pin->GetName() == FPinName::ForBody().ToString())
            {
                BodyPin = Pin;
            }
            else if (Pin->GetName() == FPinName::Completed().ToString())
            {
                CompletedPin = BodyPin;
            }
        }

        if (!BodyPin || !CompletedPin)
        {
            CompilerContext.MessageLog.Error(TEXT("Failed to get pins."));
            return;
        }
        
        if (BodyPin->LinkedTo.Num() < 1)
        {
            // Immediately jump to the end
            FBlueprintCompiledStatement& NextExecutionState = Context.AppendStatementForNode(Node);
            NextExecutionState.Type = KCST_UnconditionalGoto;
            Context.GotoFixupRequestMap.Add(&NextExecutionState, CompletedPin);
        }
        else
        {            
            FBlueprintCompiledStatement& PushExecutionState = Context.AppendStatementForNode(Node);
            PushExecutionState.Type = KCST_PushState;
            Context.GotoFixupRequestMap.Add(&PushExecutionState, CompletedPin);

            // Immediately jump to the first pin
            UEdGraphNode* const NextNode = BodyPin->LinkedTo[0]->GetOwningNode();
            FBlueprintCompiledStatement& NextExecutionState = Context.AppendStatementForNode(Node);
            NextExecutionState.Type = KCST_UnconditionalGoto;
            Context.GotoFixupRequestMap.Add(&NextExecutionState, BodyPin);
        }
    }
};

UEdGraphPin* UK2Node_ForEachFieldJsonObject::GetJsonObjectPin()
{
    return FindPinChecked(FPinName::JsonObject());
}

UEdGraphPin* UK2Node_ForEachFieldJsonObject::GetFieldNamePin() 
{
    return FindPinChecked(FPinName::FieldName());
}

UEdGraphPin* UK2Node_ForEachFieldJsonObject::GetFieldValuePin()
{
    return FindPinChecked(FPinName::FieldValue());
}

UEdGraphPin* UK2Node_ForEachFieldJsonObject::GetLoopBodyPin()
{
    return FindPinChecked(FPinName::ForBody());
}

UEdGraphPin* UK2Node_ForEachFieldJsonObject::GetCompletedPin()
{
    return FindPinChecked(FPinName::Completed());
}

FLinearColor UK2Node_ForEachFieldJsonObject::GetNodeTitleColor() const
{
    return FLinearColor::White;
}

void  UK2Node_ForEachFieldJsonObject::GetMenuActions(FBlueprintActionDatabaseRegistrar& ActionRegistrar) const
{
    Super::GetMenuActions(ActionRegistrar);

    UClass* const Action = GetClass();
    if (ActionRegistrar.IsOpenForRegistration(Action))
    {
        UBlueprintNodeSpawner* const Spawner = UBlueprintNodeSpawner::Create(GetClass());
        check(Spawner != nullptr);
        ActionRegistrar.AddBlueprintAction(Action, Spawner);
    }
}

void  UK2Node_ForEachFieldJsonObject::AllocateDefaultPins()
{
    // Inputs
    CreatePin(EGPD_Input, UEdGraphSchema_K2::PC_Exec, UEdGraphSchema_K2::PN_Execute);
    CreatePin(EGPD_Input, UEdGraphSchema_K2::PC_Object, UJsonObject::StaticClass(), FPinName::JsonObject());

    // Outputs
    CreatePin(EGPD_Output, UEdGraphSchema_K2::PC_Exec,   FPinName::ForBody());
    CreatePin(EGPD_Output, UEdGraphSchema_K2::PC_String, FPinName::FieldName());
    CreatePin(EGPD_Output, UEdGraphSchema_K2::PC_Object, UJsonValue::StaticClass(), FPinName::FieldValue());
    CreatePin(EGPD_Output, UEdGraphSchema_K2::PC_Exec,   FPinName::Completed());

    Super::AllocateDefaultPins();
}

FText UK2Node_ForEachFieldJsonObject::GetMenuCategory() const
{
    return LOCTEXT("UK2_BlueprintJson_ForEachFieldJsonObject_MenuCategory", "JSON");
}

FText UK2Node_ForEachFieldJsonObject::GetNodeTitle(ENodeTitleType::Type TitleType) const
{
    return LOCTEXT("UK2_BlueprintJson_ForEachFieldJsonObject_NodeTitle", "For each JSON Object field");
}

FText UK2Node_ForEachFieldJsonObject::GetTooltipText() const
{
    return LOCTEXT("UK2_BlueprintJson_ForEachFieldJsonObject_Tooltip", "Iterate over all this object's properties.");
}

void  UK2Node_ForEachFieldJsonObject::PinDefaultValueChanged(UEdGraphPin* const Pin)
{
    Super::PinDefaultValueChanged(Pin);
}

void  UK2Node_ForEachFieldJsonObject::NotifyPinConnectionListChanged(UEdGraphPin* Pin)
{
    Super::NotifyPinConnectionListChanged(Pin);
}

void  UK2Node_ForEachFieldJsonObject::ReallocatePinsDuringReconstruction(TArray<UEdGraphPin*>& OldPins)
{
    Super::ReallocatePinsDuringReconstruction(OldPins);
}

FNodeHandlingFunctor* UK2Node_ForEachFieldJsonObject::CreateNodeHandler(FKismetCompilerContext& CompilerContext) const
{
    return new FKCHandler_JsonObjectLoop(CompilerContext);
}


#undef LOCTEXT_NAMEPSACE
*/
