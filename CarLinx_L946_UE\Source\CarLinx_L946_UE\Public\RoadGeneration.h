// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "RoadGeneration.generated.h"

UCLASS()
class CARLINX_L946_UE_API ARoadGeneration : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	ARoadGeneration();

	UFUNCTION(BlueprintCallable)
	static bool InitValue(TArray<FVector> Left, TArray<FVector> Right, TArray<FVector>& Points, TArray<int32>& Triangles);

	UFUNCTION(BlueprintCallable)
	TArray<FVector> Beizer(TArray<FVector> src);

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

};

