#include "../Public/FUDPClientThread.h"

// UE4 Core headers
#include "CoreMinimal.h"
#include "HAL/PlatformFilemanager.h"
#include "HAL/PlatformProcess.h"
#include "HAL/FileManager.h"
#include "HAL/RunnableThread.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "Async/AsyncWork.h"
#include "Async/TaskGraphInterfaces.h"

// UE4 Network headers
#include "Interfaces/IPv4/IPv4Address.h"
#include "Sockets.h"
#include "SocketSubsystem.h"
#include "Common/UdpSocketBuilder.h"

// UE4 Gameplay headers
#include "Kismet/GameplayStatics.h"

// Project headers
#include "../Public/Logger.h"
#include "../Public/ParkingDataManager.h"
#include "../Public/ParkingDataProcessor2UE.h"
#include "../Public/ParkingDataSerializationManager.h"
#include "../Public/GlobalConfig.h"
#include "../Public/ParkingDataProcessManager.h"
#include "../Public/GlobalConfig.h"
#include "../Public/BFL_soLoad.h"

// Standard headers
#include <chrono>
#include <iomanip>
#include <sstream>
#include <string>


#include "Async.h"
#include "SocketSubsystem.h"
#include "Common/UdpSocketBuilder.h"
#include "ParkingDataManager.h"
#include "Logger.h"

// Constants
static constexpr float UDP_SLEEP_INTERVAL = 0.001f;  // 1ms
static constexpr float UDP_SEND_INTERVAL = 0.05f;   // 50ms
static constexpr int32 UDP_RECV_TIMEOUT = 1000;      // 1s
static constexpr int32 BUFFER_SIZE = 1024 * 500;
static constexpr int32 MAX_RETRY_COUNT = 5;


FUDPClientThread::FUDPClientThread(const FString& InIP, int32 InPort)
	: IP(InIP)
	, Port(InPort)
	, Socket(nullptr)
	, bShouldRun(true)
	, Thread(nullptr)
	, LastProcessTime(0.0)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Constructor Begin"), this);

	UBFL_soLoad::LogAllStaticInfo();
	
	// 预分配接收缓冲区
	ReceivedData.SetNumUninitialized(BUFFER_SIZE);
	
	// 创建线程
	Thread = FRunnableThread::Create(this, TEXT("UDPClientThread"));
	if (!Thread)
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to create UDP client thread"));
	}
	
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Constructor End"), this);
}

FUDPClientThread::~FUDPClientThread()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor Begin"), this);
	
	delete Thread;
	Thread = nullptr;
	
	// 关闭socket
	if (Socket)
	{
		Socket->Close();
		ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(Socket);
		Socket = nullptr;
	}
	
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Destructor End"), this);
}

bool FUDPClientThread::Init()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Init Begin"), this);
	
	// 创建socket
	Socket = FUdpSocketBuilder(TEXT("UDPClientSocket"))
		.AsNonBlocking()
		.WithReceiveBufferSize(BUFFER_SIZE)
		.WithSendBufferSize(BUFFER_SIZE)
		.Build();

	if (!Socket)
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to create socket"));
		return false;
	}

	// 解析IP地址
	FIPv4Address IPAddress;
	if (!FIPv4Address::Parse(IP, IPAddress))
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("Failed to parse IP address: %s"), *IP);
		return false;
	}
	
	RemoteEndpoint = FIPv4Endpoint(IPAddress, Port);
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Init End"), this);
	return true;
}

uint32 FUDPClientThread::Run()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Run Begin"), this);
	
	const TCHAR* Message = TEXT("client alive, req all data");
	int32 BytesSent = 0;
	double LastSendTime = FPlatformTime::Seconds();

	while (bShouldRun)
	{
		// 当前时间
		double CurrentTime = FPlatformTime::Seconds();

		// 应用前台时每隔指定时间发一次消息
		if (CurrentTime - LastSendTime >= UDP_SEND_INTERVAL) 
		{
			// 尝试更新统计采样，应用后台时，也更新
			//UBFL_soLoad::UpdateCPUSamples();
			// 尝试打印所有动态信息
			UBFL_soLoad::LogAllDynamicInfo();
			
			// 如果是需要someip数据的前台
			if (UGlobalConfig::GetInstance()->IsInAPA() ||
				UGlobalConfig::GetInstance()->IsInLSDA() ||
				UGlobalConfig::GetInstance()->IsInHPA())
			{
				LastSendTime = CurrentTime;
				
				// 发送心跳消息
				bool bSendSuccess = Socket && Socket->SendTo(
					reinterpret_cast<const uint8*>(TCHAR_TO_UTF8(Message)),
					FCString::Strlen(Message),
					BytesSent,
					*RemoteEndpoint.ToInternetAddr()
				);

				if (!bSendSuccess)
				{
					CARLINX_LOG(LogCarLinx, Warning, TEXT("Failed to send message: %s"),
						ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetSocketError());
						
					// 尝试重连
					if (RetryCount.Increment() > MAX_RETRY_COUNT)
					{
						HandleConnectionError();
					}
				}
				else
				{
					RetryCount.Set(0);
					CARLINX_LOG(LogCarLinx, Log, TEXT("Sent: \"%s\", Bytes: %d, to Server: %s:%d"),
						Message, BytesSent, *RemoteEndpoint.Address.ToString(), RemoteEndpoint.Port);
				}
			}
			else
			{
				// 当前处于后台，每秒提示一次
				double CurrentBackgroundTime = FPlatformTime::Seconds();
				if (CurrentBackgroundTime - LastSendTime >= 1.0)
				{
					CARLINX_LOG(LogCarLinx, Log, TEXT("Currently in background, no SOMEIP data interaction, current state: %s"), *UEnum::GetValueAsString(UGlobalConfig::GetInstance()->GetCurrentState()));
					LastSendTime = CurrentBackgroundTime;
				}
				FPlatformProcess::Sleep(UDP_SLEEP_INTERVAL);
				continue;
			}
		}
		else {
			// 本次循环未到50ms，等待10ms
			FPlatformProcess::Sleep(UDP_SLEEP_INTERVAL);
			continue;
		}

		// 等待接收数据
		bool bHasData = Socket && Socket->Wait(ESocketWaitConditions::WaitForRead,
			FTimespan::FromMilliseconds(UDP_RECV_TIMEOUT));

		if (bHasData)
		{
			uint32 PendingDataSize = 0;
			if (Socket->HasPendingData(PendingDataSize))
			{
				// 确保缓冲区足够大
				if (PendingDataSize > static_cast<uint32>(ReceivedData.Num()))
				{
					ReceivedData.SetNumUninitialized(PendingDataSize);
				}

				int32 BytesRead = 0;
				if (Socket->RecvFrom(ReceivedData.GetData(), PendingDataSize,
					BytesRead, *Sender.ToInternetAddr()))
				{
					if (BytesRead > 0)
					{
						// 处理接收到的数据
						std::vector<uint8_t> DataCopy(
							ReceivedData.GetData(),
							ReceivedData.GetData() + BytesRead
						);
						ProcessReceivedData(DataCopy);
						
						// 更新性能计数器
						ProcessedFrameCount.Increment();
						double CurrentProcessTime = FPlatformTime::Seconds();
						if (CurrentProcessTime - LastProcessTime >= 1.0)
						{
							CARLINX_LOG(LogCarLinx, Log,
								TEXT("Processed %d frames in the last second"),
								ProcessedFrameCount.GetValue());
							ProcessedFrameCount.Set(0);
							LastProcessTime = CurrentProcessTime;
						}
					}
				}
				else
				{
					CARLINX_LOG(LogCarLinx, Warning,
						TEXT("Failed to receive data: %s"),
						ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetSocketError());
				}
			}
		} else {
			CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] UDP receive timeout: %s"), this,
				ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->GetSocketError());
		}
	}

	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Run End"), this);
	return 0;
}

void FUDPClientThread::Stop()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Stop Begin"), this);
	bShouldRun = false;
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Stop End"), this);
}

void FUDPClientThread::Exit()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Exit Begin"), this);
	if (Socket)
	{
		Socket->Close();
		ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(Socket);
		Socket = nullptr;
	}
	CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Exit End"), this);
}

void FUDPClientThread::InitMap()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("Enter"));
	CARLINX_LOG(LogCarLinx, Log, TEXT("Exit"));
}

void FUDPClientThread::mapKeyAdd(TMap<FString, FString>& inputMap, FString Key)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("Enter"));
	FString value = FString::FromInt(FCString::Atoi(*inputMap[Key] + 1));
	inputMap.Add(Key, FString::Printf(TEXT("%d"), 0));
	CARLINX_LOG(LogCarLinx, Log, TEXT("Exit"));
}

void FUDPClientThread::ProcessReceivedData(const std::vector<uint8_t>& data)
{
	FDateTime StartTime = FDateTime::Now();
	CARLINX_LOG(LogCarLinx, Log, TEXT("Enter >>>>>"));

	// 检查UGlobalConfig是否有效
	UGlobalConfig* GlobalConfig = UGlobalConfig::GetInstance();
	if (!GlobalConfig)
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("GlobalConfig is null"));
		return;
	}

	bool CurrentIsBackground = GlobalConfig->IsInBackground();

	if (CurrentIsBackground)
	{
		// 如果当前状态为后台，则不处理数据
		return;
	}

	// 检查数据处理管理器是否有效
	UParkingDataProcessManager* ProcessManager = UParkingDataProcessManager::GetInstance();
	if (!ProcessManager)
	{
		CARLINX_LOG(LogCarLinx, Error, TEXT("ProcessManager is null"));
		return;
	}

	// 使用数据处理管理器处理数据
	ProcessManager->ProcessReceivedData(data);
	
	if (GlobalConfig->GetEnableDataRecording())
	{
		// 只在特定状态下且不在后台时记录数据 APA LSDA HPA 
		if (GlobalConfig->GetCurrentState() >= EParkingState::APA)
		{
			UParkingDataSerializationManager* SerializationManager = UParkingDataSerializationManager::GetInstance();
			if (SerializationManager)
			{
				SerializationManager->RecordFrame(data.data(), data.size());
			}
			else
			{
				CARLINX_LOG(LogCarLinx, Error, TEXT("SerializationManager is null"));
			}
		}
	}

	FDateTime EndTime = FDateTime::Now();
	FTimespan TimeDiff = EndTime - StartTime;
	CARLINX_LOG(LogCarLinx, Log, TEXT("Exit <<<<<< Time elapsed: %d ms"), TimeDiff.GetTotalMilliseconds());
}
//void FUDPClientThread::ProcessReceivedData(const std::vector<uint8_t>& data)
//{
//	
//
//	FDateTime StartTime = FDateTime::Now();
//	CARLINX_LOG(LogCarLinx, Log, TEXT("Enter >>>>>"));
//
//	 数据有效性校验
//	if (data.empty() || data.size() > MAX_PACKET_SIZE)
//	{
//		CARLINX_LOG(LogCarLinx, Warning, TEXT("Invalid data size: %llu"), data.size());
//		return;
//	}
//
//	static UGlobalConfig* GlobalConfig = nullptr;
//	static std::once_flag InitFlag;
//	
//	std::call_once(InitFlag, [&]() {GlobalConfig = UGlobalConfig::GetInstance();});
//	CARLINX_LOG(LogCarLinx, Log, TEXT("UGlobalConfig check"));
//	 检查UGlobalConfig是否有效
//
//	if (!GlobalConfig)
//	{
//		CARLINX_LOG(LogCarLinx, Error, TEXT("GlobalConfig initialization failed!"));
//		return;
//	}
//
//	const bool bIsBackground = GlobalConfig->IsInBackground();
//	if (bIsBackground)
//	{
//		 如果当前状态为后台，则不处理数据
//		CARLINX_LOG(LogCarLinx, Verbose, TEXT("Background mode skip processing"));
//		return;
//	}
//
//
//		 检查数据处理管理器是否有效
//		UParkingDataProcessManager* ProcessManager = UParkingDataProcessManager::GetInstance();
//		if (!ProcessManager)
//		{
//			CARLINX_LOG(LogCarLinx, Error, TEXT("ProcessManager is null"));
//			return;
//		}
//	
//		 使用数据处理管理器处理数据
//		ProcessManager->ProcessReceivedData(data);
//		CARLINX_LOG(LogCarLinx, Log, TEXT("ProcessManager check"));
//		if (GlobalConfig->GetEnableDataRecording())
//		{
//			 只在特定状态下且不在后台时记录数据 APA LSDA HPA 
//			if (GlobalConfig->GetCurrentState() >= EParkingState::APA)
//			{
//				UParkingDataSerializationManager* SerializationManager = UParkingDataSerializationManager::GetInstance();
//				if (SerializationManager)
//				{
//					SerializationManager->RecordFrame(data.data(), data.size());
//				}
//				else
//				{
//					CARLINX_LOG(LogCarLinx, Error, TEXT("SerializationManager is null"));
//				}
//			}
//		}
//
//
//	FDateTime EndTime = FDateTime::Now();
//	FTimespan TimeDiff = EndTime - StartTime;
//	CARLINX_LOG(LogCarLinx, Log, TEXT("Exit <<<<<< Time elapsed: %d ms"), TimeDiff.GetTotalMilliseconds());
//	
//}

void FUDPClientThread::HandleConnectionError()
{
    CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Connection error, attempting to reconnect..."), this);
    
    if (Socket)
    {
        Socket->Close();
        ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(Socket);
        Socket = nullptr;
    }

    if (!TryReconnect())
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to reconnect, stopping thread..."), this);
        Stop();
    }
}

bool FUDPClientThread::TryReconnect()
{
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Attempting to reconnect..."), this);
    
    // 创建新的socket
    Socket = FUdpSocketBuilder(TEXT("UDPClientSocket"))
        .AsNonBlocking()
        .WithReceiveBufferSize(BUFFER_SIZE)
        .WithSendBufferSize(BUFFER_SIZE)
        .Build();

    if (!Socket)
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to create new socket"), this);
        return false;
    }

    // 解析IP地址
    FIPv4Address IPAddress;
    if (!FIPv4Address::Parse(IP, IPAddress))
    {
        CARLINX_LOG(LogCarLinx, Error, TEXT("[0x%p] Failed to parse IP address: %s"), this, *IP);
        return false;
    }

    RemoteEndpoint = FIPv4Endpoint(IPAddress, Port);
    CARLINX_LOG(LogCarLinx, Log, TEXT("[0x%p] Reconnected successfully"), this);
    return true;
}

bool FUDPClientThread::UpdateMapValue(TMap<FString, FString>& Map, const FString& Key, const FString& Value)
{
	const FString* OldValue = Map.Find(Key);
	if (!OldValue || *OldValue != Value)
	{
		Map.Add(Key, Value);
		return true;
	}
	return false;
}


// FUDPClientThreadManager implementation
FUDPClientThreadManager& FUDPClientThreadManager::Get()
{
	static FUDPClientThreadManager Instance;
	return Instance;
}

void FUDPClientThreadManager::StartThread(const FString& IP, int32 Port)
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadManager::StartThread() Enter"));
	if (!ClientThread)
	{
		ClientThread = new FUDPClientThread(IP, Port);
	}
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadManager::StartThread() Exit"));
}

void FUDPClientThreadManager::StopThread()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadManager::StopThread() Enter"));
	if (ClientThread)
	{
		ClientThread->Stop();
		delete ClientThread;
		ClientThread = nullptr;
	}
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadManager::StopThread() Exit"));
}



FUDPClientThreadManager::FUDPClientThreadManager() : ClientThread(nullptr) 
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadManager::FUDPClientThreadManager() Enter"));
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadManager::FUDPClientThreadManager() Exit"));
}

FUDPClientThreadManager::~FUDPClientThreadManager()
{
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadManager::~FUDPClientThreadManager() Enter"));
	StopThread();
	CARLINX_LOG(LogCarLinx, Log, TEXT("FUDPClientThreadManager::~FUDPClientThreadManager() Exit"));
}