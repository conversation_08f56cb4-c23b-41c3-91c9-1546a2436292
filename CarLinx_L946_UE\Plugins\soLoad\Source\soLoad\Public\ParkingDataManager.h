#ifndef PARKING_DATA_MANAGER_H
#define PARKING_DATA_MANAGER_H

#include <array>
#include <bitset>
#include <vector>

#if 0
#include "VehValtPrkgHmiGenr_types.h"
#else
#include "VehValtPrkgHmiGenr_minimal_types.h"
#endif

class ParkingDataManager {
public:
    static ParkingDataManager& getInstance() {
        static ParkingDataManager instance;
        return instance;
    }

    // 禁用拷贝构造和赋值操作
    ParkingDataManager(const ParkingDataManager&) = delete;
    ParkingDataManager& operator=(const ParkingDataManager&) = delete;

    // 自车数据
    void updatePrkgSelfST(const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& data);
    const gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp& getPrkgSelfST() const;

    // 车位数据
    void updatePrkgSlotArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot, 32>& data);
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot, 32>& getPrkgSlotArr() const;

    // 障碍物数据
    void updatePrkgObstacleArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle, 60>& data);
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle, 60>& getPrkgObstacleArr() const;

    // 交通标志数据
    void updatePrkgTrafficsignArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgTrafficsign, 10>& data);
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgTrafficsign, 10>& getPrkgTrafficsignArr() const;

    // 道路右侧点数据
    void updatePrkgRoadRightPointArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& data);
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& getPrkgRoadRightPointArr() const;

    // 道路左侧点数据
    void updatePrkgRoadLeftPointArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& data);
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint, 100>& getPrkgRoadLeftPointArr() const;

    // 左侧轨迹规划点数据
    void updatePrkgTtrackPlanningLArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& data);
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& getPrkgTtrackPlanningLArr() const;

    // 右侧轨迹规划点数据
    void updatePrkgTtrackPlanningRArr(const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& data);
    const std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning, 270>& getPrkgTtrackPlanningRArr() const;

    // 视角模式数据
    void updatePerspectiveMode(uint8_t mode);
    uint8_t getPerspectiveMode() const;

    // 序列化函数
    std::vector<uint8_t> serialize() const;
    
    // 反序列化函数
    bool deserialize(const std::vector<uint8_t>& data);

    // 检查是否有数据更新
    bool hasUpdates() const;
    
    // 检查特定数据是否更新
    bool isUpdated(size_t dataIndex) const;

    // 数据更新标记枚举，使用位掩码
    enum DataFlag : uint32_t {
        FLAG_NONE                = 0,         // 0x00000000
        FLAG_SELF_ST             = 1 << 0,    // 0x00000001
        FLAG_SLOT_ARR            = 1 << 1,    // 0x00000002
        FLAG_OBSTACLE_ARR        = 1 << 2,    // 0x00000004
        FLAG_TRAFFICSIGN_ARR     = 1 << 3,    // 0x00000008
        FLAG_ROAD_RIGHT_POINT_ARR = 1 << 4,   // 0x00000010
        FLAG_ROAD_LEFT_POINT_ARR  = 1 << 5,   // 0x00000020
        FLAG_TTRACK_PLANNING_L_ARR = 1 << 6,  // 0x00000040
        FLAG_TTRACK_PLANNING_R_ARR = 1 << 7,  // 0x00000080
        FLAG_PERSPECTIVE_MODE     = 1 << 8,   // 0x00000100
        FLAG_ALL                 = 0xFFFFFFFF,
        FLAG_DEFAULT             = FLAG_SELF_ST | FLAG_SLOT_ARR | FLAG_OBSTACLE_ARR  // 默认关注的标志位组合
    };

    // 设置指定的标志位
    void setFlag(DataFlag flag);
    
    // 清除指定的标志位
    void clearFlag(DataFlag flag);
    
    // 清除所有标志位
    void clearAllFlags();
    
    // 检查指定的标志位是否设置
    bool isFlagSet(DataFlag flag) const;
    
    // 获取当前所有标志位
    uint32_t getFlags() const;

    // 重置为默认标志位
    void resetToDefaultFlags();
    
    // 检查是否包含默认标志位
    bool hasDefaultFlags() const;

private:
    ParkingDataManager() = default;

#pragma pack(push, 4) // 修改为4字节对齐

    // 使用uint32_t替代bitset存储标志位
    uint32_t m_updateFlags{FLAG_DEFAULT};

    // 以下是数据段
    gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSelfvehtyp                        m_prkgSelfST;
    std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgSlot,          32>     m_prkgSlotArr;
    std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgObstacle,      60>     m_prkgObstacleArr;
    std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgTrafficsign,   10>     m_prkgTrafficsignArr;
    std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint,     100>    m_prkgRoadRightPointArr;
    std::array<gossoa::ap::services::VehValtPrkgHmiGenr::PrkgRoadPoint,     100>    m_prkgRoadLeftPointArr;
    std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning,    270>    m_prkgTtrackPlanningLArr;
    std::array<gossoa::ap::services::VehValtPrkgHmiGenr::TtrackPlanning,    270>    m_prkgTtrackPlanningRArr;
    uint8_t m_perspectiveMode{0};
 #pragma pack(pop)
};

#endif // PARKING_DATA_MANAGER_H 